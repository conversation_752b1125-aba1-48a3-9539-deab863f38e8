time="2025-07-31 09:42:33" level=info msg="Redis DSN not configured, falling back to in-memory store."
time="2025-07-31 09:42:33" level=info msg="Starting as Master Node."
time="2025-07-31 09:42:33" level=info msg="Database auto-migration completed."
time="2025-07-31 09:42:33" level=info msg="Initialized system setting: app_url = http://localhost:3001"
time="2025-07-31 09:42:33" level=info msg="Initialized system setting: request_log_retention_days = 7"
time="2025-07-31 09:42:33" level=info msg="Initialized system setting: request_log_write_interval_minutes = 1"
time="2025-07-31 09:42:33" level=info msg="Initialized system setting: proxy_keys = sk-123456"
time="2025-07-31 09:42:33" level=info msg="Initialized system setting: request_timeout = 600"
time="2025-07-31 09:42:33" level=info msg="Initialized system setting: connect_timeout = 15"
time="2025-07-31 09:42:33" level=info msg="Initialized system setting: idle_conn_timeout = 120"
time="2025-07-31 09:42:33" level=info msg="Initialized system setting: response_header_timeout = 600"
time="2025-07-31 09:42:33" level=info msg="Initialized system setting: max_idle_conns = 100"
time="2025-07-31 09:42:33" level=info msg="Initialized system setting: max_idle_conns_per_host = 50"
time="2025-07-31 09:42:33" level=info msg="Initialized system setting: max_retries = 3"
time="2025-07-31 09:42:33" level=info msg="Initialized system setting: blacklist_threshold = 3"
time="2025-07-31 09:42:33" level=info msg="Initialized system setting: key_validation_interval_minutes = 60"
time="2025-07-31 09:42:33" level=info msg="Initialized system setting: key_validation_concurrency = 10"
time="2025-07-31 09:42:33" level=info msg="Initialized system setting: key_validation_timeout_seconds = 20"
time="2025-07-31 09:42:33" level=info msg="System settings initialized in DB."
time="2025-07-31 09:42:33" level=info
time="2025-07-31 09:42:33" level=info msg="========= System Settings ========="
time="2025-07-31 09:42:33" level=info msg="  --- Basic Settings ---"
time="2025-07-31 09:42:33" level=info msg="    App URL: http://localhost:3001"
time="2025-07-31 09:42:33" level=info msg="    Request Log Retention: 7 days"
time="2025-07-31 09:42:33" level=info msg="    Request Log Write Interval: 1 minutes"
time="2025-07-31 09:42:33" level=info msg="  --- Request Behavior ---"
time="2025-07-31 09:42:33" level=info msg="    Request Timeout: 600 seconds"
time="2025-07-31 09:42:33" level=info msg="    Connect Timeout: 15 seconds"
time="2025-07-31 09:42:33" level=info msg="    Response Header Timeout: 600 seconds"
time="2025-07-31 09:42:33" level=info msg="    Idle Connection Timeout: 120 seconds"
time="2025-07-31 09:42:33" level=info msg="    Max Idle Connections: 100"
time="2025-07-31 09:42:33" level=info msg="    Max Idle Connections Per Host: 50"
time="2025-07-31 09:42:33" level=info msg="  --- Key & Group Behavior ---"
time="2025-07-31 09:42:33" level=info msg="    Max Retries: 3"
time="2025-07-31 09:42:33" level=info msg="    Blacklist Threshold: 3"
time="2025-07-31 09:42:33" level=info msg="    Key Validation Interval: 60 minutes"
time="2025-07-31 09:42:33" level=info msg="===================================="
time="2025-07-31 09:42:33" level=info
time="2025-07-31 09:42:33" level=info msg="cache reloaded successfully" syncer=system_settings
time="2025-07-31 09:42:33" level=info msg="Updating active key lists for all groups..."
time="2025-07-31 09:42:33" level=info
time="2025-07-31 09:42:33" level=info msg="======= Server Configuration ======="
time="2025-07-31 09:42:33" level=info msg="  --- Server ---"
time="2025-07-31 09:42:33" level=info msg="    Listen Address: 0.0.0.0:3001"
time="2025-07-31 09:42:33" level=info msg="    Graceful Shutdown Timeout: 10 seconds"
time="2025-07-31 09:42:33" level=info msg="    Read Timeout: 60 seconds"
time="2025-07-31 09:42:33" level=info msg="    Write Timeout: 600 seconds"
time="2025-07-31 09:42:33" level=info msg="    Idle Timeout: 120 seconds"
time="2025-07-31 09:42:33" level=info msg="  --- Performance ---"
time="2025-07-31 09:42:33" level=info msg="    Max Concurrent Requests: 100"
time="2025-07-31 09:42:33" level=info msg="  --- Security ---"
time="2025-07-31 09:42:33" level=info msg="    Authentication: enabled (key loaded)"
time="2025-07-31 09:42:33" level=info msg="    CORS: enabled (Origins: *)"
time="2025-07-31 09:42:33" level=info msg="  --- Logging ---"
time="2025-07-31 09:42:33" level=info msg="    Log Level: info"
time="2025-07-31 09:42:33" level=info msg="    Log Format: text"
time="2025-07-31 09:42:33" level=info msg="    File Logging: true"
time="2025-07-31 09:42:33" level=info msg="    Log File Path: ./data/logs/app.log"
time="2025-07-31 09:42:33" level=info msg="  --- Dependencies ---"
time="2025-07-31 09:42:33" level=info msg="    Database: configured"
time="2025-07-31 09:42:33" level=info msg="    Redis: not configured"
time="2025-07-31 09:42:33" level=info msg="===================================="
time="2025-07-31 09:42:33" level=info
time="2025-07-31 09:42:33" level=info msg="cache reloaded successfully" syncer=groups
time="2025-07-31 09:42:33" level=info msg="GPT-Load proxy server started successfully on Version: v1.0.17"
time="2025-07-31 09:42:33" level=info msg="Server address: http://0.0.0.0:3001"
time="2025-07-31 09:42:33" level=info
time="2025-07-31 09:44:23" level=info msg="Shutting down server..."
time="2025-07-31 09:44:23" level=info msg="HTTP server has been shut down."
time="2025-07-31 09:44:23" level=info msg="cache syncer stopped." syncer=groups
time="2025-07-31 09:44:23" level=info msg="RequestLogService stopped gracefully."
time="2025-07-31 09:44:23" level=info msg="LogCleanupService stopped gracefully."
time="2025-07-31 09:44:23" level=info msg="CronChecker stopped gracefully."
time="2025-07-31 09:44:23" level=info msg="cache syncer stopped." syncer=system_settings
time="2025-07-31 09:44:23" level=info msg="All background services stopped."
time="2025-07-31 09:44:23" level=info msg="Server exited gracefully"
time="2025-07-31 09:44:24" level=info msg="Redis DSN not configured, falling back to in-memory store."
time="2025-07-31 09:44:24" level=info msg="Starting as Master Node."
time="2025-07-31 09:44:24" level=info msg="Database auto-migration completed."
time="2025-07-31 09:44:24" level=info msg="System settings initialized in DB."
time="2025-07-31 09:44:24" level=info
time="2025-07-31 09:44:24" level=info msg="========= System Settings ========="
time="2025-07-31 09:44:24" level=info msg="  --- Basic Settings ---"
time="2025-07-31 09:44:24" level=info msg="    App URL: http://localhost:3001"
time="2025-07-31 09:44:24" level=info msg="    Request Log Retention: 7 days"
time="2025-07-31 09:44:24" level=info msg="    Request Log Write Interval: 1 minutes"
time="2025-07-31 09:44:24" level=info msg="  --- Request Behavior ---"
time="2025-07-31 09:44:24" level=info msg="    Request Timeout: 600 seconds"
time="2025-07-31 09:44:24" level=info msg="    Connect Timeout: 15 seconds"
time="2025-07-31 09:44:24" level=info msg="    Response Header Timeout: 600 seconds"
time="2025-07-31 09:44:24" level=info msg="    Idle Connection Timeout: 120 seconds"
time="2025-07-31 09:44:24" level=info msg="    Max Idle Connections: 100"
time="2025-07-31 09:44:24" level=info msg="    Max Idle Connections Per Host: 50"
time="2025-07-31 09:44:24" level=info msg="  --- Key & Group Behavior ---"
time="2025-07-31 09:44:24" level=info msg="    Max Retries: 3"
time="2025-07-31 09:44:24" level=info msg="    Blacklist Threshold: 3"
time="2025-07-31 09:44:24" level=info msg="    Key Validation Interval: 60 minutes"
time="2025-07-31 09:44:24" level=info msg="===================================="
time="2025-07-31 09:44:24" level=info
time="2025-07-31 09:44:24" level=info msg="cache reloaded successfully" syncer=system_settings
time="2025-07-31 09:44:24" level=info msg="Updating active key lists for all groups..."
time="2025-07-31 09:44:24" level=info
time="2025-07-31 09:44:24" level=info msg="======= Server Configuration ======="
time="2025-07-31 09:44:24" level=info msg="  --- Server ---"
time="2025-07-31 09:44:24" level=info msg="    Listen Address: 0.0.0.0:3001"
time="2025-07-31 09:44:24" level=info msg="    Graceful Shutdown Timeout: 10 seconds"
time="2025-07-31 09:44:24" level=info msg="    Read Timeout: 60 seconds"
time="2025-07-31 09:44:24" level=info msg="    Write Timeout: 600 seconds"
time="2025-07-31 09:44:24" level=info msg="    Idle Timeout: 120 seconds"
time="2025-07-31 09:44:24" level=info msg="  --- Performance ---"
time="2025-07-31 09:44:24" level=info msg="    Max Concurrent Requests: 100"
time="2025-07-31 09:44:24" level=info msg="  --- Security ---"
time="2025-07-31 09:44:24" level=info msg="    Authentication: enabled (key loaded)"
time="2025-07-31 09:44:24" level=info msg="    CORS: enabled (Origins: *)"
time="2025-07-31 09:44:24" level=info msg="  --- Logging ---"
time="2025-07-31 09:44:24" level=info msg="    Log Level: info"
time="2025-07-31 09:44:24" level=info msg="    Log Format: text"
time="2025-07-31 09:44:24" level=info msg="    File Logging: true"
time="2025-07-31 09:44:24" level=info msg="    Log File Path: ./data/logs/app.log"
time="2025-07-31 09:44:24" level=info msg="  --- Dependencies ---"
time="2025-07-31 09:44:24" level=info msg="    Database: configured"
time="2025-07-31 09:44:24" level=info msg="    Redis: not configured"
time="2025-07-31 09:44:24" level=info msg="===================================="
time="2025-07-31 09:44:24" level=info
time="2025-07-31 09:44:24" level=info msg="cache reloaded successfully" syncer=groups
time="2025-07-31 09:44:24" level=info msg="GPT-Load proxy server started successfully on Version: v1.0.17"
time="2025-07-31 09:44:24" level=info msg="Server address: http://0.0.0.0:3001"
time="2025-07-31 09:44:24" level=info
time="2025-07-31 09:44:49" level=info msg="GET /wordpress/ - 200 - 306.733µs"
time="2025-07-31 09:45:18" level=info msg="GET / - 200 - 502.305µs"
time="2025-07-31 09:45:19" level=info msg="GET /assets/index-Gswog8ov.css - 200 - 3.017732ms"
time="2025-07-31 09:45:19" level=info msg="GET /assets/index-SPLiOoPZ.js - 200 - 69.48716ms"
time="2025-07-31 09:45:21" level=info msg="GET /assets/Login-DzpmRpZS.css - 200 - 388.849µs"
time="2025-07-31 09:45:21" level=info msg="GET /assets/Login-C7BGdm6I.js - 200 - 475.053µs"
time="2025-07-31 09:45:21" level=info msg="GET /assets/logo-BV-_qOqO.png - 200 - 1.47681ms"
time="2025-07-31 09:45:23" level=warning msg="POST /api/auth/login - 401 - 428.515µs"
time="2025-07-31 09:45:56" level=info msg="Shutting down server..."
time="2025-07-31 09:45:56" level=info msg="HTTP server has been shut down."
time="2025-07-31 09:45:56" level=info msg="cache syncer stopped." syncer=groups
time="2025-07-31 09:45:56" level=info msg="cache syncer stopped." syncer=system_settings
time="2025-07-31 09:45:56" level=info msg="RequestLogService stopped gracefully."
time="2025-07-31 09:45:56" level=info msg="LogCleanupService stopped gracefully."
time="2025-07-31 09:45:56" level=info msg="CronChecker stopped gracefully."
time="2025-07-31 09:45:56" level=info msg="All background services stopped."
time="2025-07-31 09:45:56" level=info msg="Server exited gracefully"
time="2025-07-31 09:45:57" level=info msg="Redis DSN not configured, falling back to in-memory store."
time="2025-07-31 09:45:57" level=info msg="Starting as Master Node."
time="2025-07-31 09:45:57" level=info msg="Database auto-migration completed."
time="2025-07-31 09:45:57" level=info msg="System settings initialized in DB."
time="2025-07-31 09:45:57" level=info
time="2025-07-31 09:45:57" level=info msg="========= System Settings ========="
time="2025-07-31 09:45:57" level=info msg="  --- Basic Settings ---"
time="2025-07-31 09:45:57" level=info msg="    App URL: http://localhost:3001"
time="2025-07-31 09:45:57" level=info msg="    Request Log Retention: 7 days"
time="2025-07-31 09:45:57" level=info msg="    Request Log Write Interval: 1 minutes"
time="2025-07-31 09:45:57" level=info msg="  --- Request Behavior ---"
time="2025-07-31 09:45:57" level=info msg="    Request Timeout: 600 seconds"
time="2025-07-31 09:45:57" level=info msg="    Connect Timeout: 15 seconds"
time="2025-07-31 09:45:57" level=info msg="    Response Header Timeout: 600 seconds"
time="2025-07-31 09:45:57" level=info msg="    Idle Connection Timeout: 120 seconds"
time="2025-07-31 09:45:57" level=info msg="    Max Idle Connections: 100"
time="2025-07-31 09:45:57" level=info msg="    Max Idle Connections Per Host: 50"
time="2025-07-31 09:45:57" level=info msg="  --- Key & Group Behavior ---"
time="2025-07-31 09:45:57" level=info msg="    Max Retries: 3"
time="2025-07-31 09:45:57" level=info msg="    Blacklist Threshold: 3"
time="2025-07-31 09:45:57" level=info msg="    Key Validation Interval: 60 minutes"
time="2025-07-31 09:45:57" level=info msg="===================================="
time="2025-07-31 09:45:57" level=info
time="2025-07-31 09:45:57" level=info msg="cache reloaded successfully" syncer=system_settings
time="2025-07-31 09:45:57" level=info msg="Updating active key lists for all groups..."
time="2025-07-31 09:45:57" level=info
time="2025-07-31 09:45:57" level=info msg="======= Server Configuration ======="
time="2025-07-31 09:45:57" level=info msg="  --- Server ---"
time="2025-07-31 09:45:57" level=info msg="    Listen Address: 0.0.0.0:3001"
time="2025-07-31 09:45:57" level=info msg="    Graceful Shutdown Timeout: 10 seconds"
time="2025-07-31 09:45:57" level=info msg="    Read Timeout: 60 seconds"
time="2025-07-31 09:45:57" level=info msg="    Write Timeout: 600 seconds"
time="2025-07-31 09:45:57" level=info msg="    Idle Timeout: 120 seconds"
time="2025-07-31 09:45:57" level=info msg="  --- Performance ---"
time="2025-07-31 09:45:57" level=info msg="    Max Concurrent Requests: 100"
time="2025-07-31 09:45:57" level=info msg="  --- Security ---"
time="2025-07-31 09:45:57" level=info msg="    Authentication: enabled (key loaded)"
time="2025-07-31 09:45:57" level=info msg="    CORS: enabled (Origins: *)"
time="2025-07-31 09:45:57" level=info msg="  --- Logging ---"
time="2025-07-31 09:45:57" level=info msg="    Log Level: info"
time="2025-07-31 09:45:57" level=info msg="    Log Format: text"
time="2025-07-31 09:45:57" level=info msg="    File Logging: true"
time="2025-07-31 09:45:57" level=info msg="    Log File Path: ./data/logs/app.log"
time="2025-07-31 09:45:57" level=info msg="  --- Dependencies ---"
time="2025-07-31 09:45:57" level=info msg="    Database: configured"
time="2025-07-31 09:45:57" level=info msg="    Redis: not configured"
time="2025-07-31 09:45:57" level=info msg="===================================="
time="2025-07-31 09:45:57" level=info
time="2025-07-31 09:45:57" level=info msg="cache reloaded successfully" syncer=groups
time="2025-07-31 09:45:57" level=info msg="GPT-Load proxy server started successfully on Version: v1.0.17"
time="2025-07-31 09:45:57" level=info msg="Server address: http://0.0.0.0:3001"
time="2025-07-31 09:45:57" level=info
time="2025-07-31 09:46:01" level=info msg="GET /login - 200 - 546.169µs"
time="2025-07-31 09:46:04" level=warning msg="POST /api/auth/login - 401 - 210.16µs"
time="2025-07-31 09:46:16" level=warning msg="POST /api/auth/login - 401 - 98.797µs"
time="2025-07-31 09:47:17" level=info msg="GET / - 200 - 374.623µs"
time="2025-07-31 09:47:17" level=info msg="GET /assets/index-Gswog8ov.css - 200 - 2.169628ms"
time="2025-07-31 09:47:18" level=info msg="GET /assets/index-SPLiOoPZ.js - 200 - 62.411015ms"
time="2025-07-31 09:47:18" level=info msg="GET /assets/Login-DzpmRpZS.css - 200 - 233.544µs"
time="2025-07-31 09:47:18" level=info msg="GET /assets/logo-BV-_qOqO.png - 200 - 902.878µs"
time="2025-07-31 09:47:18" level=info msg="GET /assets/Login-C7BGdm6I.js - 200 - 388.83µs"
time="2025-07-31 09:47:19" level=info msg="GET /assets/logo-BV-_qOqO.png - 200 - 1.342604ms"
time="2025-07-31 09:47:20" level=info msg="Shutting down server..."
time="2025-07-31 09:47:20" level=info msg="HTTP server has been shut down."
time="2025-07-31 09:47:20" level=info msg="cache syncer stopped." syncer=groups
time="2025-07-31 09:47:20" level=info msg="cache syncer stopped." syncer=system_settings
time="2025-07-31 09:47:20" level=info msg="CronChecker stopped gracefully."
time="2025-07-31 09:47:20" level=info msg="LogCleanupService stopped gracefully."
time="2025-07-31 09:47:20" level=info msg="RequestLogService stopped gracefully."
time="2025-07-31 09:47:20" level=info msg="All background services stopped."
time="2025-07-31 09:47:20" level=info msg="Server exited gracefully"
time="2025-07-31 09:47:20" level=info msg="Redis DSN not configured, falling back to in-memory store."
time="2025-07-31 09:47:20" level=info msg="Starting as Master Node."
time="2025-07-31 09:47:20" level=info msg="Database auto-migration completed."
time="2025-07-31 09:47:20" level=info msg="System settings initialized in DB."
time="2025-07-31 09:47:20" level=info
time="2025-07-31 09:47:20" level=info msg="========= System Settings ========="
time="2025-07-31 09:47:20" level=info msg="  --- Basic Settings ---"
time="2025-07-31 09:47:20" level=info msg="    App URL: http://localhost:3001"
time="2025-07-31 09:47:20" level=info msg="    Request Log Retention: 7 days"
time="2025-07-31 09:47:20" level=info msg="    Request Log Write Interval: 1 minutes"
time="2025-07-31 09:47:20" level=info msg="  --- Request Behavior ---"
time="2025-07-31 09:47:20" level=info msg="    Request Timeout: 600 seconds"
time="2025-07-31 09:47:20" level=info msg="    Connect Timeout: 15 seconds"
time="2025-07-31 09:47:20" level=info msg="    Response Header Timeout: 600 seconds"
time="2025-07-31 09:47:20" level=info msg="    Idle Connection Timeout: 120 seconds"
time="2025-07-31 09:47:20" level=info msg="    Max Idle Connections: 100"
time="2025-07-31 09:47:20" level=info msg="    Max Idle Connections Per Host: 50"
time="2025-07-31 09:47:20" level=info msg="  --- Key & Group Behavior ---"
time="2025-07-31 09:47:20" level=info msg="    Max Retries: 3"
time="2025-07-31 09:47:20" level=info msg="    Blacklist Threshold: 3"
time="2025-07-31 09:47:20" level=info msg="    Key Validation Interval: 60 minutes"
time="2025-07-31 09:47:20" level=info msg="===================================="
time="2025-07-31 09:47:20" level=info
time="2025-07-31 09:47:20" level=info msg="cache reloaded successfully" syncer=system_settings
time="2025-07-31 09:47:20" level=info msg="Updating active key lists for all groups..."
time="2025-07-31 09:47:20" level=info
time="2025-07-31 09:47:20" level=info msg="======= Server Configuration ======="
time="2025-07-31 09:47:20" level=info msg="  --- Server ---"
time="2025-07-31 09:47:20" level=info msg="    Listen Address: 0.0.0.0:3001"
time="2025-07-31 09:47:20" level=info msg="    Graceful Shutdown Timeout: 10 seconds"
time="2025-07-31 09:47:20" level=info msg="    Read Timeout: 60 seconds"
time="2025-07-31 09:47:20" level=info msg="    Write Timeout: 600 seconds"
time="2025-07-31 09:47:20" level=info msg="    Idle Timeout: 120 seconds"
time="2025-07-31 09:47:20" level=info msg="  --- Performance ---"
time="2025-07-31 09:47:20" level=info msg="    Max Concurrent Requests: 100"
time="2025-07-31 09:47:20" level=info msg="  --- Security ---"
time="2025-07-31 09:47:20" level=info msg="    Authentication: enabled (key loaded)"
time="2025-07-31 09:47:20" level=info msg="    CORS: enabled (Origins: *)"
time="2025-07-31 09:47:20" level=info msg="  --- Logging ---"
time="2025-07-31 09:47:20" level=info msg="    Log Level: info"
time="2025-07-31 09:47:20" level=info msg="    Log Format: text"
time="2025-07-31 09:47:20" level=info msg="    File Logging: true"
time="2025-07-31 09:47:20" level=info msg="    Log File Path: ./data/logs/app.log"
time="2025-07-31 09:47:20" level=info msg="  --- Dependencies ---"
time="2025-07-31 09:47:20" level=info msg="    Database: configured"
time="2025-07-31 09:47:20" level=info msg="    Redis: not configured"
time="2025-07-31 09:47:20" level=info msg="===================================="
time="2025-07-31 09:47:20" level=info
time="2025-07-31 09:47:20" level=info msg="cache reloaded successfully" syncer=groups
time="2025-07-31 09:47:20" level=info msg="GPT-Load proxy server started successfully on Version: v1.0.17"
time="2025-07-31 09:47:20" level=info msg="Server address: http://0.0.0.0:3001"
time="2025-07-31 09:47:20" level=info
time="2025-07-31 09:47:22" level=info msg="GET / - 200 - 635.39µs"
time="2025-07-31 09:47:23" level=info msg="GET /assets/index-Gswog8ov.css - 200 - 1.146722ms"
time="2025-07-31 09:47:23" level=info msg="GET /login - 200 - 852.863µs"
time="2025-07-31 09:47:23" level=info msg="GET /assets/index-SPLiOoPZ.js - 200 - 67.13886ms"
time="2025-07-31 09:47:24" level=info msg="GET /assets/Login-DzpmRpZS.css - 200 - 246.268µs"
time="2025-07-31 09:47:24" level=info msg="GET /assets/Login-C7BGdm6I.js - 200 - 261.619µs"
time="2025-07-31 09:47:24" level=info msg="GET /assets/logo-BV-_qOqO.png - 200 - 853.634µs"
time="2025-07-31 09:47:25" level=info msg="GET /assets/logo-BV-_qOqO.png - 200 - 1.053905ms"
time="2025-07-31 09:47:25" level=warning msg="POST /api/auth/login - 401 - 439.316µs"
time="2025-07-31 09:47:40" level=info msg="Shutting down server..."
time="2025-07-31 09:47:40" level=info msg="HTTP server has been shut down."
time="2025-07-31 09:47:40" level=info msg="LogCleanupService stopped gracefully."
time="2025-07-31 09:47:40" level=info msg="cache syncer stopped." syncer=system_settings
time="2025-07-31 09:47:40" level=info msg="RequestLogService stopped gracefully."
time="2025-07-31 09:47:40" level=info msg="CronChecker stopped gracefully."
time="2025-07-31 09:47:40" level=info msg="cache syncer stopped." syncer=groups
time="2025-07-31 09:47:40" level=info msg="All background services stopped."
time="2025-07-31 09:47:40" level=info msg="Server exited gracefully"
time="2025-07-31 09:47:41" level=info msg="Redis DSN not configured, falling back to in-memory store."
time="2025-07-31 09:47:41" level=info msg="Starting as Master Node."
time="2025-07-31 09:47:41" level=info msg="Database auto-migration completed."
time="2025-07-31 09:47:41" level=info msg="System settings initialized in DB."
time="2025-07-31 09:47:41" level=info
time="2025-07-31 09:47:41" level=info msg="========= System Settings ========="
time="2025-07-31 09:47:41" level=info msg="  --- Basic Settings ---"
time="2025-07-31 09:47:41" level=info msg="    App URL: http://localhost:3001"
time="2025-07-31 09:47:41" level=info msg="    Request Log Retention: 7 days"
time="2025-07-31 09:47:41" level=info msg="    Request Log Write Interval: 1 minutes"
time="2025-07-31 09:47:41" level=info msg="  --- Request Behavior ---"
time="2025-07-31 09:47:41" level=info msg="    Request Timeout: 600 seconds"
time="2025-07-31 09:47:41" level=info msg="    Connect Timeout: 15 seconds"
time="2025-07-31 09:47:41" level=info msg="    Response Header Timeout: 600 seconds"
time="2025-07-31 09:47:41" level=info msg="    Idle Connection Timeout: 120 seconds"
time="2025-07-31 09:47:41" level=info msg="    Max Idle Connections: 100"
time="2025-07-31 09:47:41" level=info msg="    Max Idle Connections Per Host: 50"
time="2025-07-31 09:47:41" level=info msg="  --- Key & Group Behavior ---"
time="2025-07-31 09:47:41" level=info msg="    Max Retries: 3"
time="2025-07-31 09:47:41" level=info msg="    Blacklist Threshold: 3"
time="2025-07-31 09:47:41" level=info msg="    Key Validation Interval: 60 minutes"
time="2025-07-31 09:47:41" level=info msg="===================================="
time="2025-07-31 09:47:41" level=info
time="2025-07-31 09:47:41" level=info msg="cache reloaded successfully" syncer=system_settings
time="2025-07-31 09:47:41" level=info msg="Updating active key lists for all groups..."
time="2025-07-31 09:47:41" level=info
time="2025-07-31 09:47:41" level=info msg="======= Server Configuration ======="
time="2025-07-31 09:47:41" level=info msg="  --- Server ---"
time="2025-07-31 09:47:41" level=info msg="    Listen Address: 0.0.0.0:3001"
time="2025-07-31 09:47:41" level=info msg="    Graceful Shutdown Timeout: 10 seconds"
time="2025-07-31 09:47:41" level=info msg="    Read Timeout: 60 seconds"
time="2025-07-31 09:47:41" level=info msg="    Write Timeout: 600 seconds"
time="2025-07-31 09:47:41" level=info msg="    Idle Timeout: 120 seconds"
time="2025-07-31 09:47:41" level=info msg="  --- Performance ---"
time="2025-07-31 09:47:41" level=info msg="    Max Concurrent Requests: 100"
time="2025-07-31 09:47:41" level=info msg="  --- Security ---"
time="2025-07-31 09:47:41" level=info msg="    Authentication: enabled (key loaded)"
time="2025-07-31 09:47:41" level=info msg="    CORS: enabled (Origins: *)"
time="2025-07-31 09:47:41" level=info msg="  --- Logging ---"
time="2025-07-31 09:47:41" level=info msg="    Log Level: info"
time="2025-07-31 09:47:41" level=info msg="    Log Format: text"
time="2025-07-31 09:47:41" level=info msg="    File Logging: true"
time="2025-07-31 09:47:41" level=info msg="    Log File Path: ./data/logs/app.log"
time="2025-07-31 09:47:41" level=info msg="  --- Dependencies ---"
time="2025-07-31 09:47:41" level=info msg="    Database: configured"
time="2025-07-31 09:47:41" level=info msg="    Redis: not configured"
time="2025-07-31 09:47:41" level=info msg="===================================="
time="2025-07-31 09:47:41" level=info
time="2025-07-31 09:47:41" level=info msg="cache reloaded successfully" syncer=groups
time="2025-07-31 09:47:41" level=info msg="GPT-Load proxy server started successfully on Version: v1.0.17"
time="2025-07-31 09:47:41" level=info msg="Server address: http://0.0.0.0:3001"
time="2025-07-31 09:47:41" level=info
time="2025-07-31 09:48:56" level=info msg="Redis DSN not configured, falling back to in-memory store."
time="2025-07-31 09:48:56" level=info msg="Starting as Master Node."
time="2025-07-31 09:48:56" level=info msg="Database auto-migration completed."
time="2025-07-31 09:48:56" level=info msg="System settings initialized in DB."
time="2025-07-31 09:48:56" level=info
time="2025-07-31 09:48:56" level=info msg="========= System Settings ========="
time="2025-07-31 09:48:56" level=info msg="  --- Basic Settings ---"
time="2025-07-31 09:48:56" level=info msg="    App URL: http://localhost:3001"
time="2025-07-31 09:48:56" level=info msg="    Request Log Retention: 7 days"
time="2025-07-31 09:48:56" level=info msg="    Request Log Write Interval: 1 minutes"
time="2025-07-31 09:48:56" level=info msg="  --- Request Behavior ---"
time="2025-07-31 09:48:56" level=info msg="    Request Timeout: 600 seconds"
time="2025-07-31 09:48:56" level=info msg="    Connect Timeout: 15 seconds"
time="2025-07-31 09:48:56" level=info msg="    Response Header Timeout: 600 seconds"
time="2025-07-31 09:48:56" level=info msg="    Idle Connection Timeout: 120 seconds"
time="2025-07-31 09:48:56" level=info msg="    Max Idle Connections: 100"
time="2025-07-31 09:48:56" level=info msg="    Max Idle Connections Per Host: 50"
time="2025-07-31 09:48:56" level=info msg="  --- Key & Group Behavior ---"
time="2025-07-31 09:48:56" level=info msg="    Max Retries: 3"
time="2025-07-31 09:48:56" level=info msg="    Blacklist Threshold: 3"
time="2025-07-31 09:48:56" level=info msg="    Key Validation Interval: 60 minutes"
time="2025-07-31 09:48:56" level=info msg="===================================="
time="2025-07-31 09:48:56" level=info
time="2025-07-31 09:48:56" level=info msg="cache reloaded successfully" syncer=system_settings
time="2025-07-31 09:48:56" level=info msg="Updating active key lists for all groups..."
time="2025-07-31 09:48:56" level=info
time="2025-07-31 09:48:56" level=info msg="======= Server Configuration ======="
time="2025-07-31 09:48:56" level=info msg="  --- Server ---"
time="2025-07-31 09:48:56" level=info msg="    Listen Address: 0.0.0.0:3001"
time="2025-07-31 09:48:56" level=info msg="    Graceful Shutdown Timeout: 10 seconds"
time="2025-07-31 09:48:56" level=info msg="    Read Timeout: 60 seconds"
time="2025-07-31 09:48:56" level=info msg="    Write Timeout: 600 seconds"
time="2025-07-31 09:48:56" level=info msg="    Idle Timeout: 120 seconds"
time="2025-07-31 09:48:56" level=info msg="  --- Performance ---"
time="2025-07-31 09:48:56" level=info msg="    Max Concurrent Requests: 100"
time="2025-07-31 09:48:56" level=info msg="  --- Security ---"
time="2025-07-31 09:48:56" level=info msg="    Authentication: enabled (key loaded)"
time="2025-07-31 09:48:56" level=info msg="    CORS: enabled (Origins: *)"
time="2025-07-31 09:48:56" level=info msg="  --- Logging ---"
time="2025-07-31 09:48:56" level=info msg="    Log Level: info"
time="2025-07-31 09:48:56" level=info msg="    Log Format: text"
time="2025-07-31 09:48:56" level=info msg="    File Logging: true"
time="2025-07-31 09:48:56" level=info msg="    Log File Path: ./data/logs/app.log"
time="2025-07-31 09:48:56" level=info msg="  --- Dependencies ---"
time="2025-07-31 09:48:56" level=info msg="    Database: configured"
time="2025-07-31 09:48:56" level=info msg="    Redis: not configured"
time="2025-07-31 09:48:56" level=info msg="===================================="
time="2025-07-31 09:48:56" level=info
time="2025-07-31 09:48:56" level=info msg="cache reloaded successfully" syncer=groups
time="2025-07-31 09:48:56" level=info msg="GPT-Load proxy server started successfully on Version: v1.0.17"
time="2025-07-31 09:48:56" level=info msg="Server address: http://0.0.0.0:3001"
time="2025-07-31 09:48:56" level=info
time="2025-07-31 09:49:03" level=info msg="GET /login - 200 - 288.208µs"
time="2025-07-31 09:49:05" level=info msg="POST /api/auth/login - 200 - 276.847µs"
time="2025-07-31 09:49:06" level=info msg="GET /assets/display-DuIJKIJ5.js - 200 - 832.754µs"
time="2025-07-31 09:49:06" level=info msg="GET /assets/Dashboard-ZqIXRWdq.js - 200 - 890.845µs"
time="2025-07-31 09:49:06" level=info msg="GET /assets/Dashboard-DHOcOEH2.css - 200 - 461.578µs"
time="2025-07-31 09:49:06" level=info msg="GET /api/tasks/status - 200 - 74.101µs"
time="2025-07-31 09:49:06" level=info msg="GET /api/groups/list - 200 - 953.393µs"
time="2025-07-31 09:49:06" level=info msg="GET /api/dashboard/chart - 200 - 1.693673ms"
time="2025-07-31 09:49:06" level=info msg="GET /api/dashboard/stats - 200 - 2.400066ms"
time="2025-07-31 09:49:08" level=info msg="GET /assets/ProxyKeysInput-qloCXNPS.js - 200 - 425.058µs"
time="2025-07-31 09:49:08" level=info msg="GET /assets/ProxyKeysInput-CoLmi6p2.css - 200 - 299.961µs"
time="2025-07-31 09:49:08" level=info msg="GET /assets/Settings-bVufNCH9.js - 200 - 572.47µs"
time="2025-07-31 09:49:09" level=info msg="GET /api/settings - 200 - 830.641µs"
time="2025-07-31 09:50:12" level=info msg="GET /assets/Keys-CCps4UaT.css - 200 - 784.824µs"
time="2025-07-31 09:50:12" level=info msg="GET /assets/Search-oHPKo0PA.js - 200 - 304.128µs"
time="2025-07-31 09:50:12" level=info msg="GET /assets/Keys-Cvs7q-Qm.js - 200 - 2.176111ms"
time="2025-07-31 09:50:13" level=info msg="GET /api/groups/config-options - 200 - 275.384µs"
time="2025-07-31 09:50:13" level=info msg="GET /api/groups - 200 - 520.87µs"
time="2025-07-31 09:51:43" level=info msg="GET /api/channel-types - 200 - 101.933µs"
time="2025-07-31 09:51:43" level=info msg="GET /api/groups/config-options - 200 - 150.507µs"
time="2025-07-31 09:52:41" level=info msg="POST /api/groups - 200 - 5.306466ms"
time="2025-07-31 09:52:41" level=info msg="cache reloaded successfully" syncer=groups
time="2025-07-31 09:52:42" level=info msg="GET /api/groups - 200 - 750.428µs"
time="2025-07-31 09:52:42" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 1.573754ms"
time="2025-07-31 09:52:42" level=info msg="GET /api/groups/1/stats - 200 - 2.934843ms"
time="2025-07-31 09:53:13" level=info msg="POST /api/keys/add-async - 200 - 936.963µs"
time="2025-07-31 09:53:13" level=info msg="GET /api/tasks/status - 200 - 128.825µs"
time="2025-07-31 09:53:14" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 939.708µs"
time="2025-07-31 09:53:14" level=info msg="GET /api/groups/1/stats - 200 - 1.153005ms"
time="2025-07-31 09:53:18" level=info msg="POST /api/keys/test-multiple - 200 - 532.801465ms"
time="2025-07-31 09:53:18" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 1.398702ms"
time="2025-07-31 09:53:18" level=info msg="GET /api/groups/1/stats - 200 - 1.026302ms"
time="2025-07-31 09:53:56" level=info msg="CronChecker: Group 'kimi-k2' has no invalid keys to check."
time="2025-07-31 09:54:47" level=info msg="GET /api/groups/config-options - 200 - 1.206205ms"
time="2025-07-31 09:54:47" level=info msg="GET /api/channel-types - 200 - 76.736µs"
time="2025-07-31 09:55:09" level=info msg="PUT /api/groups/1 - 200 - 7.82547ms"
time="2025-07-31 09:55:09" level=info msg="cache reloaded successfully" syncer=groups
time="2025-07-31 09:55:10" level=info msg="GET /api/groups - 200 - 576.753µs"
time="2025-07-31 09:55:10" level=info msg="GET /api/groups/1/stats - 200 - 1.274206ms"
time="2025-07-31 09:55:10" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 1.068794ms"
time="2025-07-31 09:55:12" level=info msg="POST /api/keys/test-multiple - 200 - 132.334182ms"
time="2025-07-31 09:55:13" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 957.531µs"
time="2025-07-31 09:55:13" level=info msg="GET /api/groups/1/stats - 200 - 1.009008ms"
time="2025-07-31 09:56:01" level=info msg="PUT /api/groups/1 - 200 - 5.022538ms"
time="2025-07-31 09:56:01" level=info msg="cache reloaded successfully" syncer=groups
time="2025-07-31 09:56:01" level=info msg="GET /api/groups - 200 - 696.06µs"
time="2025-07-31 09:56:02" level=info msg="GET /api/groups/1/stats - 200 - 1.149807ms"
time="2025-07-31 09:56:02" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 912.042µs"
time="2025-07-31 09:56:03" level=info msg="POST /api/keys/test-multiple - 200 - 206.399107ms"
time="2025-07-31 09:56:03" level=warning msg="Key has reached blacklist threshold, disabling." keyID=1 threshold=3
time="2025-07-31 09:56:04" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 940.006µs"
time="2025-07-31 09:56:04" level=info msg="GET /api/groups/1/stats - 200 - 1.338218ms"
time="2025-07-31 09:56:07" level=info msg="POST /api/keys/restore-multiple - 200 - 4.078894ms"
time="2025-07-31 09:56:08" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 908.926µs"
time="2025-07-31 09:56:08" level=info msg="GET /api/groups/1/stats - 200 - 1.195384ms"
time="2025-07-31 09:56:10" level=info msg="POST /api/keys/test-multiple - 200 - 230.063686ms"
time="2025-07-31 09:56:11" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 916.211µs"
time="2025-07-31 09:56:11" level=info msg="GET /api/groups/1/stats - 200 - 957.829µs"
time="2025-07-31 09:57:00" level=info msg="GET / - 200 - 967.728µs"
time="2025-07-31 09:57:00" level=info msg="GET / - 200 - 608.281µs"
time="2025-07-31 09:57:01" level=info msg="PUT /api/groups/1 - 200 - 5.598724ms"
time="2025-07-31 09:57:01" level=info msg="cache reloaded successfully" syncer=groups
time="2025-07-31 09:57:02" level=info msg="GET /api/groups - 200 - 638.329µs"
time="2025-07-31 09:57:02" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 1.170045ms"
time="2025-07-31 09:57:02" level=info msg="GET /api/groups/1/stats - 200 - 1.713332ms"
time="2025-07-31 09:57:04" level=info msg="POST /api/keys/test-multiple - 200 - 249.302547ms"
time="2025-07-31 09:57:04" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 843.15µs"
time="2025-07-31 09:57:05" level=info msg="GET /api/groups/1/stats - 200 - 860.264µs"
time="2025-07-31 09:57:46" level=info msg="PUT /api/groups/1 - 200 - 4.243343ms"
time="2025-07-31 09:57:46" level=info msg="cache reloaded successfully" syncer=groups
time="2025-07-31 09:57:46" level=info msg="GET /api/groups - 200 - 707.803µs"
time="2025-07-31 09:57:47" level=info msg="GET /api/groups/1/stats - 200 - 995.391µs"
time="2025-07-31 09:57:47" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 1.129537ms"
time="2025-07-31 09:57:50" level=info msg="POST /api/keys/test-multiple - 200 - 2.001965546s"
time="2025-07-31 09:57:51" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 2.1678ms"
time="2025-07-31 09:57:51" level=info msg="GET /api/groups/1/stats - 200 - 1.354708ms"
time="2025-07-31 09:57:57" level=info msg="POST /api/keys/validate-group - 200 - 813.162µs"
time="2025-07-31 09:57:57" level=info msg="Starting manual validation for group kimi-k2"
time="2025-07-31 09:57:57" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=3
time="2025-07-31 09:57:57" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=7
time="2025-07-31 09:57:58" level=info msg="GET /api/tasks/status - 200 - 263.312µs"
time="2025-07-31 09:57:59" level=info msg="GET /api/tasks/status - 200 - 129.989µs"
time="2025-07-31 09:57:59" level=info msg="Manual validation finished for group kimi-k2: {TotalKeys:10 ValidKeys:5 InvalidKeys:5}"
time="2025-07-31 09:58:00" level=info msg="GET /api/tasks/status - 200 - 129.788µs"
time="2025-07-31 09:58:01" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 879.89µs"
time="2025-07-31 09:58:01" level=info msg="GET /api/groups/1/stats - 200 - 1.345459ms"
time="2025-07-31 09:58:54" level=info msg="POST /api/keys/validate-group - 200 - 847.176µs"
time="2025-07-31 09:58:54" level=info msg="Starting manual validation for group kimi-k2"
time="2025-07-31 09:58:54" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=9
time="2025-07-31 09:58:54" level=info msg="GET /api/tasks/status - 200 - 93.739µs"
time="2025-07-31 09:58:55" level=info msg="Manual validation finished for group kimi-k2: {TotalKeys:10 ValidKeys:4 InvalidKeys:6}"
time="2025-07-31 09:58:56" level=info msg="GET /api/tasks/status - 200 - 199.8µs"
time="2025-07-31 09:58:56" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 973.498µs"
time="2025-07-31 09:58:56" level=info msg="GET /api/groups/1/stats - 200 - 1.134105ms"
time="2025-07-31 09:59:06" level=info msg="GET /api/settings - 200 - 269.635µs"
time="2025-07-31 10:00:03" level=info
time="2025-07-31 10:00:03" level=info msg="========= System Settings ========="
time="2025-07-31 10:00:03" level=info msg="  --- Basic Settings ---"
time="2025-07-31 10:00:03" level=info msg="    App URL: https://load.ainima.de"
time="2025-07-31 10:00:03" level=info msg="    Request Log Retention: 7 days"
time="2025-07-31 10:00:03" level=info msg="    Request Log Write Interval: 1 minutes"
time="2025-07-31 10:00:03" level=info msg="  --- Request Behavior ---"
time="2025-07-31 10:00:03" level=info msg="    Request Timeout: 600 seconds"
time="2025-07-31 10:00:03" level=info msg="    Connect Timeout: 15 seconds"
time="2025-07-31 10:00:03" level=info msg="    Response Header Timeout: 600 seconds"
time="2025-07-31 10:00:03" level=info msg="    Idle Connection Timeout: 120 seconds"
time="2025-07-31 10:00:03" level=info msg="    Max Idle Connections: 100"
time="2025-07-31 10:00:03" level=info msg="    Max Idle Connections Per Host: 50"
time="2025-07-31 10:00:03" level=info msg="  --- Key & Group Behavior ---"
time="2025-07-31 10:00:03" level=info msg="    Max Retries: 3"
time="2025-07-31 10:00:03" level=info msg="    Blacklist Threshold: 3"
time="2025-07-31 10:00:03" level=info msg="    Key Validation Interval: 60 minutes"
time="2025-07-31 10:00:03" level=info msg="===================================="
time="2025-07-31 10:00:03" level=info
time="2025-07-31 10:00:03" level=info msg="cache reloaded successfully" syncer=system_settings
time="2025-07-31 10:00:03" level=info msg="cache reloaded successfully" syncer=groups
time="2025-07-31 10:00:03" level=info msg="PUT /api/settings - 200 - 106.700047ms"
time="2025-07-31 10:00:03" level=info msg="GET /api/settings - 200 - 236.212µs"
time="2025-07-31 10:00:08" level=info msg="GET /api/dashboard/stats - 200 - 1.088797ms"
time="2025-07-31 10:00:08" level=info msg="GET /api/dashboard/chart - 200 - 354.447µs"
time="2025-07-31 10:00:08" level=info msg="GET /api/groups/list - 200 - 285.035µs"
time="2025-07-31 10:00:11" level=info msg="GET /api/groups/config-options - 200 - 296.304µs"
time="2025-07-31 10:00:11" level=info msg="GET /api/groups - 200 - 565.059µs"
time="2025-07-31 10:00:11" level=info msg="GET /api/groups/1/stats - 200 - 1.401424ms"
time="2025-07-31 10:00:11" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 558.084µs"
time="2025-07-31 10:00:15" level=info msg="POST /api/keys/test-multiple - 200 - 204.708687ms"
time="2025-07-31 10:00:15" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 1.009506ms"
time="2025-07-31 10:00:16" level=info msg="GET /api/groups/1/stats - 200 - 1.724671ms"
time="2025-07-31 10:00:19" level=info msg="POST /api/keys/test-multiple - 200 - 1.041100445s"
time="2025-07-31 10:00:19" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 910.658µs"
time="2025-07-31 10:00:20" level=info msg="GET /api/groups/1/stats - 200 - 2.02824ms"
time="2025-07-31 10:00:59" level=info msg="GET /api/channel-types - 200 - 51.067µs"
time="2025-07-31 10:00:59" level=info msg="GET /api/groups/config-options - 200 - 163.733µs"
time="2025-07-31 10:02:58" level=info msg="GET /api/settings - 200 - 221.873µs"
time="2025-07-31 10:03:02" level=info msg="GET /api/groups/config-options - 200 - 180.364µs"
time="2025-07-31 10:03:02" level=info msg="GET /api/groups - 200 - 1.006881ms"
time="2025-07-31 10:03:02" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 1.076173ms"
time="2025-07-31 10:03:02" level=info msg="GET /api/groups/1/stats - 200 - 1.116538ms"
time="2025-07-31 10:03:06" level=info msg="GET /api/channel-types - 200 - 120.209µs"
time="2025-07-31 10:03:06" level=info msg="GET /api/groups/config-options - 200 - 148.684µs"
time="2025-07-31 10:03:26" level=info msg="PUT /api/groups/1 - 200 - 4.928378ms"
time="2025-07-31 10:03:26" level=info msg="cache reloaded successfully" syncer=groups
time="2025-07-31 10:03:26" level=info msg="GET /api/groups - 200 - 569.516µs"
time="2025-07-31 10:03:27" level=info msg="GET /api/groups/1/stats - 200 - 1.266475ms"
time="2025-07-31 10:03:27" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 1.198806ms"
time="2025-07-31 10:03:34" level=info msg="POST /api/keys/restore-all-invalid - 200 - 886.461µs"
time="2025-07-31 10:03:34" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 834.982µs"
time="2025-07-31 10:03:34" level=info msg="GET /api/groups/1/stats - 200 - 944.781µs"
time="2025-07-31 10:03:36" level=info msg="POST /api/keys/validate-group - 200 - 743.007µs"
time="2025-07-31 10:03:36" level=info msg="Starting manual validation for group kimi-k2"
time="2025-07-31 10:03:36" level=info msg="GET /api/tasks/status - 200 - 181.107µs"
time="2025-07-31 10:03:37" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=4
time="2025-07-31 10:03:37" level=warning msg="Key has reached blacklist threshold, disabling." keyID=8 threshold=3
time="2025-07-31 10:03:38" level=info msg="GET /api/tasks/status - 200 - 128.034µs"
time="2025-07-31 10:03:39" level=info msg="Manual validation finished for group kimi-k2: {TotalKeys:10 ValidKeys:3 InvalidKeys:7}"
time="2025-07-31 10:03:39" level=info msg="GET /api/tasks/status - 200 - 133.043µs"
time="2025-07-31 10:03:40" level=info msg="GET /api/groups/1/stats - 200 - 1.198855ms"
time="2025-07-31 10:03:40" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 907.521µs"
time="2025-07-31 10:03:48" level=info msg="POST /api/keys/validate-group - 200 - 1.309196ms"
time="2025-07-31 10:03:48" level=info msg="Starting manual validation for group kimi-k2"
time="2025-07-31 10:03:48" level=warning msg="Key has reached blacklist threshold, disabling." keyID=1 threshold=3
time="2025-07-31 10:03:48" level=warning msg="Key has reached blacklist threshold, disabling." keyID=6 threshold=3
time="2025-07-31 10:03:48" level=warning msg="Key has reached blacklist threshold, disabling." keyID=7 threshold=3
time="2025-07-31 10:03:48" level=info msg="GET /api/tasks/status - 200 - 131.161µs"
time="2025-07-31 10:03:48" level=info msg="Manual validation finished for group kimi-k2: {TotalKeys:10 ValidKeys:3 InvalidKeys:7}"
time="2025-07-31 10:03:49" level=info msg="GET /api/tasks/status - 200 - 137.543µs"
time="2025-07-31 10:03:50" level=info msg="GET /api/groups/1/stats - 200 - 1.24841ms"
time="2025-07-31 10:03:50" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 1.016609ms"
time="2025-07-31 10:03:53" level=info msg="POST /api/keys/validate-group - 200 - 1.034122ms"
time="2025-07-31 10:03:53" level=info msg="Starting manual validation for group kimi-k2"
time="2025-07-31 10:03:54" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=2
time="2025-07-31 10:03:54" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=4
time="2025-07-31 10:03:54" level=warning msg="Key has reached blacklist threshold, disabling." keyID=10 threshold=3
time="2025-07-31 10:03:54" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=5
time="2025-07-31 10:03:54" level=info msg="GET /api/tasks/status - 200 - 188.379µs"
time="2025-07-31 10:03:54" level=info msg="Manual validation finished for group kimi-k2: {TotalKeys:10 ValidKeys:2 InvalidKeys:8}"
time="2025-07-31 10:03:55" level=info msg="GET /api/tasks/status - 200 - 212.336µs"
time="2025-07-31 10:03:56" level=info msg="GET /api/groups/1/stats - 200 - 1.370113ms"
time="2025-07-31 10:03:56" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 887.842µs"
time="2025-07-31 10:03:58" level=info msg="POST /api/keys/validate-group - 200 - 1.094787ms"
time="2025-07-31 10:03:58" level=info msg="Starting manual validation for group kimi-k2"
time="2025-07-31 10:03:58" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=5
time="2025-07-31 10:03:58" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=3
time="2025-07-31 10:03:58" level=error msg="Failed to handle key failure" error="failed to update key stats in DB: database is locked (5) (SQLITE_BUSY)" keyID=2
time="2025-07-31 10:03:59" level=info msg="GET /api/tasks/status - 200 - 108.998µs"
time="2025-07-31 10:03:59" level=info msg="Manual validation finished for group kimi-k2: {TotalKeys:10 ValidKeys:2 InvalidKeys:8}"
time="2025-07-31 10:04:00" level=info msg="GET /api/tasks/status - 200 - 153.152µs"
time="2025-07-31 10:04:00" level=info msg="GET /api/groups/1/stats - 200 - 1.538945ms"
time="2025-07-31 10:04:00" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 1.586705ms"
time="2025-07-31 10:04:03" level=info msg="POST /api/keys/validate-group - 200 - 930.714µs"
time="2025-07-31 10:04:03" level=info msg="Starting manual validation for group kimi-k2"
time="2025-07-31 10:04:04" level=info msg="GET /api/tasks/status - 200 - 108.277µs"
time="2025-07-31 10:04:04" level=info msg="Manual validation finished for group kimi-k2: {TotalKeys:10 ValidKeys:6 InvalidKeys:4}"
time="2025-07-31 10:04:05" level=info msg="GET /api/tasks/status - 200 - 164.534µs"
time="2025-07-31 10:04:05" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 987.082µs"
time="2025-07-31 10:04:05" level=info msg="GET /api/groups/1/stats - 200 - 1.039933ms"
time="2025-07-31 10:04:13" level=info msg="POST /api/keys/restore-all-invalid - 200 - 881.771µs"
time="2025-07-31 10:04:14" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 969.788µs"
time="2025-07-31 10:04:14" level=info msg="GET /api/groups/1/stats - 200 - 1.11701ms"
time="2025-07-31 10:04:37" level=info msg="GET /api/settings - 200 - 166.507µs"
time="2025-07-31 10:05:15" level=info msg="GET /api/groups/list - 200 - 370.676µs"
time="2025-07-31 10:05:15" level=info msg="GET /api/dashboard/chart - 200 - 248.593µs"
time="2025-07-31 10:05:15" level=info msg="GET /api/dashboard/stats - 200 - 1.26444ms"
time="2025-07-31 10:06:04" level=info msg="GET /api/groups/config-options - 200 - 467.051µs"
time="2025-07-31 10:06:04" level=info msg="GET /api/groups - 200 - 961.813µs"
time="2025-07-31 10:06:04" level=info msg="GET /api/groups/1/stats - 200 - 1.026576ms"
time="2025-07-31 10:06:04" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 861.743µs"
time="2025-07-31 10:06:38" level=info msg="GET /api/groups/config-options - 200 - 189.912µs"
time="2025-07-31 10:06:38" level=info msg="GET /api/channel-types - 200 - 93.439µs"
time="2025-07-31 10:06:54" level=info msg="PUT /api/groups/1 - 200 - 3.889743ms"
time="2025-07-31 10:06:54" level=info msg="cache reloaded successfully" syncer=groups
time="2025-07-31 10:06:54" level=info msg="GET /api/groups - 200 - 619.46µs"
time="2025-07-31 10:06:55" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 1.005727ms"
time="2025-07-31 10:06:55" level=info msg="GET /api/groups/1/stats - 200 - 1.281572ms"
time="2025-07-31 10:08:46" level=info msg="GET / - 200 - 945.04µs"
time="2025-07-31 10:08:46" level=info msg="GET / - 200 - 1.540225ms"
time="2025-07-31 10:08:56" level=info msg="GET / - 200 - 180.994µs"
time="2025-07-31 10:09:02" level=info msg="GET /api/settings - 200 - 161.358µs"
time="2025-07-31 10:09:10" level=info msg="GET /assets/index-SPLiOoPZ.js - 200 - 54.013661ms"
time="2025-07-31 10:11:05" level=info msg="GET /api/groups/config-options - 200 - 279.503µs"
time="2025-07-31 10:11:05" level=info msg="GET /api/groups - 200 - 516.815µs"
time="2025-07-31 10:11:05" level=info msg="GET /api/groups/1/stats - 200 - 2.28856ms"
time="2025-07-31 10:11:05" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 1.456535ms"
time="2025-07-31 10:11:08" level=info msg="GET /api/groups/config-options - 200 - 214.539µs"
time="2025-07-31 10:11:08" level=info msg="GET /api/channel-types - 200 - 64.182µs"
time="2025-07-31 10:11:19" level=warning msg="Key has reached blacklist threshold, disabling." keyID=3 threshold=3
time="2025-07-31 10:11:19" level=warning msg="Key has reached blacklist threshold, disabling." keyID=4 threshold=3
time="2025-07-31 10:11:19" level=warning msg="POST /proxy/kimi-k2/v1/chat/completions - 400 - 1.215400863s"
time="2025-07-31 10:11:19" level=warning msg="Key has reached blacklist threshold, disabling." keyID=5 threshold=3
time="2025-07-31 10:11:56" level=info msg="Successfully flushed 1 request logs."
time="2025-07-31 10:12:20" level=warning msg="POST /proxy/kimi-k2/v1/chat/completions - 400 - 1.808362094s"
time="2025-07-31 10:12:21" level=warning msg="POST /proxy/kimi-k2/v1/chat/completions - 400 - 833.037521ms"
time="2025-07-31 10:12:22" level=warning msg="Key has reached blacklist threshold, disabling." keyID=7 threshold=3
time="2025-07-31 10:12:22" level=warning msg="POST /proxy/kimi-k2/v1/chat/completions - 400 - 667.021377ms"
time="2025-07-31 10:12:23" level=warning msg="Key has reached blacklist threshold, disabling." keyID=2 threshold=3
time="2025-07-31 10:12:23" level=warning msg="Key has reached blacklist threshold, disabling." keyID=9 threshold=3
time="2025-07-31 10:12:23" level=warning msg="POST /proxy/kimi-k2/v1/chat/completions - 400 - 727.739314ms"
time="2025-07-31 10:12:23" level=warning msg="Key has reached blacklist threshold, disabling." keyID=8 threshold=3
time="2025-07-31 10:12:56" level=info msg="Successfully flushed 4 request logs."
time="2025-07-31 10:14:21" level=info msg="PUT /api/groups/1 - 200 - 4.006132ms"
time="2025-07-31 10:14:21" level=info msg="cache reloaded successfully" syncer=groups
time="2025-07-31 10:14:21" level=info msg="GET /api/groups - 200 - 702.017µs"
time="2025-07-31 10:14:21" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 729.249µs"
time="2025-07-31 10:14:21" level=info msg="GET /api/groups/1/stats - 200 - 786.569µs"
time="2025-07-31 10:14:26" level=info msg="POST /api/keys/restore-all-invalid - 200 - 5.58597ms"
time="2025-07-31 10:14:27" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 797.74µs"
time="2025-07-31 10:14:27" level=info msg="GET /api/groups/1/stats - 200 - 1.074377ms"
time="2025-07-31 10:14:31" level=info msg="POST /api/keys/test-multiple - 200 - 743.216001ms"
time="2025-07-31 10:14:31" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 1.030904ms"
time="2025-07-31 10:14:31" level=info msg="GET /api/groups/1/stats - 200 - 1.267265ms"
time="2025-07-31 10:14:37" level=info msg="POST /api/keys/test-multiple - 200 - 462.548177ms"
time="2025-07-31 10:14:37" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 826.454µs"
time="2025-07-31 10:14:37" level=info msg="GET /api/groups/1/stats - 200 - 1.262425ms"
time="2025-07-31 10:15:16" level=info msg="PUT /api/groups/1 - 200 - 5.343388ms"
time="2025-07-31 10:15:16" level=info msg="cache reloaded successfully" syncer=groups
time="2025-07-31 10:15:16" level=info msg="GET /api/groups - 200 - 725.732µs"
time="2025-07-31 10:15:16" level=info msg="GET /api/groups/1/stats - 200 - 1.209905ms"
time="2025-07-31 10:15:16" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 1.327528ms"
time="2025-07-31 10:20:39" level=info msg="GET / - 200 - 85.222µs"
time="2025-07-31 10:21:00" level=info msg="GET / - 200 - 832.415µs"
time="2025-07-31 10:21:00" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 973.361515ms"
time="2025-07-31 10:21:04" level=info msg="GET /assets/logo-BV-_qOqO.png - 200 - 819.053466ms"
time="2025-07-31 10:21:13" level=info msg="GET /favicon.ico - 200 - 476.207µs"
time="2025-07-31 10:21:14" level=info msg="PRI * - 200 - 74.522µs"
time="2025-07-31 10:21:31" level=info msg="GET /favicon.ico - 200 - 227.974µs"
time="2025-07-31 10:21:34" level=info msg="PRI * - 200 - 64.914µs"
time="2025-07-31 10:21:56" level=info msg="GET /robots.txt - 200 - 233.846µs"
time="2025-07-31 10:21:56" level=info msg="Successfully flushed 1 request logs."
time="2025-07-31 10:22:27" level=warning msg="Key has reached blacklist threshold, disabling." keyID=10 threshold=3
time="2025-07-31 10:22:27" level=warning msg="Key has reached blacklist threshold, disabling." keyID=1 threshold=3
time="2025-07-31 10:22:28" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 1.097017326s"
time="2025-07-31 10:22:56" level=info msg="Successfully flushed 1 request logs."
time="2025-07-31 10:22:59" level=info msg="GET /api/settings - 200 - 171.957µs"
time="2025-07-31 10:23:21" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 673.181535ms"
time="2025-07-31 10:23:56" level=info msg="Successfully flushed 1 request logs."
time="2025-07-31 10:24:15" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 651.967672ms"
time="2025-07-31 10:24:37" level=info msg="GET /api/dashboard/stats - 200 - 629.889µs"
time="2025-07-31 10:24:37" level=info msg="GET /api/dashboard/chart - 200 - 575.705µs"
time="2025-07-31 10:24:37" level=info msg="GET /api/groups/list - 200 - 571.94µs"
time="2025-07-31 10:24:40" level=info msg="GET /api/groups/config-options - 200 - 262.049µs"
time="2025-07-31 10:24:40" level=info msg="GET /api/groups - 200 - 599.622µs"
time="2025-07-31 10:24:40" level=info msg="GET /api/groups/1/stats - 200 - 1.401779ms"
time="2025-07-31 10:24:40" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 808.299µs"
time="2025-07-31 10:24:47" level=info msg="POST /api/keys/restore-all-invalid - 200 - 3.918482ms"
time="2025-07-31 10:24:47" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 1.014031ms"
time="2025-07-31 10:24:47" level=info msg="GET /api/groups/1/stats - 200 - 1.054258ms"
time="2025-07-31 10:24:49" level=info msg="POST /api/keys/validate-group - 200 - 759.878µs"
time="2025-07-31 10:24:49" level=info msg="Starting manual validation for group targon"
time="2025-07-31 10:24:49" level=info msg="GET /api/tasks/status - 200 - 98.417µs"
time="2025-07-31 10:24:50" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=2
time="2025-07-31 10:24:50" level=info msg="Manual validation finished for group targon: {TotalKeys:10 ValidKeys:10 InvalidKeys:0}"
time="2025-07-31 10:24:51" level=info msg="GET /api/tasks/status - 200 - 105.792µs"
time="2025-07-31 10:24:51" level=info msg="GET /api/groups/1/stats - 200 - 1.227347ms"
time="2025-07-31 10:24:51" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 1.154929ms"
time="2025-07-31 10:24:56" level=info msg="Successfully flushed 1 request logs."
time="2025-07-31 10:27:08" level=info msg="GET /api/settings - 200 - 215.47µs"
time="2025-07-31 10:27:11" level=info
time="2025-07-31 10:27:11" level=info msg="========= System Settings ========="
time="2025-07-31 10:27:11" level=info msg="  --- Basic Settings ---"
time="2025-07-31 10:27:11" level=info msg="    App URL: https://load.ainima.de"
time="2025-07-31 10:27:11" level=info msg="    Request Log Retention: 7 days"
time="2025-07-31 10:27:11" level=info msg="    Request Log Write Interval: 1 minutes"
time="2025-07-31 10:27:11" level=info msg="  --- Request Behavior ---"
time="2025-07-31 10:27:11" level=info msg="    Request Timeout: 600 seconds"
time="2025-07-31 10:27:11" level=info msg="    Connect Timeout: 15 seconds"
time="2025-07-31 10:27:11" level=info msg="    Response Header Timeout: 600 seconds"
time="2025-07-31 10:27:11" level=info msg="    Idle Connection Timeout: 120 seconds"
time="2025-07-31 10:27:11" level=info msg="    Max Idle Connections: 100"
time="2025-07-31 10:27:11" level=info msg="    Max Idle Connections Per Host: 50"
time="2025-07-31 10:27:11" level=info msg="  --- Key & Group Behavior ---"
time="2025-07-31 10:27:11" level=info msg="    Max Retries: 3"
time="2025-07-31 10:27:11" level=info msg="    Blacklist Threshold: 3"
time="2025-07-31 10:27:11" level=info msg="    Key Validation Interval: 60 minutes"
time="2025-07-31 10:27:11" level=info msg="===================================="
time="2025-07-31 10:27:11" level=info
time="2025-07-31 10:27:11" level=info msg="cache reloaded successfully" syncer=system_settings
time="2025-07-31 10:27:11" level=info msg="cache reloaded successfully" syncer=groups
time="2025-07-31 10:27:11" level=info msg="PUT /api/settings - 200 - 104.337805ms"
time="2025-07-31 10:27:11" level=info msg="GET /api/settings - 200 - 187.145µs"
time="2025-07-31 10:28:33" level=warning msg="POST /proxy/targon/v1/chat/completions - 429 - 1.288436718s"
time="2025-07-31 10:28:44" level=info msg="GET /api/groups - 200 - 716.614µs"
time="2025-07-31 10:28:44" level=info msg="GET /api/groups/config-options - 200 - 189.11µs"
time="2025-07-31 10:28:44" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 679.654µs"
time="2025-07-31 10:28:44" level=info msg="GET /api/groups/1/stats - 200 - 1.986082ms"
time="2025-07-31 10:28:56" level=info msg="Successfully flushed 1 request logs."
time="2025-07-31 10:29:26" level=info msg="POST /proxy/targon/v1/chat/completions - 200 - 806.275797ms"
time="2025-07-31 10:29:32" level=warning msg="POST /proxy/targon/v1/chat/completions - 429 - 924.186682ms"
time="2025-07-31 10:29:56" level=info msg="Successfully flushed 2 request logs."
time="2025-07-31 10:30:00" level=warning msg="POST /proxy/targon/v1/chat/completions - 429 - 1.198546397s"
time="2025-07-31 10:30:01" level=warning msg="POST /proxy/targon/v1/chat/completions - 429 - 915.477432ms"
time="2025-07-31 10:30:02" level=warning msg="POST /proxy/targon/v1/chat/completions - 429 - 639.937141ms"
time="2025-07-31 10:30:02" level=warning msg="Key has reached blacklist threshold, disabling." keyID=7 threshold=3
time="2025-07-31 10:30:03" level=warning msg="Key has reached blacklist threshold, disabling." keyID=8 threshold=3
time="2025-07-31 10:30:03" level=info msg="GET /assets/Logs-D_3m6Xv1.css - 200 - 2.406984ms"
time="2025-07-31 10:30:03" level=warning msg="Key has reached blacklist threshold, disabling." keyID=9 threshold=3
time="2025-07-31 10:30:03" level=info msg="GET /assets/Logs-BAsgvVXW.js - 200 - 1.198282ms"
time="2025-07-31 10:30:03" level=warning msg="Key has reached blacklist threshold, disabling." keyID=6 threshold=3
time="2025-07-31 10:30:03" level=warning msg="POST /proxy/targon/v1/chat/completions - 429 - 566.015515ms"
time="2025-07-31 10:30:03" level=warning msg="Key has reached blacklist threshold, disabling." keyID=2 threshold=3
time="2025-07-31 10:30:04" level=info msg="GET /api/logs?page=1&page_size=15 - 200 - 1.297911ms"
time="2025-07-31 10:30:56" level=info msg="Successfully flushed 4 request logs."
time="2025-07-31 10:36:44" level=info msg="GET /.git/config - 200 - 483.471µs"
time="2025-07-31 10:37:02" level=info msg="GET / - 200 - 200.482µs"
time="2025-07-31 10:37:03" level=info msg="GET / - 200 - 604.842µs"
time="2025-07-31 10:37:09" level=info msg="GET /.git/config - 200 - 404.391µs"
time="2025-07-31 10:37:16" level=info msg="GET / - 200 - 564.285µs"
time="2025-07-31 10:37:29" level=info msg="GET /.git/config - 200 - 220.26µs"
time="2025-07-31 10:37:33" level=info msg="GET /assets/index-SPLiOoPZ.js - 200 - 52.603751ms"
time="2025-07-31 10:41:16" level=info msg="GET /.git/config - 200 - 457.822µs"
time="2025-07-31 10:41:39" level=info msg="GET /.git/config - 200 - 177.308µs"
time="2025-07-31 10:50:10" level=info msg="GET / - 200 - 300.622µs"
time="2025-07-31 10:50:10" level=info msg="GET / - 200 - 543.895µs"
time="2025-07-31 10:50:16" level=info msg="GET / - 200 - 196.975µs"
time="2025-07-31 10:50:24" level=info msg="GET /assets/index-SPLiOoPZ.js - 200 - 69.563279ms"
time="2025-07-31 10:53:53" level=info msg="GET / - 200 - 181.115µs"
time="2025-07-31 10:58:57" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=6
time="2025-07-31 10:58:57" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=8
time="2025-07-31 10:58:57" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=9
time="2025-07-31 10:58:57" level=info msg="CronChecker: Group 'targon' validation finished. Total checked: 5, became valid: 5. Duration: 958.521993ms."
time="2025-07-31 11:02:30" level=info msg="GET / - 200 - 479.042µs"
time="2025-07-31 11:02:32" level=info msg="GET /assets/logo-BV-_qOqO.png - 200 - 1.027396ms"
time="2025-07-31 11:02:32" level=info msg="GET /js/twint_ch.js - 200 - 1.115926ms"
time="2025-07-31 11:02:32" level=info msg="GET /js/lkk_ch.js - 200 - 618.839µs"
time="2025-07-31 12:03:57" level=error msg="Failed to handle key success" error="failed to update key in DB: database is locked (5) (SQLITE_BUSY)" keyID=8
time="2025-07-31 12:03:57" level=info msg="CronChecker: Group 'targon' validation finished. Total checked: 3, became valid: 3. Duration: 846.013647ms."
time="2025-07-31 12:03:59" level=info msg="GET / - 200 - 1.504803ms"
time="2025-07-31 12:03:59" level=info msg="GET / - 200 - 198.518µs"
time="2025-07-31 12:13:13" level=info msg="GET / - 200 - 670.797µs"
time="2025-07-31 12:13:13" level=info msg="GET /assets/index-Gswog8ov.css - 200 - 437.232µs"
time="2025-07-31 12:13:13" level=info msg="GET /assets/index-SPLiOoPZ.js - 200 - 65.929427ms"
time="2025-07-31 12:13:17" level=info msg="GET / - 200 - 459.215µs"
time="2025-07-31 12:13:17" level=info msg="GET /assets/index-Gswog8ov.css - 200 - 587.147µs"
time="2025-07-31 12:13:17" level=info msg="GET /assets/index-SPLiOoPZ.js - 200 - 82.550692ms"
time="2025-07-31 12:39:27" level=info msg="GET / - 200 - 468.331µs"
time="2025-07-31 12:39:28" level=info msg="GET / - 200 - 293.788µs"
time="2025-07-31 12:39:28" level=info msg="GET /assets/index-Gswog8ov.css - 200 - 2.467245ms"
time="2025-07-31 12:39:28" level=info msg="GET /assets/index-SPLiOoPZ.js - 200 - 60.929418ms"
time="2025-07-31 12:39:28" level=info msg="GET /assets/Login-DzpmRpZS.css - 200 - 243.222µs"
time="2025-07-31 12:39:28" level=info msg="GET /assets/Login-C7BGdm6I.js - 200 - 416.392µs"
time="2025-07-31 12:39:28" level=info msg="GET /assets/logo-BV-_qOqO.png - 200 - 2.820898ms"
time="2025-07-31 12:39:29" level=info msg="GET /login - 200 - 259.223µs"
time="2025-07-31 12:39:29" level=info msg="GET /favicon.ico - 200 - 266.166µs"
time="2025-07-31 12:39:29" level=info msg="GET /favicon.ico - 200 - 321.01µs"
time="2025-07-31 12:39:29" level=info msg="GET /assets/logo-BV-_qOqO.png - 200 - 846.541µs"
time="2025-07-31 13:08:57" level=info msg="CronChecker: Group 'targon' validation finished. Total checked: 1, became valid: 1. Duration: 782.270539ms."
time="2025-07-31 13:53:05" level=info msg="GET / - 200 - 556.188µs"
time="2025-07-31 13:53:06" level=info msg="GET /api/dashboard/stats - 200 - 797.136µs"
time="2025-07-31 13:53:06" level=info msg="GET /api/dashboard/chart - 200 - 548.113µs"
time="2025-07-31 13:53:06" level=info msg="GET /api/groups/list - 200 - 841.221µs"
time="2025-07-31 13:53:06" level=info msg="GET /api/tasks/status - 200 - 61.806µs"
time="2025-07-31 13:53:10" level=info msg="GET /api/groups/config-options - 200 - 211.162µs"
time="2025-07-31 13:53:10" level=info msg="GET /api/groups - 200 - 690.213µs"
time="2025-07-31 13:53:11" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 2.114222ms"
time="2025-07-31 13:53:11" level=info msg="GET /api/groups/1/stats - 200 - 2.668867ms"
time="2025-07-31 13:53:16" level=info msg="GET /api/channel-types - 200 - 54.834µs"
time="2025-07-31 13:53:16" level=info msg="GET /api/groups/config-options - 200 - 271.426µs"
time="2025-07-31 13:53:40" level=info msg="POST /api/groups - 200 - 5.796627ms"
time="2025-07-31 13:53:40" level=info msg="cache reloaded successfully" syncer=groups
time="2025-07-31 13:53:40" level=info msg="GET /api/groups - 200 - 781.146µs"
time="2025-07-31 13:53:41" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 1.207036ms"
time="2025-07-31 13:53:41" level=info msg="GET /api/groups/2/stats - 200 - 1.199752ms"
time="2025-07-31 13:53:45" level=info msg="GET /api/settings - 200 - 212.565µs"
time="2025-07-31 13:53:56" level=info msg="CronChecker: Group 'gemini' has no invalid keys to check."
time="2025-07-31 13:54:24" level=info msg="GET /api/logs?page=1&page_size=15 - 200 - 1.17916ms"
time="2025-07-31 13:54:27" level=info msg="GET /api/groups/config-options - 200 - 161.067µs"
time="2025-07-31 13:54:27" level=info msg="GET /api/groups - 200 - 910.698µs"
time="2025-07-31 13:54:28" level=info msg="GET /api/groups/2/stats - 200 - 1.258461ms"
time="2025-07-31 13:54:28" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 585.207µs"
time="2025-07-31 13:54:30" level=info msg="GET /api/groups/1/stats - 200 - 1.375604ms"
time="2025-07-31 13:54:30" level=info msg="GET /api/keys?group_id=1&page=1&page_size=12 - 200 - 2.056813ms"
time="2025-07-31 13:54:30" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 713.451µs"
time="2025-07-31 13:54:30" level=info msg="GET /api/groups/2/stats - 200 - 1.20571ms"
time="2025-07-31 13:57:27" level=error msg="Failed to select a key for group gemini on attempt 1: No active API keys available for this group"
time="2025-07-31 13:57:27" level=error msg="GET /proxy/gemini/api/groups - 503 - 353.794µs"
time="2025-07-31 13:57:56" level=info msg="Successfully flushed 1 request logs."
time="2025-07-31 13:58:26" level=error msg="Failed to select a key for group gemini on attempt 1: No active API keys available for this group"
time="2025-07-31 13:58:26" level=error msg="GET /proxy/gemini/api/groups - 503 - 240.117µs"
time="2025-07-31 13:58:56" level=info msg="Successfully flushed 1 request logs."
time="2025-07-31 13:59:27" level=error msg="Failed to select a key for group gemini on attempt 1: No active API keys available for this group"
time="2025-07-31 13:59:27" level=error msg="GET /proxy/gemini/api/groups - 503 - 247.882µs"
time="2025-07-31 13:59:56" level=info msg="Successfully flushed 1 request logs."
time="2025-07-31 14:00:26" level=error msg="Failed to select a key for group gemini on attempt 1: No active API keys available for this group"
time="2025-07-31 14:00:26" level=error msg="GET /proxy/gemini/api/groups - 503 - 288.27µs"
time="2025-07-31 14:00:56" level=info msg="Successfully flushed 1 request logs."
time="2025-07-31 14:01:26" level=error msg="Failed to select a key for group gemini on attempt 1: No active API keys available for this group"
time="2025-07-31 14:01:26" level=error msg="GET /proxy/gemini/api/groups - 503 - 579.063µs"
time="2025-07-31 14:01:42" level=info msg="GET /keys?groupId=2 - 200 - 560.829µs"
time="2025-07-31 14:01:43" level=info msg="GET /api/groups/config-options - 200 - 180.002µs"
time="2025-07-31 14:01:43" level=info msg="GET /api/groups - 200 - 758.865µs"
time="2025-07-31 14:01:43" level=info msg="GET /api/tasks/status - 200 - 49.434µs"
time="2025-07-31 14:01:43" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 548.415µs"
time="2025-07-31 14:01:43" level=info msg="GET /api/groups/2/stats - 200 - 1.525717ms"
time="2025-07-31 14:01:51" level=info msg="GET /api/logs?page=1&page_size=15 - 200 - 1.015785ms"
time="2025-07-31 14:01:56" level=info msg="Successfully flushed 1 request logs."
time="2025-07-31 14:01:57" level=info msg="GET /api/groups/list - 200 - 358.914µs"
time="2025-07-31 14:01:57" level=info msg="GET /api/dashboard/stats - 200 - 1.332709ms"
time="2025-07-31 14:01:57" level=info msg="GET /api/dashboard/chart - 200 - 552.102µs"
time="2025-07-31 14:01:59" level=info msg="GET /api/settings - 200 - 434.588µs"
time="2025-07-31 14:02:26" level=error msg="Failed to select a key for group gemini on attempt 1: No active API keys available for this group"
time="2025-07-31 14:02:26" level=error msg="GET /proxy/gemini/api/groups - 503 - 294.381µs"
time="2025-07-31 14:02:51" level=info msg="GET /api/groups/config-options - 200 - 204.64µs"
time="2025-07-31 14:02:51" level=info msg="GET /api/groups - 200 - 821.966µs"
time="2025-07-31 14:02:51" level=info msg="GET /api/groups/2/stats - 200 - 1.156683ms"
time="2025-07-31 14:02:51" level=info msg="GET /api/keys?group_id=2&page=1&page_size=12 - 200 - 462.341µs"
time="2025-07-31 14:02:56" level=info msg="Successfully flushed 1 request logs."
time="2025-07-31 14:03:27" level=error msg="Failed to select a key for group gemini on attempt 1: No active API keys available for this group"
time="2025-07-31 14:03:27" level=error msg="GET /proxy/gemini/api/groups - 503 - 337.653µs"
