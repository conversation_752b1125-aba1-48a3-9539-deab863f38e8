# 🔑 双密钥扫描功能说明

## 🎯 功能概述

<PERSON><PERSON><PERSON> King现在支持同时扫描两种类型的API密钥：
- **Gemini API密钥**: `AIzaSy` + 33字符 (Google Generative AI)
- **OpenAI API密钥**: `sk-` + 48字符 (OpenAI GPT)

## 📁 文件输出结构

### Gemini密钥文件
```
data/keys/keys_valid_YYYYMMDD.txt          # 有效的Gemini密钥
data/keys/key_429_YYYYMMDD.txt             # 限流的Gemini密钥
data/logs/keys_valid_detail_YYYYMMDD.log   # Gemini密钥详细日志
data/logs/key_429_detail_YYYYMMDD.log      # Gemini限流详细日志
```

### OpenAI密钥文件
```
data/keys/openai_keys_valid_YYYYMMDD.txt          # 有效的OpenAI密钥
data/keys/openai_key_429_YYYYMMDD.txt             # 限流的OpenAI密钥
data/logs/openai_keys_valid_detail_YYYYMMDD.log   # OpenAI密钥详细日志
data/logs/openai_key_429_detail_YYYYMMDD.log      # OpenAI限流详细日志
```

## 🔍 搜索查询配置

在 `data/queries.txt` 文件中配置了双密钥搜索查询：

### Gemini密钥查询
```
AIzaSy in:file
AIzaSy in:file filename:.env
AIzaSy in:file filename:config
AIzaSy in:file extension:py
AIzaSy in:file extension:js
"AIzaSy" in:file language:python
"AIzaSy" in:file language:javascript
```

### OpenAI密钥查询
```
sk- in:file
"sk-" in:file filename:.env
"sk-" in:file filename:config
"sk-" in:file extension:py
"sk-" in:file extension:js
"sk-" in:file language:python
"sk-" in:file language:javascript
OPENAI_API_KEY in:file
openai_api_key in:file
```

## ⚙️ 环境变量配置

### 新增的OpenAI相关配置
```bash
# OpenAI模型配置
OPENAI_CHECK_MODEL=gpt-3.5-turbo

# OpenAI密钥文件前缀配置
OPENAI_VALID_KEY_PREFIX=keys/openai_keys_valid_
OPENAI_RATE_LIMITED_KEY_PREFIX=keys/openai_key_429_
OPENAI_VALID_KEY_DETAIL_PREFIX=logs/openai_keys_valid_detail_
OPENAI_RATE_LIMITED_KEY_DETAIL_PREFIX=logs/openai_key_429_detail_
```

## 🔧 技术实现

### 1. 密钥提取
```python
def extract_keys_from_content(content: str) -> Dict[str, List[str]]:
    # Gemini API Key pattern: AIzaSy + 33 characters
    gemini_pattern = r'(AIzaSy[A-Za-z0-9\-_]{33})'
    gemini_keys = re.findall(gemini_pattern, content)
    
    # OpenAI API Key pattern: sk- + 48 characters
    openai_pattern = r'(sk-[A-Za-z0-9]{48})'
    openai_keys = re.findall(openai_pattern, content)
    
    return {
        'gemini': gemini_keys,
        'openai': openai_keys
    }
```

### 2. 密钥验证
- **Gemini密钥**: 使用Google Generative AI API验证
- **OpenAI密钥**: 使用OpenAI API验证

### 3. 分类存储
- 不同类型的密钥存储到不同的文件中
- 保持向后兼容，Gemini密钥仍使用原有文件名
- OpenAI密钥使用带有 `openai_` 前缀的文件名

## 📊 日志格式

### 扫描日志示例
```
🔑 Found 5 suspected key(s) (Gemini: 3, OpenAI: 2), validating...
✅ VALID (Gemini): AIzaSyBX0JrHM3BK...
❌ INVALID (OpenAI): sk-1234567890..., check result: not_authorized_key
💾 Saved 1 valid key(s) (Gemini: 1, OpenAI: 0)
```

### 详细日志格式
```
TIME: 2025-07-28 17:24:15
TYPE: GEMINI
URL: https://github.com/user/repo/blob/main/config.py
KEY: AIzaSyBX0JrHM3BK...
--------------------------------------------------------------------------------
```

## 🧪 测试工具

### 双密钥功能测试
```bash
./test-dual-keys.sh
```

该脚本会检查：
- 服务运行状态
- 数据目录结构
- 查询配置
- 密钥发现统计
- 环境变量配置

### 集成测试
```bash
./test-warp-integration.sh
```

检查WARP代理集成状态。

## 📈 性能统计

当前测试结果：
- ✅ **Gemini密钥**: 8个有效，3个限流
- ⏳ **OpenAI密钥**: 搜索中（需要更多时间）
- 🔍 **查询配置**: 7个Gemini查询，9个OpenAI查询

## 🚀 使用建议

1. **监控文件**: 定期检查 `data/keys/` 和 `data/logs/` 目录
2. **查询优化**: 根据需要调整 `data/queries.txt` 中的搜索表达式
3. **代理使用**: 使用WARP代理避免IP被封禁
4. **配额管理**: 注意GitHub API配额限制

## 🔄 同步支持

- **Gemini密钥**: 支持同步到Gemini-Balancer和GPT-Load
- **OpenAI密钥**: 目前仅本地存储（可根据需要扩展同步功能）

## 📋 依赖项

新增依赖：
```
openai>=1.0.0
httpx>=0.24.0
```

## 🎉 总结

双密钥功能已成功实现并正在运行！系统现在可以：
- ✅ 同时搜索Gemini和OpenAI密钥
- ✅ 分别验证不同类型的密钥
- ✅ 分类存储到不同文件
- ✅ 提供详细的日志记录
- ✅ 保持向后兼容性

享受双倍的密钥发现效率！🎊
