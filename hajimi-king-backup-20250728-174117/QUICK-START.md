# 🚀 Ha<PERSON><PERSON> King + WARP 快速启动指南

## 📋 前置要求

- ✅ Docker 和 Docker Compose（支持 `docker compose` 命令）
- ✅ GitHub Token（具有 `public_repo` 权限）
- ✅ 网络连接

## ⚡ 3分钟快速部署

### 1️⃣ 获取GitHub Token
访问 [GitHub Settings > Tokens](https://github.com/settings/tokens)，创建新的访问令牌：
- 选择 `public_repo` 权限
- 复制生成的token（格式：`ghp_xxxxxxxxxxxx`）

### 2️⃣ 一键部署
```bash
cd hajimi-king
./deploy-with-warp.sh
```

脚本会自动：
- ✅ 检查环境
- ✅ 创建配置文件
- ✅ 启动WARP代理
- ✅ 启动Hajimi King
- ✅ 验证连接

### 3️⃣ 验证部署
```bash
# 测试集成状态
./test-warp-integration.sh

# 查看服务状态
docker compose ps

# 查看实时日志
docker compose logs -f
```

## 📊 预期结果

### 服务状态
```
NAME           IMAGE                                    STATUS
hajimi-king    ghcr.io/gakkinoone/hajimi-king:latest   Up
hajimi-warp    caomingjun/warp                         Up (healthy)
```

### WARP代理测试
```bash
curl --socks5-hostname 127.0.0.1:1080 https://cloudflare.com/cdn-cgi/trace
```
应该返回包含 `warp=on` 或 `warp=plus` 的响应。

### 数据文件
- `./data/keys/keys_valid_*.txt` - 发现的有效密钥
- `./data/logs/keys_valid_detail_*.log` - 详细日志
- `./warp-data/` - WARP配置数据

## 🎛️ 基本管理

```bash
# 查看日志
docker compose logs -f hajimi-king  # Hajimi King日志
docker compose logs -f warp         # WARP代理日志

# 重启服务
docker compose restart

# 停止服务
docker compose down

# 更新服务
docker compose pull && docker compose up -d
```

## 🔧 常见问题

### Q: WARP代理无法启动？
```bash
# 检查日志
docker compose logs warp

# 常见解决方案
sudo sysctl net.ipv4.conf.all.src_valid_mark=1
```

### Q: 找不到有效密钥？
- 检查 `data/queries.txt` 搜索表达式
- 确认GitHub Token有效且有足够配额
- 查看详细日志了解搜索进度

### Q: 代理连接失败？
```bash
# 测试代理
curl --socks5-hostname 127.0.0.1:1080 https://google.com

# 检查网络
docker compose exec hajimi-king ping warp
```

## 📈 性能优化

### 提高搜索效率
1. **优化查询表达式**：编辑 `data/queries.txt`
2. **增加GitHub Token**：在 `.env` 中添加多个token
3. **调整日期范围**：修改 `DATE_RANGE_DAYS` 参数

### 使用WARP+
如果有WARP+订阅，在 `.env` 中添加：
```bash
WARP_LICENSE_KEY=your_warp_plus_key
```

## 🎯 下一步

1. **监控运行**：定期检查日志和发现的密钥
2. **优化配置**：根据需要调整搜索参数
3. **数据管理**：定期备份和清理数据文件
4. **安全维护**：定期更新镜像和轮换Token

## 📚 更多资源

- [完整README](README.md) - 详细功能说明
- [WARP集成指南](README-WARP-INTEGRATION.md) - 深入配置说明
- [GitHub API文档](https://docs.github.com/en/rest) - API使用说明

---

🎉 **恭喜！您已成功部署 Hajimi King + WARP 集成服务！**

如有问题，请查看日志或提交Issue。享受搜索的乐趣！ 🎊
