#!/bin/bash

# <PERSON><PERSON><PERSON> King + WARP 集成部署脚本
# 此脚本将帮助您快速部署带有WARP代理的Haji<PERSON> King服务

set -e

echo "🎪 <PERSON><PERSON><PERSON> King + WARP 集成部署脚本 🏆"
echo "========================================"

# 检查Docker和Docker Compose
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    exit 1
fi

if ! docker compose version &> /dev/null; then
    echo "❌ Docker Compose 未安装或版本过旧，请升级到支持 'docker compose' 命令的版本"
    exit 1
fi

# 创建必要的目录
echo "📁 创建必要的目录..."
mkdir -p data/keys
mkdir -p data/logs
mkdir -p warp-data

# 检查.env文件
if [ ! -f ".env" ]; then
    echo "⚙️ 创建 .env 配置文件..."
    cp env.example .env
    echo "📝 请编辑 .env 文件，填入您的 GitHub Token："
    echo "   GITHUB_TOKENS=your_github_token_here"
    echo ""
    echo "💡 获取 GitHub Token: https://github.com/settings/tokens"
    echo "   需要 'public_repo' 权限"
    echo ""
    read -p "是否现在编辑 .env 文件？(y/n): " edit_env
    if [[ $edit_env =~ ^[Yy]$ ]]; then
        ${EDITOR:-nano} .env
    fi
else
    echo "✅ .env 文件已存在"
fi

# 检查queries.txt文件
if [ ! -f "data/queries.txt" ]; then
    echo "🔍 创建搜索查询配置文件..."
    cat > data/queries.txt << 'EOF'
# GitHub搜索查询配置文件
# 每行一个查询语句，支持GitHub搜索语法
# 以#开头的行为注释，空行会被忽略

# Gemini API Key搜索
AIzaSy in:file
AIzaSy in:file filename:.env
AIzaSy in:file filename:config
AIzaSy in:file extension:py
AIzaSy in:file extension:js
AIzaSy in:file extension:json

# 更精确的Gemini搜索
"AIzaSy" in:file language:python
"AIzaSy" in:file language:javascript
"AIzaSy" in:file path:config
"AIzaSy" in:file path:src

# OpenAI API Key搜索
sk- in:file
"sk-" in:file filename:.env
"sk-" in:file filename:config
"sk-" in:file extension:py
"sk-" in:file extension:js

# 更精确的OpenAI搜索
"sk-" in:file language:python
"sk-" in:file language:javascript
OPENAI_API_KEY in:file
openai_api_key in:file
EOF
    echo "✅ 已创建默认的 queries.txt 文件"
else
    echo "✅ queries.txt 文件已存在"
fi

# 检查GitHub Token配置
if ! grep -q "GITHUB_TOKENS=.*[^[:space:]]" .env; then
    echo "⚠️  警告：未检测到有效的 GitHub Token 配置"
    echo "   请确保在 .env 文件中设置了 GITHUB_TOKENS"
    echo ""
    read -p "是否继续部署？(y/n): " continue_deploy
    if [[ ! $continue_deploy =~ ^[Yy]$ ]]; then
        echo "❌ 部署已取消"
        exit 1
    fi
fi

echo ""
echo "🚀 启动服务..."
echo "   - WARP 代理服务将在端口 1080 提供服务"
echo "   - Hajimi King 将自动使用 WARP 代理"
echo ""

# 启动服务
docker compose up -d

echo ""
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "📊 检查服务状态..."
docker compose ps

echo ""
echo "🔍 检查 WARP 代理连接..."
if docker compose exec -T warp curl --socks5-hostname 127.0.0.1:1080 https://cloudflare.com/cdn-cgi/trace 2>/dev/null | grep -q "warp=on\|warp=plus"; then
    echo "✅ WARP 代理工作正常"
else
    echo "⚠️  WARP 代理可能还在启动中，请稍后检查"
fi

echo ""
echo "🎉 部署完成！"
echo ""
echo "📋 管理命令："
echo "   查看日志:     docker compose logs -f"
echo "   查看状态:     docker compose ps"
echo "   停止服务:     docker compose down"
echo "   重启服务:     docker compose restart"
echo ""
echo "📁 数据文件位置："
echo "   有效密钥:     ./data/keys/keys_valid_*.txt"
echo "   详细日志:     ./data/logs/keys_valid_detail_*.log"
echo "   WARP数据:     ./warp-data/"
echo ""
echo "🌐 代理测试："
echo "   curl --socks5-hostname 127.0.0.1:1080 https://cloudflare.com/cdn-cgi/trace"
echo ""
echo "💖 享受使用 Hajimi King + WARP 的快乐时光！🎊"
