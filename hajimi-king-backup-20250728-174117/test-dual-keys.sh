#!/bin/bash

# 双密钥功能测试脚本
# 测试Gemini和OpenAI密钥的同时扫描功能

set -e

echo "🔑 Hajimi King 双密钥功能测试"
echo "================================"

# 检查服务是否运行
echo "📊 检查服务状态..."
if ! docker compose ps | grep -q "Up"; then
    echo "❌ 服务未运行，请先执行: docker compose up -d"
    exit 1
fi

echo "✅ 服务正在运行"

# 检查数据目录结构
echo ""
echo "📁 检查数据目录结构..."
echo "Gemini密钥文件:"
ls -la data/keys/*gemini* 2>/dev/null || echo "  暂无Gemini密钥文件"
ls -la data/keys/keys_valid_* 2>/dev/null || echo "  暂无传统格式密钥文件"

echo ""
echo "OpenAI密钥文件:"
ls -la data/keys/*openai* 2>/dev/null || echo "  暂无OpenAI密钥文件"

echo ""
echo "日志文件:"
ls -la data/logs/*gemini* 2>/dev/null || echo "  暂无Gemini日志文件"
ls -la data/logs/*openai* 2>/dev/null || echo "  暂无OpenAI日志文件"
ls -la data/logs/keys_valid_detail_* 2>/dev/null || echo "  暂无传统格式日志文件"

# 检查查询配置
echo ""
echo "🔍 检查查询配置..."
if [ -f "data/queries.txt" ]; then
    echo "✅ queries.txt 存在"
    gemini_queries=$(grep -c "AIzaSy" data/queries.txt || echo "0")
    openai_queries=$(grep -c "sk-\|OPENAI_API_KEY\|openai_api_key" data/queries.txt || echo "0")
    echo "   Gemini查询数量: $gemini_queries"
    echo "   OpenAI查询数量: $openai_queries"
    
    if [ "$gemini_queries" -gt 0 ] && [ "$openai_queries" -gt 0 ]; then
        echo "✅ 双密钥查询配置正确"
    else
        echo "⚠️  查询配置可能不完整"
    fi
else
    echo "❌ queries.txt 不存在"
fi

# 检查最近的日志，看是否有双密钥扫描
echo ""
echo "📝 检查最近的扫描日志..."
recent_logs=$(docker compose logs hajimi-king --tail=20 2>/dev/null)

if echo "$recent_logs" | grep -q "Gemini.*OpenAI"; then
    echo "✅ 检测到双密钥扫描活动"
elif echo "$recent_logs" | grep -q "Found.*suspected key"; then
    echo "✅ 检测到密钥扫描活动"
    echo "$recent_logs" | grep "Found.*suspected key" | tail -3
else
    echo "ℹ️  暂未检测到最近的扫描活动"
fi

# 统计发现的密钥
echo ""
echo "📊 密钥发现统计..."

# Gemini密钥统计
gemini_valid=0
gemini_rate_limited=0

if [ -f "data/keys/keys_valid_$(date +%Y%m%d).txt" ]; then
    gemini_valid=$(wc -l < "data/keys/keys_valid_$(date +%Y%m%d).txt" 2>/dev/null || echo "0")
fi

if [ -f "data/keys/key_429_$(date +%Y%m%d).txt" ]; then
    gemini_rate_limited=$(wc -l < "data/keys/key_429_$(date +%Y%m%d).txt" 2>/dev/null || echo "0")
fi

# OpenAI密钥统计
openai_valid=0
openai_rate_limited=0

if [ -f "data/keys/openai_keys_valid_$(date +%Y%m%d).txt" ]; then
    openai_valid=$(wc -l < "data/keys/openai_keys_valid_$(date +%Y%m%d).txt" 2>/dev/null || echo "0")
fi

if [ -f "data/keys/openai_key_429_$(date +%Y%m%d).txt" ]; then
    openai_rate_limited=$(wc -l < "data/keys/openai_key_429_$(date +%Y%m%d).txt" 2>/dev/null || echo "0")
fi

echo "🔑 Gemini密钥:"
echo "   有效密钥: $gemini_valid"
echo "   限流密钥: $gemini_rate_limited"

echo "🤖 OpenAI密钥:"
echo "   有效密钥: $openai_valid"
echo "   限流密钥: $openai_rate_limited"

total_valid=$((gemini_valid + openai_valid))
total_rate_limited=$((gemini_rate_limited + openai_rate_limited))

echo "📈 总计:"
echo "   有效密钥: $total_valid"
echo "   限流密钥: $total_rate_limited"

# 显示示例密钥（如果有）
echo ""
echo "🔍 密钥示例 (仅显示前缀)..."

if [ "$gemini_valid" -gt 0 ]; then
    echo "Gemini有效密钥示例:"
    head -3 "data/keys/keys_valid_$(date +%Y%m%d).txt" 2>/dev/null | sed 's/\(AIzaSy.\{10\}\).*/\1.../' || echo "  无法读取"
fi

if [ "$openai_valid" -gt 0 ]; then
    echo "OpenAI有效密钥示例:"
    head -3 "data/keys/openai_keys_valid_$(date +%Y%m%d).txt" 2>/dev/null | sed 's/\(sk-.\{10\}\).*/\1.../' || echo "  无法读取"
fi

# 检查环境变量配置
echo ""
echo "⚙️ 检查环境变量配置..."
if docker compose exec -T hajimi-king printenv | grep -q "OPENAI_CHECK_MODEL"; then
    echo "✅ OpenAI模型配置已设置"
else
    echo "⚠️  OpenAI模型配置未设置，将使用默认值"
fi

# 性能建议
echo ""
echo "💡 性能建议:"
echo "   - 定期检查 data/keys/ 目录下的密钥文件"
echo "   - 监控 data/logs/ 目录下的详细日志"
echo "   - 根据需要调整查询表达式以提高效率"
echo "   - 使用代理避免IP被封禁"

echo ""
echo "🎯 测试完成！"
echo ""
echo "📋 文件说明:"
echo "   Gemini密钥: data/keys/keys_valid_*.txt"
echo "   OpenAI密钥: data/keys/openai_keys_valid_*.txt"
echo "   Gemini日志: data/logs/keys_valid_detail_*.log"
echo "   OpenAI日志: data/logs/openai_keys_valid_detail_*.log"
