# 🎪 官方 Hajimi King + WARP 集成部署指南

本指南基于官方 [hajimi-king](https://github.com/GakkiNoOne/hajimi-king) 项目，集成 [warp-docker](https://github.com/cmj2002/warp-docker) 代理服务。

## 🎯 项目特点

- ✅ **官方版本**: 严格基于官方 hajimi-king 项目
- ✅ **WARP集成**: 集成 Cloudflare WARP 代理避免IP封禁
- ✅ **官方镜像**: 使用 `ghcr.io/gakkinoone/hajimi-king:latest`
- ✅ **纯Gemini**: 专注于 Gemini API 密钥搜索（不包含OpenAI）
- ✅ **一键部署**: 提供自动化部署脚本

## 🚀 快速部署

### 方法一：一键部署（推荐）

```bash
# 进入项目目录
cd hajimi-king

# 运行一键部署脚本
./deploy-with-warp.sh
```

### 方法二：手动部署

1. **配置环境变量**
```bash
cp env.example .env
# 编辑 .env 文件，填入 GitHub Token
```

2. **创建数据目录**
```bash
mkdir -p data/keys data/logs warp-data
```

3. **创建查询配置**
```bash
cp queries.example data/queries.txt
```

4. **启动服务**
```bash
docker compose up -d
```

## 📋 服务架构

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Hajimi King   │───▶│   WARP Proxy     │───▶│  Internet       │
│   (官方镜像)     │    │   (代理服务)      │    │  (GitHub/Gemini)│
└─────────────────┘    └──────────────────┘    └─────────────────┘
      官方版本                :1080                    :443
```

## ⚙️ 配置说明

### 环境变量配置 (.env)

```bash
# 必填：GitHub Token（支持多个，逗号分隔）
GITHUB_TOKENS=your_token_1,your_token_2

# 代理配置（自动配置为WARP代理）
PROXY=http://warp:1080

# 其他配置保持默认即可
DATA_PATH=/app/data
QUERIES_FILE=queries.txt
HAJIMI_CHECK_MODEL=gemini-2.5-flash
```

### Docker Compose 特点

- **官方镜像**: `ghcr.io/gakkinoone/hajimi-king:latest`
- **网络隔离**: 专用Docker网络 `hajimi-network`
- **健康检查**: WARP服务包含健康检查
- **依赖管理**: Hajimi King等待WARP健康后启动
- **数据持久化**: 数据和WARP配置持久化存储

## 🔧 管理命令

```bash
# 查看服务状态
docker compose ps

# 查看日志
docker compose logs -f hajimi-king  # Hajimi King日志
docker compose logs -f warp         # WARP代理日志

# 重启服务
docker compose restart

# 停止服务
docker compose down

# 更新服务
docker compose pull && docker compose up -d
```

## 🧪 测试工具

### 官方版本集成测试
```bash
./test-official-warp.sh
```

该脚本会检查：
- 服务运行状态
- WARP代理连接
- 网络连通性
- 配置文件
- 镜像版本
- 密钥发现统计

### 手动测试

```bash
# 测试WARP代理
curl --socks5-hostname 127.0.0.1:1080 https://cloudflare.com/cdn-cgi/trace

# 查看服务状态
docker compose ps

# 查看实时日志
docker compose logs -f
```

## 📁 数据文件

```
data/
├── keys/
│   ├── keys_valid_YYYYMMDD.txt      # 有效的Gemini密钥
│   └── key_429_YYYYMMDD.txt         # 限流的Gemini密钥
├── logs/
│   ├── keys_valid_detail_YYYYMMDD.log   # 详细日志
│   └── key_429_detail_YYYYMMDD.log      # 限流详细日志
├── queries.txt                      # 搜索查询配置
├── checkpoint.json                  # 检查点数据
└── scanned_shas.txt                # 已扫描文件记录

warp-data/                          # WARP配置数据
```

## 🔍 搜索查询

默认查询配置（`data/queries.txt`）：
```
# 基础Gemini API Key搜索
AIzaSy in:file
AIzaSy in:file filename:.env
AIzaSy in:file filename:config
AIzaSy in:file extension:py
AIzaSy in:file extension:js

# 更精确的搜索
"AIzaSy" in:file language:python
"AIzaSy" in:file language:javascript
"AIzaSy" in:file path:config
```

## 📊 性能优化

1. **多Token配置**: 在 `.env` 中配置多个GitHub Token
2. **查询优化**: 根据需要调整 `data/queries.txt`
3. **WARP+订阅**: 如有订阅，可配置 `WARP_LICENSE_KEY`
4. **监控配额**: 注意GitHub API配额限制

## 🔄 同步支持

支持同步到外部服务：
- **Gemini Balancer**: 配置 `GEMINI_BALANCER_*` 变量
- **GPT Load**: 配置 `GPT_LOAD_*` 变量

## 🛠️ 故障排除

### WARP代理问题
```bash
# 检查WARP日志
docker compose logs warp

# 重启WARP服务
docker compose restart warp
```

### Hajimi King问题
```bash
# 检查配置
cat .env

# 查看详细日志
docker compose logs hajimi-king
```

### 网络连接问题
```bash
# 测试容器间连接
docker compose exec hajimi-king curl -I http://warp:1080
```

## 🎉 总结

这是一个基于官方 hajimi-king 项目的完整集成方案：

- ✅ **官方兼容**: 完全基于官方项目，无修改
- ✅ **WARP集成**: 自动配置WARP代理
- ✅ **简单部署**: 一键部署脚本
- ✅ **完整测试**: 提供测试工具
- ✅ **生产就绪**: 包含健康检查和依赖管理

享受使用官方 Hajimi King + WARP 的稳定搜索体验！🎊
