# 🌐 Hajimi King + WARP 集成部署指南

本指南介绍如何将 [warp-docker](https://github.com/cmj2002/warp-docker) 集成到 Hajimi King 项目中，提供稳定的代理服务。

## 🎯 为什么需要WARP代理？

- **避免IP封禁**: GitHub和Gemini API对高频访问有严格限制
- **提高成功率**: WARP提供稳定的Cloudflare网络出口
- **全球加速**: 通过Cloudflare的全球网络优化访问速度
- **简单集成**: 一键部署，无需额外配置

## 🚀 快速开始

### 方法一：使用一键部署脚本（推荐）

```bash
# 进入项目目录
cd hajimi-king

# 运行一键部署脚本
./deploy-with-warp.sh
```

脚本会自动：
- 检查Docker环境
- 创建必要的目录和配置文件
- 启动WARP代理和Hajimi King服务
- 验证代理连接状态

### 方法二：手动部署

1. **准备配置文件**
```bash
# 复制环境变量配置
cp env.example .env

# 编辑配置文件，填入GitHub Token
nano .env
```

2. **创建数据目录**
```bash
mkdir -p data/keys data/logs warp-data
```

3. **创建查询配置文件**
```bash
echo "AIzaSy in:file" > data/queries.txt
```

4. **启动服务**
```bash
docker compose up -d
```

## 📋 服务架构

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Hajimi King   │───▶│   WARP Proxy     │───▶│  Internet       │
│   (主服务)       │    │   (代理服务)      │    │  (GitHub/Gemini)│
└─────────────────┘    └──────────────────┘    └─────────────────┘
      :8080                   :1080                    :443
```

- **Hajimi King**: 主要的密钥搜索服务
- **WARP Proxy**: 提供SOCKS5/HTTP代理，端口1080
- **网络**: 两个服务通过Docker网络通信

## ⚙️ 配置说明

### 环境变量配置

在 `.env` 文件中的关键配置：

```bash
# 必填：GitHub Token
GITHUB_TOKENS=ghp_your_token_here

# 代理配置（使用集成的WARP服务）
PROXY=http://warp:1080

# 其他可选配置...
```

### Docker Compose 配置特点

- **网络隔离**: 使用专用Docker网络 `hajimi-network`
- **健康检查**: WARP服务包含健康检查，确保代理正常工作
- **依赖管理**: Hajimi King等待WARP服务健康后才启动
- **数据持久化**: WARP配置数据持久化到 `./warp-data`

## 🔧 管理命令

```bash
# 查看服务状态
docker compose ps

# 查看日志
docker compose logs -f

# 查看特定服务日志
docker compose logs -f warp
docker compose logs -f hajimi-king

# 重启服务
docker compose restart

# 停止服务
docker compose down

# 完全清理（包括数据卷）
docker compose down -v
```

## 🧪 测试代理连接

### 测试WARP代理是否工作

```bash
# 从宿主机测试
curl --socks5-hostname 127.0.0.1:1080 https://cloudflare.com/cdn-cgi/trace

# 从容器内测试
docker compose exec warp curl --socks5-hostname 127.0.0.1:1080 https://cloudflare.com/cdn-cgi/trace
```

成功的输出应包含 `warp=on` 或 `warp=plus`。

### 测试Hajimi King是否使用代理

查看Hajimi King的日志，应该能看到通过代理访问的记录：

```bash
docker compose logs hajimi-king | grep -i proxy
```

## 📁 文件结构

```
hajimi-king/
├── docker-compose.yml          # 集成的Docker Compose配置
├── deploy-with-warp.sh         # 一键部署脚本
├── .env                        # 环境变量配置
├── data/                       # Hajimi King数据目录
│   ├── keys/                   # 发现的密钥文件
│   ├── logs/                   # 日志文件
│   └── queries.txt             # 搜索查询配置
└── warp-data/                  # WARP配置数据
```

## 🔍 故障排除

### WARP代理无法启动

1. **检查权限**: 确保Docker有足够权限创建TUN设备
2. **检查端口**: 确保1080端口未被占用
3. **查看日志**: `docker compose logs warp`

### Hajimi King无法连接代理

1. **检查网络**: 确保两个服务在同一Docker网络中
2. **检查代理配置**: 确认 `.env` 中 `PROXY=http://warp:1080`
3. **检查服务依赖**: 确认WARP服务健康检查通过

### 性能优化

1. **调整WARP_SLEEP**: 如果服务器性能较差，增加等待时间
2. **使用WARP+**: 如果有订阅，在环境变量中设置 `WARP_LICENSE_KEY`
3. **监控资源**: 使用 `docker stats` 监控容器资源使用

## 🔒 安全注意事项

- **网络隔离**: 服务运行在隔离的Docker网络中
- **最小权限**: 只暴露必要的端口（1080）
- **数据保护**: 敏感数据存储在持久化卷中
- **定期更新**: 定期更新镜像以获取安全补丁

## 📚 相关链接

- [warp-docker 项目](https://github.com/cmj2002/warp-docker)
- [Cloudflare WARP 官方文档](https://*******/)
- [Docker Compose 文档](https://docs.docker.com/compose/)
- [GitHub API 文档](https://docs.github.com/en/rest)

## 💡 高级配置

### 启用NAT模式

如果需要路由L3流量，可以启用NAT模式：

```yaml
# 在docker-compose.yml中的warp服务添加
environment:
  - WARP_ENABLE_NAT=1
sysctls:
  - net.ipv4.ip_forward=1
  - net.ipv6.conf.all.forwarding=1
  - net.ipv6.conf.all.accept_ra=2
```

### 使用WARP+订阅

如果有WARP+订阅，可以获得更好的性能：

```bash
# 在.env文件中添加
WARP_LICENSE_KEY=your_warp_plus_key_here
```

### 自定义GOST参数

如果需要UDP支持或其他高级功能：

```yaml
# 在docker-compose.yml中的warp服务添加
environment:
  - GOST_ARGS=-L :1080 -L udp://:1081
```

---

🎉 **享受使用集成WARP代理的Hajimi King！** 如有问题，请查看日志或提交Issue。
