version: '3.8'

networks:
  hajimi-network:
    driver: bridge

services:
  # Cloudflare WARP 代理服务
  warp:
    image: caomingjun/warp
    container_name: hajimi-warp
    restart: always
    # add removed rule back (https://github.com/opencontainers/runc/pull/3468)
    device_cgroup_rules:
      - 'c 10:200 rwm'
    ports:
      - "1080:1080"  # SOCKS5/HTTP 代理端口
    environment:
      - WARP_SLEEP=2
      # - WARP_LICENSE_KEY= # 可选：如果有WARP+订阅，可以填入密钥
      # - WARP_ENABLE_NAT=1 # 启用NAT模式
    cap_add:
      # Docker already have them, these are for podman users
      - MKNOD
      - AUDIT_WRITE
      # additional required cap for warp, both for podman and docker
      - NET_ADMIN
    sysctls:
      - net.ipv6.conf.all.disable_ipv6=0
      - net.ipv4.conf.all.src_valid_mark=1
      # uncomment for nat
      # - net.ipv4.ip_forward=1
      # - net.ipv6.conf.all.forwarding=1
      # - net.ipv6.conf.all.accept_ra=2
    volumes:
      - ./warp-data:/var/lib/cloudflare-warp
    networks:
      - hajimi-network
    healthcheck:
      test: ["CMD", "curl", "--socks5-hostname", "127.0.0.1:1080", "https://cloudflare.com/cdn-cgi/trace"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Hajimi King 主服务
  hajimi-king:
    image: ghcr.io/gakkinoone/hajimi-king:latest
    container_name: hajimi-king
    restart: unless-stopped
    env_file:
      - .env
    environment:
      # 使用WARP代理，通过服务名访问
      - PROXY=http://warp:1080
    volumes:
      - ./data:/app/data
    working_dir: /app
    networks:
      - hajimi-network
    depends_on:
      warp:
        condition: service_healthy