#!/bin/bash

# Gemini Balancer 同步功能测试脚本

set -e

echo "🔗 Gemini Balancer 同步功能测试"
echo "================================="

# 检查服务是否运行
echo "📊 检查服务状态..."
if ! docker compose ps | grep -q "Up"; then
    echo "❌ 服务未运行，请先执行: docker compose up -d"
    exit 1
fi

echo "✅ 服务正在运行"

# 检查环境变量配置
echo ""
echo "⚙️ 检查Gemini Balancer配置..."
echo "环境变量配置:"
docker compose exec -T hajimi-king printenv | grep GEMINI | while read line; do
    if [[ $line == *"AUTH"* ]]; then
        # 隐藏认证信息的敏感部分
        echo "  $line" | sed 's/\(.*AUTH=\).*/\1****/'
    else
        echo "  $line"
    fi
done

# 检查配置文件
echo ""
echo "📋 检查.env配置文件..."
if [ -f ".env" ]; then
    echo "✅ .env 文件存在"
    if grep -q "GEMINI_BALANCER_SYNC_ENABLED=true" .env; then
        echo "✅ Gemini Balancer同步已启用"
    else
        echo "❌ Gemini Balancer同步未启用"
    fi
    
    if grep -q "GEMINI_BALANCER_URL=.*[^[:space:]]" .env; then
        balancer_url=$(grep "GEMINI_BALANCER_URL=" .env | cut -d'=' -f2)
        echo "✅ Balancer URL已配置: $balancer_url"
    else
        echo "❌ Balancer URL未配置"
    fi
    
    if grep -q "GEMINI_BALANCER_AUTH=.*[^[:space:]]" .env; then
        echo "✅ Balancer认证已配置"
    else
        echo "❌ Balancer认证未配置"
    fi
else
    echo "❌ .env 配置文件不存在"
fi

# 检查启动日志中的同步配置
echo ""
echo "📝 检查启动日志中的同步配置..."
sync_logs=$(docker compose logs hajimi-king | grep -E "(Gemini.*Balancer|sync|SYNC)" | head -10)
if [ -n "$sync_logs" ]; then
    echo "✅ 找到同步相关日志:"
    echo "$sync_logs" | while read line; do
        echo "  $line"
    done
else
    echo "⚠️  未找到同步相关日志"
fi

# 检查同步队列状态
echo ""
echo "📊 检查同步队列状态..."
queue_logs=$(docker compose logs hajimi-king | grep "Queue status" | tail -3)
if [ -n "$queue_logs" ]; then
    echo "✅ 队列状态日志:"
    echo "$queue_logs" | while read line; do
        echo "  $line"
    done
else
    echo "⚠️  未找到队列状态日志"
fi

# 检查批量发送日志
echo ""
echo "📤 检查批量发送日志..."
send_logs=$(docker compose logs hajimi-king | grep "batch sending" | tail -5)
if [ -n "$send_logs" ]; then
    echo "✅ 批量发送日志:"
    echo "$send_logs" | while read line; do
        echo "  $line"
    done
else
    echo "⚠️  未找到批量发送日志"
fi

# 检查已发现的有效密钥
echo ""
echo "🔑 检查已发现的有效密钥..."
if ls data/keys/keys_valid_*.txt 1> /dev/null 2>&1; then
    valid_count=$(cat data/keys/keys_valid_*.txt 2>/dev/null | wc -l || echo "0")
    echo "✅ 发现 $valid_count 个有效密钥"
    
    if [ "$valid_count" -gt 0 ]; then
        echo "最新的有效密钥示例（前缀）:"
        cat data/keys/keys_valid_*.txt 2>/dev/null | tail -3 | sed 's/\(AIzaSy.\{10\}\).*/\1.../' | while read key; do
            echo "  $key"
        done
    fi
else
    echo "⚠️  暂未发现有效密钥文件"
fi

# 检查同步发送记录
echo ""
echo "📨 检查同步发送记录..."
if ls data/keys/keys_send_*.txt 1> /dev/null 2>&1; then
    send_count=$(cat data/keys/keys_send_*.txt 2>/dev/null | wc -l || echo "0")
    echo "✅ 已发送 $send_count 个密钥到外部服务"
    
    if [ "$send_count" -gt 0 ]; then
        echo "已发送的密钥示例（前缀）:"
        cat data/keys/keys_send_*.txt 2>/dev/null | tail -3 | sed 's/\(AIzaSy.\{10\}\).*/\1.../' | while read key; do
            echo "  $key"
        done
    fi
else
    echo "⚠️  暂无同步发送记录"
fi

# 测试网络连接
echo ""
echo "🌐 测试到Gemini Balancer的网络连接..."
balancer_url=$(grep "GEMINI_BALANCER_URL=" .env | cut -d'=' -f2)
if [ -n "$balancer_url" ]; then
    echo "测试连接到: $balancer_url"
    if timeout 10 curl -s -I "$balancer_url" >/dev/null 2>&1; then
        echo "✅ 网络连接正常"
    else
        echo "⚠️  网络连接测试失败，请检查URL和网络"
    fi
else
    echo "⚠️  未配置Balancer URL，跳过网络测试"
fi

# 总结
echo ""
echo "🎯 测试总结"
echo "==========="
echo "Gemini Balancer同步功能配置检查完成。"
echo ""
echo "📋 配置要求："
echo "   ✅ GEMINI_BALANCER_SYNC_ENABLED=true"
echo "   ✅ GEMINI_BALANCER_URL=http://152.53.166.18:38000"
echo "   ✅ GEMINI_BALANCER_AUTH=sk-redkaytop_success"
echo ""
echo "🔄 工作原理："
echo "   1. Hajimi King发现有效的Gemini API密钥"
echo "   2. 自动将密钥添加到同步队列"
echo "   3. 定期批量发送到配置的Gemini Balancer服务"
echo "   4. 记录发送状态到 keys_send_*.txt 文件"
echo ""
echo "📊 监控建议："
echo "   - 查看实时日志: docker compose logs -f hajimi-king"
echo "   - 检查有效密钥: cat data/keys/keys_valid_*.txt"
echo "   - 检查发送记录: cat data/keys/keys_send_*.txt"
echo ""
echo "🎉 测试完成！"
