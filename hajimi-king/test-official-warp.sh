#!/bin/bash

# 官方 Hajimi King + WARP 集成测试脚本

set -e

echo "🧪 官方 Hajimi King + WARP 集成测试"
echo "===================================="

# 检查服务是否运行
echo "📊 检查服务状态..."
if ! docker compose ps | grep -q "Up"; then
    echo "❌ 服务未运行，请先执行: docker compose up -d"
    exit 1
fi

echo "✅ Docker Compose 服务正在运行"

# 检查WARP容器状态
echo ""
echo "🔍 检查WARP代理状态..."
if docker compose ps warp | grep -q "Up"; then
    echo "✅ WARP容器正在运行"
else
    echo "❌ WARP容器未运行"
    exit 1
fi

# 检查Haji<PERSON> King容器状态
echo ""
echo "🔍 检查Hajimi King状态..."
if docker compose ps hajimi-king | grep -q "Up"; then
    echo "✅ Hajimi King容器正在运行"
else
    echo "❌ <PERSON><PERSON><PERSON> King容器未运行"
    exit 1
fi

# 测试WARP代理连接
echo ""
echo "🌐 测试WARP代理连接..."
if timeout 10 docker compose exec -T warp curl --socks5-hostname 127.0.0.1:1080 https://cloudflare.com/cdn-cgi/trace 2>/dev/null | grep -q "warp=on\|warp=plus"; then
    echo "✅ WARP代理工作正常"
    WARP_STATUS=$(docker compose exec -T warp curl --socks5-hostname 127.0.0.1:1080 https://cloudflare.com/cdn-cgi/trace 2>/dev/null | grep "warp=" | cut -d'=' -f2)
    echo "   WARP状态: $WARP_STATUS"
else
    echo "⚠️  WARP代理连接测试失败，可能还在启动中"
    echo "   请稍后重试或检查日志: docker compose logs warp"
fi

# 测试从宿主机访问代理
echo ""
echo "🔌 测试宿主机代理访问..."
if timeout 10 curl --socks5-hostname 127.0.0.1:1080 https://cloudflare.com/cdn-cgi/trace 2>/dev/null | grep -q "warp=on\|warp=plus"; then
    echo "✅ 宿主机可以正常访问WARP代理"
else
    echo "⚠️  宿主机无法访问WARP代理，请检查端口映射"
fi

# 检查网络连通性
echo ""
echo "🔗 检查容器间网络连通性..."
if docker compose exec -T hajimi-king curl -I http://warp:1080 2>/dev/null | grep -q "HTTP"; then
    echo "✅ Hajimi King可以访问WARP服务"
else
    echo "❌ Hajimi King无法访问WARP服务，请检查网络配置"
fi

# 检查配置文件
echo ""
echo "📋 检查配置文件..."
if [ -f ".env" ]; then
    echo "✅ .env 配置文件存在"
    if grep -q "PROXY=.*warp:1080" .env; then
        echo "✅ 代理配置正确指向WARP服务"
    else
        echo "⚠️  代理配置可能不正确，请检查 .env 文件中的 PROXY 设置"
    fi
    
    if grep -q "GITHUB_TOKENS=.*[^[:space:]]" .env; then
        token_count=$(grep "GITHUB_TOKENS=" .env | cut -d'=' -f2 | tr ',' '\n' | wc -l)
        echo "✅ GitHub Token已配置 ($token_count 个)"
    else
        echo "⚠️  GitHub Token未配置，请在 .env 文件中设置 GITHUB_TOKENS"
    fi
else
    echo "❌ .env 配置文件不存在"
fi

# 检查数据目录
echo ""
echo "📁 检查数据目录..."
for dir in "data" "data/keys" "data/logs" "warp-data"; do
    if [ -d "$dir" ]; then
        echo "✅ $dir 目录存在"
    else
        echo "⚠️  $dir 目录不存在"
    fi
done

# 检查查询配置文件
if [ -f "data/queries.txt" ]; then
    echo "✅ queries.txt 配置文件存在"
    query_count=$(grep -v "^#" data/queries.txt | grep -v "^$" | wc -l)
    echo "   配置了 $query_count 个搜索查询"
else
    echo "⚠️  queries.txt 配置文件不存在"
fi

# 检查镜像版本
echo ""
echo "🐳 检查Docker镜像..."
hajimi_image=$(docker compose ps hajimi-king --format "table {{.Image}}" | tail -n +2)
echo "✅ Hajimi King镜像: $hajimi_image"

warp_image=$(docker compose ps warp --format "table {{.Image}}" | tail -n +2)
echo "✅ WARP镜像: $warp_image"

# 显示最近的日志
echo ""
echo "📝 最近的服务日志（最后5行）:"
echo "--- WARP 日志 ---"
docker compose logs --tail=5 warp 2>/dev/null || echo "无法获取WARP日志"

echo ""
echo "--- Hajimi King 日志 ---"
docker compose logs --tail=5 hajimi-king 2>/dev/null || echo "无法获取Hajimi King日志"

# 统计发现的密钥
echo ""
echo "📊 密钥发现统计..."
valid_keys=0
rate_limited_keys=0

if ls data/keys/keys_valid_*.txt 1> /dev/null 2>&1; then
    valid_keys=$(cat data/keys/keys_valid_*.txt 2>/dev/null | wc -l || echo "0")
fi

if ls data/keys/key_429_*.txt 1> /dev/null 2>&1; then
    rate_limited_keys=$(cat data/keys/key_429_*.txt 2>/dev/null | wc -l || echo "0")
fi

echo "🔑 发现的密钥:"
echo "   有效密钥: $valid_keys"
echo "   限流密钥: $rate_limited_keys"

# 总结
echo ""
echo "🎯 测试总结"
echo "==========="
echo "如果所有测试都通过，说明官方版本集成配置正确。"
echo "如果有警告或错误，请根据提示进行修复。"
echo ""
echo "📋 常用管理命令："
echo "   查看实时日志: docker compose logs -f"
echo "   重启服务:     docker compose restart"
echo "   停止服务:     docker compose down"
echo ""
echo "🌐 手动测试代理："
echo "   curl --socks5-hostname 127.0.0.1:1080 https://cloudflare.com/cdn-cgi/trace"
echo ""
echo "🎉 测试完成！"
