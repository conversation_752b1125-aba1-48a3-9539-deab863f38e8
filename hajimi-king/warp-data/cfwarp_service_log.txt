2025-07-28T08:49:37.802Z  INFO warp::warp_service: Starting WarpService pid=20
2025-07-28T08:49:37.803Z  INFO warp::warp_service: Version: 2025.5.943.0
2025-07-28T08:49:37.837Z DEBUG warp_settings::raw_settings: Loading settings from file "/var/lib/cloudflare-warp/settings.json"
2025-07-28T08:49:37.837Z  INFO warp_settings::manager: User preferences not loaded e=No such file or directory (os error 2)
2025-07-28T08:49:37.837Z DEBUG warp_settings::raw_settings: Loading consumer settings from file "/var/lib/cloudflare-warp/consumer-settings.json"
2025-07-28T08:49:37.837Z  INFO warp_settings::manager: Consumer preferences not loaded e=No such file or directory (os error 2)
2025-07-28T08:49:37.837Z DEBUG warp_settings::manager::final_overrides: Loading final overrides settings from file "/var/lib/cloudflare-warp/final-overrides-settings.json"
2025-07-28T08:49:37.837Z  INFO warp_settings::manager: Final override settings not loaded e=No such file or directory (os error 2)
2025-07-28T08:49:37.838Z DEBUG warp_settings::manager: Starting local policy file watch parent_path="/var/lib/cloudflare-warp"
2025-07-28T08:49:37.953Z DEBUG warp_api_client::addresses: Initializing base API endpoint settings consumer=ConsumerEndpointConfig { ips: [*************, ************, 2606:4700::6810:1854, 2606:4700::6810:c052], api_sni: ApiSni("api.cloudflareclient.com.") } zt=ZeroTrustEndpointConfig { ips: [***************, ***************, 2606:4700:7::a29f:8a69, 2606:4700:7::a29f:8969], api_sni: ApiSni("zero-trust-client.cloudflareclient.com."), notifications_sni: NotificationsSni("notifications.cloudflareclient.com") } compliance_environment=Normal
2025-07-28T08:49:37.959Z  WARN network_info::linux::dns_manager::dns_owner: Could not determine resolv.conf file owner: No comment line contained an owner slug
2025-07-28T08:49:37.979Z DEBUG warp::warp_service: Main loop refactor feature enabled
2025-07-28T08:49:37.979Z DEBUG watchdog: warp::watchdog: Kicking off watchdog
2025-07-28T08:49:37.981Z DEBUG actor_watchdog: Initializing watchdog actor
2025-07-28T08:49:37.981Z DEBUG actor_alternate_network: Initializing alternate network actor
2025-07-28T08:49:37.984Z DEBUG actor_main_loop_compat: Initializing main loop compat actor
2025-07-28T08:49:37.985Z DEBUG actor_statistics: Initializing statistics actor
2025-07-28T08:49:37.987Z DEBUG actor_connectivity: Initializing connectivity actor
2025-07-28T08:49:37.989Z DEBUG actor_remote_dex_command: Initializing remote DEX command actor
2025-07-28T08:49:37.992Z DEBUG actor_device_state: Initializing device state actor
2025-07-28T08:49:37.995Z DEBUG actor_emergency_disconnect: Initializing emergency disconnect actor
2025-07-28T08:49:37.997Z DEBUG actor_registration_verifier: Initializing registration verifier actor
2025-07-28T08:49:37.998Z DEBUG actor_root_ca: Initializing root-ca actor
2025-07-28T08:49:37.999Z DEBUG actor_root_ca: Sleeping for 15s to allow main loop boot-up
2025-07-28T08:49:38.003Z  INFO main_loop: warp_net::ipc: Bound ipc socket name="cloudflare-warp/warp_service" path="/run/cloudflare-warp/warp_service"
2025-07-28T08:49:38.006Z  INFO main_loop: warp::warp_service: Warp IPC listening on "cloudflare-warp/warp_service"
2025-07-28T08:49:38.006Z  INFO firewall::engine: Firewall engine running
2025-07-28T08:49:38.013Z DEBUG network_info::linux::power_notifier: Initializing dbus connection
2025-07-28T08:49:38.013Z  WARN main_loop: warp::warp_service: Failed to load Registration error=OsError(Os { code: 2, kind: NotFound, message: "No such file or directory" })
2025-07-28T08:49:38.013Z DEBUG warp_api_client::client::ops: Sending API request GET api.cloudflareclient.com./v0/client_config
2025-07-28T08:49:38.013Z DEBUG warp_api_client::client::ops: Sending API request GET zero-trust-client.cloudflareclient.com./v0/client_config
2025-07-28T08:49:38.059Z DEBUG run: warp_settings::manager: LayerManager update: NetworkDefaults(NetworkDefaults { split_config: Some(Exclude { ips: [(10.0.0.0/8, None), (**********/10, None), (***********/16, None), (**********/12, None), (*********/24, None), (***********/16, None), (*********/24, None), (240.0.0.0/4, None), (***************/32, None), (***************/32, None), (fe80::/10, None), (fd00::/8, None), (ff01::/16, None), (ff02::/16, None), (ff03::/16, None), (ff04::/16, None), (ff05::/16, None), (fc00::/7, None), (**********/16, None), (**********/16, None), (***********/22, None), (************/18, None), (***********/23, None), (2620:149:a44::/48, None), (2403:300:a42::/48, None), (2403:300:a51::/48, None), (2a01:b740:a42::/48, None)], hosts: [] }) })
2025-07-28T08:49:38.060Z DEBUG main_loop: warp::warp_service: Checking for registration vs settings account mismatch reg_is_teams=false settings_is_teams=false reg_org=None settings_org=None
2025-07-28T08:49:38.061Z  INFO firewall::engine: Firewall unloaded
2025-07-28T08:49:38.062Z  INFO main_loop: warp::warp_service: New User Settings
2025-07-28T08:49:38.062Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC update: SettingsUpdated
2025-07-28T08:49:38.062Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseUpdate: SettingsUpdated
2025-07-28T08:49:38.063Z DEBUG main_loop: warp::warp_service: update_settings: no restart required
2025-07-28T08:49:38.063Z  INFO warp::warp_service: Spawning registration changed IPC loop
2025-07-28T08:49:38.063Z  INFO main_loop: warp::warp_service: WARP status: Unable(RegistrationMissing(ManualDeletion))
2025-07-28T08:49:38.063Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T08:49:38.063Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Unable(RegistrationMissing(ManualDeletion))
2025-07-28T08:49:38.063Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Unable(RegistrationMissing(ManualDeletion))
2025-07-28T08:49:38.063Z DEBUG main_loop: warp::warp_service: Checking for registration vs settings account mismatch reg_is_teams=false settings_is_teams=false reg_org=None settings_org=None
2025-07-28T08:49:38.064Z  INFO firewall::engine: Firewall unloaded
2025-07-28T08:49:38.064Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=641.01µs
2025-07-28T08:49:38.064Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="settings_changed"
2025-07-28T08:49:38.064Z  INFO main_loop: warp::warp_service: User Settings Changed: {}
2025-07-28T08:49:38.064Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC update: SettingsUpdated
2025-07-28T08:49:38.064Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseUpdate: SettingsUpdated
2025-07-28T08:49:38.064Z DEBUG main_loop: warp::warp_service: update_settings: no restart required
2025-07-28T08:49:38.064Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="settings_changed" elapsed=627.013µs
2025-07-28T08:49:39.681Z  WARN warp_net::ipc::unix: Failed to get path for client error="return code = -1, errno = 13, message = 'Permission denied'" pid=50
2025-07-28T08:49:39.681Z  WARN warp_net::ipc::unix: Failed to get path for client error="return code = -1, errno = 13, message = 'Permission denied'" pid=50
2025-07-28T08:49:39.681Z  INFO warp::warp_service::ipc_loop: IPC: new connection exec_context=Regular process_name="<Unknown>" pid=50
2025-07-28T08:49:39.682Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc request: 8a4daa5c-00ce-4c86-8344-8cea6c137dca; GetAppSettings
2025-07-28T08:49:39.682Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="handle_ipc_request"
2025-07-28T08:49:39.683Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="handle_ipc_request" elapsed=191.274µs
2025-07-28T08:49:39.683Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc request: 39682bef-5e7a-467a-b996-1258fffd44a3; GetRegistrationInfo
2025-07-28T08:49:39.683Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc response: 39682bef-5e7a-467a-b996-1258fffd44a3; RegistrationInfo: None
2025-07-28T08:49:39.683Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="handle_ipc_request"
2025-07-28T08:49:39.683Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="handle_ipc_request" elapsed=119.247µs
2025-07-28T08:49:39.684Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc request: 658e88ef-fa4c-447b-b951-87834d4d99bd; NewRegistration
2025-07-28T08:49:39.684Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="handle_ipc_request"
2025-07-28T08:49:39.684Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC update: RegistrationInProgress
2025-07-28T08:49:39.684Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseUpdate: RegistrationInProgress
2025-07-28T08:49:39.684Z DEBUG warp::warp_service::ipc_handlers: Sending IPC update: ConfigurationUpdated
2025-07-28T08:49:39.684Z DEBUG warp::warp_service::ipc_handlers: Ipc Broadcast ResponseUpdate: ConfigurationUpdated
2025-07-28T08:49:39.684Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="handle_ipc_request" elapsed=317.284µs
2025-07-28T08:49:39.684Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="registration_change"
2025-07-28T08:49:39.684Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="registration_change" elapsed=454.755µs
2025-07-28T08:49:39.685Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="main_loop_compat_rx"
2025-07-28T08:49:39.685Z  INFO main_loop:handle_update{update=AlternateNetworkChanged(None)}: warp::warp_service::actor_compat: Detected alternate network name=None
2025-07-28T08:49:39.685Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "device-state.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "root-ca.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "root-ca.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "emergency-disconnect.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "main-loop-compat.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "alternate-network.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "alternate-network.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "watchdog.updates_recv", labels: {}, count: 0 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "device-state.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "connectivity.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "registration-verifier.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "emergency-disconnect.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "watchdog.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "root-ca.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "remote-dex-command.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "connectivity.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "statistics.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "message_bus.bus_driver_request_timeouts", labels: {}, count: 0 }, CounterPayload { name: "remote-dex-command.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "connectivity.updates_recv", labels: {}, count: 0 }, CounterPayload { name: "remote-dex-command.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 18 }, CounterPayload { name: "remote-dex-command.updates_recv", labels: {}, count: 0 }, CounterPayload { name: "statistics.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "watchdog.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "alternate-network.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 0 }, CounterPayload { name: "emergency-disconnect.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "statistics.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "registration-verifier.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "connectivity.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "root-ca.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "emergency-disconnect.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 0 }], summaries: [SummaryPayload { name: "watchdog.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "statistics.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "main-loop-compat.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "connectivity.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "emergency-disconnect.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "device-state.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "alternate-network.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.026131558, count: 18 }, SummaryPayload { name: "root-ca.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "remote-dex-command.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "registration-verifier.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }], gauges: [GaugesPayload { name: "message_bus.bus_driver_current_actors", labels: {}, value: 10.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 0.000537192 }] } context="registration change"}: actor_statistics::handler: Starting to upload stats
2025-07-28T08:49:39.685Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "device-state.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "root-ca.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "root-ca.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "emergency-disconnect.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "main-loop-compat.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "alternate-network.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "alternate-network.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "watchdog.updates_recv", labels: {}, count: 0 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "device-state.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "connectivity.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "registration-verifier.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "emergency-disconnect.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "watchdog.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "root-ca.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "remote-dex-command.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "connectivity.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "statistics.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "message_bus.bus_driver_request_timeouts", labels: {}, count: 0 }, CounterPayload { name: "remote-dex-command.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "connectivity.updates_recv", labels: {}, count: 0 }, CounterPayload { name: "remote-dex-command.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 18 }, CounterPayload { name: "remote-dex-command.updates_recv", labels: {}, count: 0 }, CounterPayload { name: "statistics.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "watchdog.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "alternate-network.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 0 }, CounterPayload { name: "emergency-disconnect.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "statistics.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "registration-verifier.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "connectivity.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "root-ca.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "emergency-disconnect.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 0 }], summaries: [SummaryPayload { name: "watchdog.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "statistics.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "main-loop-compat.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "connectivity.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "emergency-disconnect.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "device-state.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "alternate-network.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.026131558, count: 18 }, SummaryPayload { name: "root-ca.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "remote-dex-command.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "registration-verifier.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }], gauges: [GaugesPayload { name: "message_bus.bus_driver_current_actors", labels: {}, value: 10.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 0.000537192 }] } context="registration change"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-07-28T08:49:39.685Z  INFO actor_statistics::handler: Switched to new registration for stats. All stats have been reset. new_registration_id=None
2025-07-28T08:49:39.685Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="main_loop_compat_rx" elapsed=555.256µs
2025-07-28T08:49:39.690Z ERROR utilities::device_info::linux: Failed to load SMBiosData error=Os { code: 2, kind: NotFound, message: "No such file or directory" }
2025-07-28T08:49:39.690Z DEBUG warp_api_client::apis::registration: New registration auth_method=Consumer
2025-07-28T08:49:39.691Z DEBUG warp_api_client::client: Sending API request POST https://api.cloudflareclient.com.//v0/reg
2025-07-28T08:49:40.885Z DEBUG warp_api_endpoints::payloads: Warp endpoint override ports ports=[2408, 500, 1701, 4500]
2025-07-28T08:49:40.886Z DEBUG main_loop: warp_api_client::executor: Returning API response for NewRegistration request_id=658e88ef-fa4c-447b-b951-87834d4d99bd
2025-07-28T08:49:40.886Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="api_responses"
2025-07-28T08:49:40.886Z DEBUG run: warp_settings::manager: LayerManager update: NetworkPolicy(Some(NetworkPolicySettings { onboarding: None, operation_mode: None, disable_auto_fallback: None, fallback_domains: None, proxy_port: None, split_config: None, gateway_id: None, support_url: None, allow_mode_switch: None, switch_locked: None, auto_connect: None, captive_portal: None, organization: None, allow_updates: None, allowed_to_leave: None, profile_id: None, lan_allow_settings: Some(LanAllowSettings { minutes: None, subnet_size: SubnetSize(24) }), warp_tunnel_protocol: Some(Masque), masque_settings: None, register_interface_ip_with_dns: None, anycast_split_ips: Some(AnycastSplitIps { always_exclude: [*************, 2606:4700:102::3], always_include: [*************, 2606:4700:102::4] }), firewall_scope: None, sccm_vpn_boundary_support: None, enable_post_quantum: None }))
2025-07-28T08:49:40.886Z DEBUG main_loop: warp::warp_service::api_handlers: Registration updated current_registration=NewInProgress disposition=UpdateOnly
2025-07-28T08:49:40.886Z DEBUG main_loop: warp::warp_service::api_handlers: Invalidating existing posture check scheduler on registration update
2025-07-28T08:49:40.887Z DEBUG main_loop: warp::warp_service::api_handlers: Registration updated 0.00 hours old, refresh at 2025-07-29 8:49:40.885977319 +00:00:00
Registration: 2d6c00fe-5a6c-46fb-a62a-b9aa5ed62ace
Device Identifiers: System User
Auth Method: Consumer
Public key: ffc414312ca52e3bdb46500ba131d8155f2a0984d79183b99c16703b960fb558
Interface: WarpEndpoint { v4: **********, v6: 2606:4700:110:88a6:60d3:2342:19a:b4a2 }
Endpoints: [WarpSocketAddrPair { v4: *************:2408, v6: [2606:4700:d0::a29f:c003]:2408 }, WarpSocketAddrPair { v4: *************:500, v6: [2606:4700:d0::a29f:c003]:500 }, WarpSocketAddrPair { v4: *************:1701, v6: [2606:4700:d0::a29f:c003]:1701 }, WarpSocketAddrPair { v4: *************:4500, v6: [2606:4700:d0::a29f:c003]:4500 }]
Subnet CIDRS: None

Account: Free { id: AccountId(38d2eb15-31ea-470f-92a9-3c26b5351c42), license: "94e5DJS8-Wqh617X9-78cjuC59" }

2025-07-28T08:49:40.887Z  INFO main_loop: warp_storage::registration::registration_storage: Saving registration to active cache config_name=None username="WARPSecret"
2025-07-28T08:49:40.888Z DEBUG warp::warp_service::ipc_handlers: Sending IPC update: ConfigurationUpdated
2025-07-28T08:49:40.888Z DEBUG warp::warp_service::ipc_handlers: Ipc Broadcast ResponseUpdate: ConfigurationUpdated
2025-07-28T08:49:40.888Z DEBUG main_loop: warp::warp::controller: Stopping WarpConnection state=Disconnected
2025-07-28T08:49:40.888Z  INFO firewall::engine: Firewall unloaded
2025-07-28T08:49:40.888Z DEBUG main_loop: warp::warp_service: Determining disconnected reason from connectivity state net_info=IPv4: [eth0; **********; Ethernet; Gateway: Some(**********)], DNS servers:,  127.0.0.11:53
 power_state=None disconnect_reason=None
2025-07-28T08:49:40.888Z  WARN main_loop: warp::warp_service: Disconnecting, but reason is unknown
2025-07-28T08:49:40.889Z DEBUG main_loop: warp::warp_service: Checking for registration vs settings account mismatch reg_is_teams=false settings_is_teams=false reg_org=None settings_org=None
2025-07-28T08:49:40.889Z  INFO firewall::engine: Firewall unloaded
2025-07-28T08:49:40.889Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "registration-verifier.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 12 }, CounterPayload { name: "emergency-disconnect.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 2 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "root-ca.updates_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.*********, count: 12 }], gauges: [] } context="registration change"}: actor_statistics::handler: Starting to upload stats
2025-07-28T08:49:40.889Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "registration-verifier.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 12 }, CounterPayload { name: "emergency-disconnect.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 2 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "root-ca.updates_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.*********, count: 12 }], gauges: [] } context="registration change"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-07-28T08:49:40.889Z  INFO actor_statistics::handler: Switched to new registration for stats. All stats have been reset. new_registration_id=None
2025-07-28T08:49:40.889Z  INFO main_loop: warp::warp_service: User Settings Changed: {warp_tunnel_protocol: "wireguard"->"masque", anycast_split_ips.always_exclude: Added: [{"ip":"*************"},{"ip":"2606:4700:102::3"}] , anycast_split_ips.always_include: Added: [{"ip":"*************"},{"ip":"2606:4700:102::4"}] , }
2025-07-28T08:49:40.889Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC update: SettingsUpdated
2025-07-28T08:49:40.889Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseUpdate: SettingsUpdated
2025-07-28T08:49:40.889Z  INFO main_loop: warp::warp_service: New user settings modify the tunnel protocol, rotating keys new_settings.warp_tunnel_protocol=Masque new_key_data=RegistrationTunnelKeys { key_type: NistP256, tunnel_type: Masque }
2025-07-28T08:49:40.889Z DEBUG main_loop: warp_api_client::executor: Spawning API request for RotateKeys request_id=e6f31b83-ba62-43bb-a480-1e0bc1fbc36d
2025-07-28T08:49:40.889Z DEBUG main_loop: warp::warp_service: Determining disconnected reason from connectivity state net_info=IPv4: [eth0; **********; Ethernet; Gateway: Some(**********)], DNS servers:,  127.0.0.11:53
 power_state=None disconnect_reason=Some(SettingsChanged)
2025-07-28T08:49:40.889Z  INFO main_loop: warp::warp_service: Disconnecting due to settings change
2025-07-28T08:49:40.889Z  INFO main_loop: warp::warp_service: Disconnecting after settings change disconnected_reason=Disconnected(SettingsChanged)
2025-07-28T08:49:40.889Z DEBUG main_loop: warp::warp::controller: Stopping WarpConnection state=Disconnected
2025-07-28T08:49:40.890Z  INFO firewall::engine: Firewall unloaded
2025-07-28T08:49:40.890Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc response: 658e88ef-fa4c-447b-b951-87834d4d99bd; Success
2025-07-28T08:49:40.890Z DEBUG warp_api_client::client: Sending API request PATCH https://api.cloudflareclient.com.//v0/reg/2d6c00fe-5a6c-46fb-a62a-b9aa5ed62ace
2025-07-28T08:49:40.890Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="api_responses" elapsed=4.287088ms
2025-07-28T08:49:40.890Z  INFO main_loop: warp::warp_service: WARP status: Disconnected(SettingsChanged)
2025-07-28T08:49:40.890Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Disconnected(SettingsChanged)
2025-07-28T08:49:40.890Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T08:49:40.890Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Disconnected(SettingsChanged)
2025-07-28T08:49:40.890Z DEBUG main_loop: warp::warp_service: Checking for registration vs settings account mismatch reg_is_teams=false settings_is_teams=false reg_org=None settings_org=None
2025-07-28T08:49:40.890Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=135.128µs
2025-07-28T08:49:40.890Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="settings_changed"
2025-07-28T08:49:40.890Z  INFO firewall::engine: Firewall unloaded
2025-07-28T08:49:40.891Z  INFO main_loop: warp::warp_service: User Settings Changed: {}
2025-07-28T08:49:40.891Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC update: SettingsUpdated
2025-07-28T08:49:40.891Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseUpdate: SettingsUpdated
2025-07-28T08:49:40.891Z  INFO main_loop: warp::warp_service: Settings and Registration determined that a key rotation was needed but there exists a pending API call that satisfies this constraint. Skipping for now.
2025-07-28T08:49:40.891Z  INFO warp::warp_service::ipc_loop: IPC connection ended
2025-07-28T08:49:40.891Z DEBUG main_loop: warp::warp_service: update_settings: no restart required
2025-07-28T08:49:40.891Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="settings_changed" elapsed=818.888µs
2025-07-28T08:49:40.891Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="registration_change"
2025-07-28T08:49:40.891Z DEBUG main_loop: warp::warp_service: Determining disconnected reason from connectivity state net_info=IPv4: [eth0; **********; Ethernet; Gateway: Some(**********)], DNS servers:,  127.0.0.11:53
 power_state=None disconnect_reason=None
2025-07-28T08:49:40.891Z  WARN main_loop: warp::warp_service: Disconnecting, but reason is unknown
2025-07-28T08:49:40.891Z  INFO main_loop: warp::warp_service: WARP status: Disconnected(Manual)
2025-07-28T08:49:40.891Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="registration_change" elapsed=221.531µs
2025-07-28T08:49:40.891Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Disconnected(Manual)
2025-07-28T08:49:40.891Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T08:49:40.891Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Disconnected(Manual)
2025-07-28T08:49:40.892Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=294.871µs
2025-07-28T08:49:40.898Z  WARN warp_net::ipc::unix: Failed to get path for client error="return code = -1, errno = 13, message = 'Permission denied'" pid=51
2025-07-28T08:49:40.898Z  WARN warp_net::ipc::unix: Failed to get path for client error="return code = -1, errno = 13, message = 'Permission denied'" pid=51
2025-07-28T08:49:40.898Z  INFO warp::warp_service::ipc_loop: IPC: new connection exec_context=Regular process_name="<Unknown>" pid=51
2025-07-28T08:49:40.899Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc request: 6edd164c-55bb-4773-a892-a60454b178e8; GetAppSettings
2025-07-28T08:49:40.899Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="handle_ipc_request"
2025-07-28T08:49:40.900Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="handle_ipc_request" elapsed=435.057µs
2025-07-28T08:49:40.900Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc request: 7c39b640-579f-4ec0-9166-8b51ce00871d; GetRegistrationInfo
2025-07-28T08:49:40.900Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="handle_ipc_request"
2025-07-28T08:49:40.900Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc response: 7c39b640-579f-4ec0-9166-8b51ce00871d; RegistrationInfo: Some(RegistrationInfo { id: Client(ConsumerRegistrationId(2d6c00fe-5a6c-46fb-a62a-b9aa5ed62ace)), device_id: PhysicalDeviceId(2d6c00fe-5a6c-46fb-a62a-b9aa5ed62ace), public_key: [255, 196, 20, 49, 44, 165, 46, 59, 219, 70, 80, 11, 161, 49, 216, 21, 95, 42, 9, 132, 215, 145, 131, 185, 156, 22, 112, 59, 150, 15, 181, 88], managed: false, account: Free { id: AccountId(38d2eb15-31ea-470f-92a9-3c26b5351c42), license: "94e5DJS8-Wqh617X9-78cjuC59" }, alternate_networks: None })
2025-07-28T08:49:40.901Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="handle_ipc_request" elapsed=772.169µs
2025-07-28T08:49:40.901Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc request: e3161fe3-2353-4f86-b281-ae5d00f684b0; SetAlwaysOn(true)
2025-07-28T08:49:40.901Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="handle_ipc_request"
2025-07-28T08:49:40.901Z  INFO main_loop: structlog: event="UserToggledV0" toggle="on" warp_colo="none" warp_metal="none" account_name="" account_id="38d2eb1531ea470f92a93c26b5351c42" hostname="9b0cfffe86d9" serial_number=""
2025-07-28T08:49:40.918Z DEBUG run: warp_settings::manager: LayerManager update: UserOverrides(<mutator>)
2025-07-28T08:49:40.918Z DEBUG run: warp_settings::manager: UserOverrides updated: UserOverrides { version: None, always_on: Some(true), operation_mode: None, disable_for_wifi: None, disable_for_ethernet: None, disable_for_networks: None, families: None, dns_log_until: FutureSystemTime(None), gateway_id: None, onboarding: None, organization: None, split_config: None, fallback_domains: None, proxy_port: None, disable_connectivity_checks: None, override_api_endpoint: None, override_doh_endpoint: None, override_warp_endpoint: None, override_tunnel_mtu: None, qlog_log_until: FutureSystemTime(None), masque_settings: None, compliance_environment: None }
2025-07-28T08:49:40.918Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc response: e3161fe3-2353-4f86-b281-ae5d00f684b0; Success
2025-07-28T08:49:40.918Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="handle_ipc_request" elapsed=17.244229ms
2025-07-28T08:49:40.918Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="settings_changed"
2025-07-28T08:49:40.919Z DEBUG main_loop: warp::warp_service: Checking for registration vs settings account mismatch reg_is_teams=false settings_is_teams=false reg_org=None settings_org=None
2025-07-28T08:49:40.919Z DEBUG firewall::change: Firewall allow API ips api_ips=[*************, ************, 2606:4700::6810:1854, 2606:4700::6810:c052]
2025-07-28T08:49:40.919Z DEBUG firewall::change: Changing catch all policy Deny->Deny
2025-07-28T08:49:40.919Z DEBUG firewall::change: Firewall allow managed network endpoints managed_network_endpoints=[]
2025-07-28T08:49:40.919Z  INFO warp::warp_service::ipc_loop: IPC connection ended
2025-07-28T08:49:40.919Z DEBUG firewall::change: Firewall allow anycast ips: [*************, 2606:4700:102::3]
2025-07-28T08:49:40.919Z  INFO firewall::engine: Firewall starting
2025-07-28T08:49:40.925Z  WARN warp_net::ipc::unix: Failed to get path for client error="return code = -1, errno = 13, message = 'Permission denied'" pid=59
2025-07-28T08:49:40.925Z  WARN warp_net::ipc::unix: Failed to get path for client error="return code = -1, errno = 13, message = 'Permission denied'" pid=59
2025-07-28T08:49:40.925Z  INFO warp::warp_service::ipc_loop: IPC: new connection exec_context=Regular process_name="<Unknown>" pid=59
2025-07-28T08:49:40.930Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T08:49:40.930Z  INFO main_loop: warp::warp_service: User Settings Changed: {always_on: false->true, }
2025-07-28T08:49:40.931Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC update: SettingsUpdated
2025-07-28T08:49:40.931Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseUpdate: SettingsUpdated
2025-07-28T08:49:40.931Z  INFO main_loop: warp::warp_service: Settings and Registration determined that a key rotation was needed but there exists a pending API call that satisfies this constraint. Skipping for now.
2025-07-28T08:49:40.931Z DEBUG main_loop: warp::warp_service: update_settings: no restart required
2025-07-28T08:49:40.931Z DEBUG main_loop: warp::warp::controller: Stopping WarpConnection state=Disconnected
2025-07-28T08:49:40.931Z DEBUG firewall::change: Firewall allow API ips api_ips=[*************, ************, 2606:4700::6810:1854, 2606:4700::6810:c052]
2025-07-28T08:49:40.931Z DEBUG firewall::change: Changing catch all policy Deny->Deny
2025-07-28T08:49:40.931Z DEBUG firewall::change: Firewall allow managed network endpoints managed_network_endpoints=[]
2025-07-28T08:49:40.931Z DEBUG firewall::change: Firewall allow anycast ips: [*************, 2606:4700:102::3]
2025-07-28T08:49:40.961Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T08:49:40.961Z DEBUG main_loop: warp::warp_service: Determining disconnected reason from connectivity state net_info=IPv4: [eth0; **********; Ethernet; Gateway: Some(**********)], DNS servers:,  127.0.0.11:53
 power_state=None disconnect_reason=None
2025-07-28T08:49:40.961Z  WARN main_loop: warp::warp_service: Disconnecting, but reason is unknown
2025-07-28T08:49:40.962Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc request: 33edc3a9-ec7c-4a31-aa80-f38501fa594c; GetAppSettings
2025-07-28T08:49:40.962Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="settings_changed" elapsed=43.460608ms
2025-07-28T08:49:40.962Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="handle_ipc_request"
2025-07-28T08:49:40.962Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="handle_ipc_request" elapsed=378.58µs
2025-07-28T08:49:40.962Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="main_loop_compat_rx"
2025-07-28T08:49:40.964Z DEBUG main_loop:handle_command: warp::warp::controller: Stopping WarpConnection state=Disconnected
2025-07-28T08:49:40.964Z DEBUG firewall::change: Firewall allow API ips api_ips=[*************, ************, 2606:4700::6810:1854, 2606:4700::6810:c052]
2025-07-28T08:49:40.964Z DEBUG firewall::change: Changing catch all policy Deny->Deny
2025-07-28T08:49:40.964Z DEBUG firewall::change: Firewall allow managed network endpoints managed_network_endpoints=[]
2025-07-28T08:49:40.964Z DEBUG firewall::change: Firewall allow anycast ips: [*************, 2606:4700:102::3]
2025-07-28T08:49:40.969Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T08:49:40.969Z  INFO main_loop:handle_command: warp::warp_service: captive_portal_fw_until: Indefinitely
2025-07-28T08:49:40.977Z DEBUG main_loop:handle_command: warp::warp::proxying_dns: Using auto fallback: true
2025-07-28T08:49:40.977Z  WARN main_loop:handle_command: warp::warp::tunnel: Settings and Registration mismatch on tunnel protocol. Falling back to registration protocol. settings_proto=Masque reg_proto=Wireguard
2025-07-28T08:49:40.977Z DEBUG main_loop:handle_command: warp::warp::controller: Setting WarpConnection current_state=Disconnected
2025-07-28T08:49:40.977Z DEBUG main_loop:handle_command: warp::warp::controller: Stopping WarpConnection state=Disconnected
2025-07-28T08:49:40.977Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="main_loop_compat_rx" elapsed=15.376536ms
2025-07-28T08:49:40.980Z DEBUG main_loop: warp::warp::warp_connection: Current Network: IPv4: [eth0; **********; Ethernet; Gateway: Some(**********)], DNS servers:,  127.0.0.11:53

2025-07-28T08:49:40.981Z  INFO main_loop: warp::warp::warp_connection: Starting Warp Connection operation_mode=Warp dns_mode=DNS Proxy tunnel_mode=Exclude-only Tunnel posture_only=disabled forward_proxy=disabled
2025-07-28T08:49:40.984Z  INFO main_loop: warp::warp::tunnel: Initiate WARP connection protocol=Wireguard self.masque_settings=MasqueProtocolSettings { http_version: H3WithH2Fallback }
2025-07-28T08:49:40.986Z  INFO main_loop: warp::warp_service: WARP status: Connecting(CheckingNetwork)
2025-07-28T08:49:40.986Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Connecting(CheckingNetwork)
2025-07-28T08:49:40.986Z DEBUG firewall::change: Firewall allow tunnel UDP ipv4=[*************] ipv6=[2606:4700:d0::a29f:c003]
2025-07-28T08:49:40.986Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Connecting(CheckingNetwork)
2025-07-28T08:49:40.986Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T08:49:40.986Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=102.135µs
2025-07-28T08:49:40.986Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc request: 2c0ac552-f57e-464a-aaa1-2087a9c8cb79; GetRegistrationInfo
2025-07-28T08:49:40.986Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="handle_ipc_request"
2025-07-28T08:49:40.986Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc response: 2c0ac552-f57e-464a-aaa1-2087a9c8cb79; RegistrationInfo: Some(RegistrationInfo { id: Client(ConsumerRegistrationId(2d6c00fe-5a6c-46fb-a62a-b9aa5ed62ace)), device_id: PhysicalDeviceId(2d6c00fe-5a6c-46fb-a62a-b9aa5ed62ace), public_key: [255, 196, 20, 49, 44, 165, 46, 59, 219, 70, 80, 11, 161, 49, 216, 21, 95, 42, 9, 132, 215, 145, 131, 185, 156, 22, 112, 59, 150, 15, 181, 88], managed: false, account: Free { id: AccountId(38d2eb15-31ea-470f-92a9-3c26b5351c42), license: "94e5DJS8-Wqh617X9-78cjuC59" }, alternate_networks: None })
2025-07-28T08:49:40.986Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="handle_ipc_request" elapsed=159.014µs
2025-07-28T08:49:40.986Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="handle_ipc_request"
2025-07-28T08:49:40.986Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc request: f587e10f-3195-4a28-9f44-9badb02bd26c; SetQlog(setting=Disabled)
2025-07-28T08:49:40.987Z DEBUG run: warp_settings::manager: LayerManager update: UserOverrides(<mutator>)
2025-07-28T08:49:40.987Z DEBUG run: warp_settings::manager: UserOverrides updated: UserOverrides { version: None, always_on: Some(true), operation_mode: None, disable_for_wifi: None, disable_for_ethernet: None, disable_for_networks: None, families: None, dns_log_until: FutureSystemTime(None), gateway_id: None, onboarding: None, organization: None, split_config: None, fallback_domains: None, proxy_port: None, disable_connectivity_checks: None, override_api_endpoint: None, override_doh_endpoint: None, override_warp_endpoint: None, override_tunnel_mtu: None, qlog_log_until: FutureSystemTime(None), masque_settings: None, compliance_environment: None }
2025-07-28T08:49:40.987Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc response: f587e10f-3195-4a28-9f44-9badb02bd26c; Success
2025-07-28T08:49:40.987Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="handle_ipc_request" elapsed=875.145µs
2025-07-28T08:49:40.988Z  INFO warp::warp_service::ipc_loop: IPC connection ended
2025-07-28T08:49:40.992Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T08:49:41.014Z DEBUG firewall::change: Firewall allow captive portal detection
2025-07-28T08:49:41.021Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T08:49:41.021Z DEBUG captive_portal: Running captive portal detections [legacy,os-detected,http-redirect[cloudflareportal.com],no-dns-redirect[cloudflareportal.com],expected-content,third-party]
2025-07-28T08:49:41.023Z DEBUG main_loop:connect_single_protocol{protocol="wireguard"}:happy_eyeballs_with_retry{protocol="wireguard"}: warp_edge::happy_eyeballs: Attempting happy eyeballs with retries retries=0 retry_delay=0ns stage=FirstRun
2025-07-28T08:49:41.023Z DEBUG main_loop:connect_single_protocol{protocol="wireguard"}:happy_eyeballs_with_retry{protocol="wireguard"}: warp_edge::happy_eyeballs: Attempting Happy Eyeballs to *************:2408 / [2606:4700:d0::a29f:c003]:2408
2025-07-28T08:49:41.024Z DEBUG main_loop:connect_single_protocol{protocol="wireguard"}:happy_eyeballs_with_retry{protocol="wireguard"}: warp_edge::happy_eyeballs: Start racer **********:56423 ---> *************:2408
2025-07-28T08:49:41.024Z DEBUG main_loop:connect_single_protocol{protocol="wireguard"}:happy_eyeballs_with_retry{protocol="wireguard"}: warp_edge::wireguard_tun: Handshaking wireguard with session tag affinity_tag=0
2025-07-28T08:49:41.025Z DEBUG run{detection_type=os-detected}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T08:49:41.025Z  INFO run{detection_type=legacy}: captive_portal::detections::legacy: Checking for captive portal with legacy detection urls=["cloudflareportal.com", "cloudflareok.com", "cloudflarecp.com"]
2025-07-28T08:49:41.025Z  INFO run{detection_type=http-redirect[cloudflareportal.com]}: captive_portal::detections::http_redirect: Checking for captive portal with HTTP-Redirect detection host="cloudflareportal.com" ips=None interface=None bind_addr=None
2025-07-28T08:49:41.025Z  INFO run{detection_type=no-dns-redirect[cloudflareportal.com]}: captive_portal::detections::http_redirect: Checking for captive portal with HTTP-Redirect detection host="cloudflareportal.com" ips=Some([**********, **************, **********, 2606:4700:7::1, 2606:4700::1]) interface=None bind_addr=None
2025-07-28T08:49:41.025Z  INFO captive_portal::detections::http_redirect: Checking for captive portal with HTTP-Redirect detection host="connectivitycheck.gstatic.com" ips=None interface=None bind_addr=None
2025-07-28T08:49:41.025Z  INFO captive_portal::detections::http_redirect: Checking for captive portal with HTTP-Redirect detection host="www.msftconnecttest.com" ips=None interface=None bind_addr=None
2025-07-28T08:49:41.026Z  INFO captive_portal::detections::http_redirect: Checking for captive portal with HTTP-Redirect detection host="captive.apple.com" ips=None interface=None bind_addr=None
2025-07-28T08:49:41.033Z DEBUG main_loop:connect_single_protocol{protocol="wireguard"}:happy_eyeballs_with_retry{protocol="wireguard"}: warp_edge::wireguard_tun: Sent handshake initiation to *************:2408
2025-07-28T08:49:41.034Z DEBUG main_loop:connect_single_protocol{protocol="wireguard"}:happy_eyeballs_with_retry{protocol="wireguard"}: warp_edge::happy_eyeballs: Happy Eyeballs check failed v6=[2606:4700:d0::a29f:c003]:2408 err=Connect(Os { code: 101, kind: NetworkUnreachable, message: "Network is unreachable" })
2025-07-28T08:49:41.034Z  INFO main_loop: warp::warp_service: WARP status: Connecting(PerformingHappyEyeballs((*************:2408, [2606:4700:d0::a29f:c003]:2408)))
2025-07-28T08:49:41.034Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Connecting(PerformingHappyEyeballs((*************:2408, [2606:4700:d0::a29f:c003]:2408)))
2025-07-28T08:49:41.034Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Connecting(PerformingHappyEyeballs((*************:2408, [2606:4700:d0::a29f:c003]:2408)))
2025-07-28T08:49:41.034Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T08:49:41.034Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=282.869µs
2025-07-28T08:49:41.035Z DEBUG captive_portal::detections::http_redirect: 204 response for HTTP test. No captive portal detected
2025-07-28T08:49:41.036Z DEBUG captive_portal::detections::http_redirect: 200 response for HTTP test. No captive portal detected
2025-07-28T08:49:41.036Z DEBUG captive_portal::detections::http_redirect: 200 response for HTTP test. No captive portal detected
2025-07-28T08:49:41.037Z DEBUG run{detection_type=third-party}: captive_portal::detections::third_party: Third party detections complete detections=[("connectivitycheck.gstatic.com", Ok(NoCaptivePortalDetected)), ("www.msftconnecttest.com", Ok(NoCaptivePortalDetected)), ("captive.apple.com", Ok(NoCaptivePortalDetected))]
2025-07-28T08:49:41.037Z DEBUG run{detection_type=third-party}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T08:49:41.044Z  INFO run{detection_type=legacy}: captive_portal::detections::legacy: Successfully resolved DNS queries
2025-07-28T08:49:41.048Z DEBUG run{detection_type=no-dns-redirect[cloudflareportal.com]}: captive_portal::detections::http_redirect: 204 response for HTTP test. No captive portal detected
2025-07-28T08:49:41.048Z DEBUG run{detection_type=no-dns-redirect[cloudflareportal.com]}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T08:49:41.060Z DEBUG run{detection_type=expected-content}: captive_portal::detections::expected_content: 200 response and correct content for HTTP Content test. No captive portal detected
2025-07-28T08:49:41.060Z DEBUG run{detection_type=expected-content}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T08:49:41.065Z DEBUG run{detection_type=http-redirect[cloudflareportal.com]}: captive_portal::detections::http_redirect: 204 response for HTTP test. No captive portal detected
2025-07-28T08:49:41.065Z DEBUG run{detection_type=http-redirect[cloudflareportal.com]}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T08:49:41.075Z  INFO run{detection_type=legacy}: captive_portal::detections::legacy: Successfully retrieved HTTPs page
2025-07-28T08:49:41.075Z DEBUG run{detection_type=legacy}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T08:49:41.076Z DEBUG captive_portal: Captive portal detection completed results=[("os-detected", Ok(NoCaptivePortalDetected)), ("third-party", Ok(NoCaptivePortalDetected)), ("no-dns-redirect[cloudflareportal.com]", Ok(NoCaptivePortalDetected)), ("expected-content", Ok(NoCaptivePortalDetected)), ("http-redirect[cloudflareportal.com]", Ok(NoCaptivePortalDetected)), ("legacy", Ok(NoCaptivePortalDetected))]
2025-07-28T08:49:41.076Z DEBUG firewall::change: Firewall disallow captive portal detection
2025-07-28T08:49:41.083Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T08:49:41.083Z DEBUG firewall::captive_portal: Firewall captive portal detection expired
2025-07-28T08:49:41.873Z DEBUG warp_api_endpoints::payloads: Warp endpoint override ports ports=[443, 500, 1701, 4500, 4443, 8443, 8095]
2025-07-28T08:49:41.873Z DEBUG main_loop: warp_api_client::executor: Returning API response for RotateKeys request_id=e6f31b83-ba62-43bb-a480-1e0bc1fbc36d
2025-07-28T08:49:41.873Z DEBUG main_loop: warp::warp_service::api_handlers: Registration updated current_registration=Registered(reg=2d6c00fe-5a6c-46fb-a62a-b9aa5ed62ace) disposition=CheckAndUpdate
2025-07-28T08:49:41.873Z DEBUG main_loop: warp::warp_service::api_handlers: Invalidating existing posture check scheduler on registration update
2025-07-28T08:49:41.873Z DEBUG main_loop: warp::warp_service::api_handlers: Registration updated 0.00 hours old, refresh at 2025-07-29 8:49:41.873123138 +00:00:00
Registration: 2d6c00fe-5a6c-46fb-a62a-b9aa5ed62ace
Device Identifiers: System User
Auth Method: Consumer
Public key: 3059301306072a8648ce3d020106082a8648ce3d03010703420004113924ef48373289b7e26ff95857ba8b4db98b523b27141ed843834bd7e10785f22e431af487429a370745973dff12d1ba6c99d1ad8dce79a9faa5f050a52165
Interface: WarpEndpoint { v4: **********, v6: 2606:4700:110:8bb0:2b1:9371:a882:ec04 }
Endpoints: [WarpSocketAddrPair { v4: *************:443, v6: [2606:4700:103::2]:443 }, WarpSocketAddrPair { v4: *************:500, v6: [2606:4700:103::2]:500 }, WarpSocketAddrPair { v4: *************:1701, v6: [2606:4700:103::2]:1701 }, WarpSocketAddrPair { v4: *************:4500, v6: [2606:4700:103::2]:4500 }, WarpSocketAddrPair { v4: *************:4443, v6: [2606:4700:103::2]:4443 }, WarpSocketAddrPair { v4: *************:8443, v6: [2606:4700:103::2]:8443 }, WarpSocketAddrPair { v4: *************:8095, v6: [2606:4700:103::2]:8095 }]
Subnet CIDRS: None

Account: Free { id: AccountId(38d2eb15-31ea-470f-92a9-3c26b5351c42), license: "94e5DJS8-Wqh617X9-78cjuC59" }

2025-07-28T08:49:41.873Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="api_responses"
2025-07-28T08:49:41.873Z  INFO main_loop: warp_storage::registration::registration_storage: Saving registration to active cache config_name=None username="WARPSecret"
2025-07-28T08:49:41.874Z DEBUG main_loop: warp::warp::controller: Stopping WarpConnection state=Connecting
2025-07-28T08:49:41.874Z DEBUG warp::warp_service::ipc_handlers: Sending IPC update: ConfigurationUpdated
2025-07-28T08:49:41.874Z DEBUG warp::warp_service::ipc_handlers: Ipc Broadcast ResponseUpdate: ConfigurationUpdated
2025-07-28T08:49:41.874Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 2 }, CounterPayload { name: "emergency-disconnect.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "root-ca.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 9 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.updates_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 63.0, count: 6 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.000322225, count: 9 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="registration change"}: actor_statistics::handler: Starting to upload stats
2025-07-28T08:49:41.874Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 2 }, CounterPayload { name: "emergency-disconnect.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "root-ca.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 9 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.updates_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 63.0, count: 6 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.000322225, count: 9 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="registration change"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-07-28T08:49:41.874Z  INFO actor_statistics::handler: Switched to new registration for stats. All stats have been reset. new_registration_id=None
2025-07-28T08:49:41.875Z DEBUG firewall::change: Firewall reset to defaults
2025-07-28T08:49:41.882Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T08:49:41.882Z DEBUG firewall::change: Firewall allow API ips api_ips=[*************, ************, 2606:4700::6810:1854, 2606:4700::6810:c052]
2025-07-28T08:49:41.882Z DEBUG firewall::change: Changing catch all policy Deny->Deny
2025-07-28T08:49:41.882Z DEBUG firewall::change: Firewall allow managed network endpoints managed_network_endpoints=[]
2025-07-28T08:49:41.882Z DEBUG firewall::change: Firewall allow anycast ips: [*************, 2606:4700:102::3]
2025-07-28T08:49:41.929Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T08:49:41.929Z DEBUG main_loop: warp::warp_service: Determining disconnected reason from connectivity state net_info=IPv4: [eth0; **********; Ethernet; Gateway: Some(**********)], DNS servers:,  127.0.0.11:53
 power_state=None disconnect_reason=None
2025-07-28T08:49:41.929Z  WARN main_loop: warp::warp_service: Disconnecting, but reason is unknown
2025-07-28T08:49:41.929Z DEBUG main_loop: warp::warp_service: Checking for registration vs settings account mismatch reg_is_teams=false settings_is_teams=false reg_org=None settings_org=None
2025-07-28T08:49:41.929Z DEBUG firewall::change: Firewall allow API ips api_ips=[*************, ************, 2606:4700::6810:1854, 2606:4700::6810:c052]
2025-07-28T08:49:41.929Z DEBUG firewall::change: Changing catch all policy Deny->Deny
2025-07-28T08:49:41.929Z DEBUG firewall::change: Firewall allow managed network endpoints managed_network_endpoints=[]
2025-07-28T08:49:41.929Z DEBUG firewall::change: Firewall allow anycast ips: [*************, 2606:4700:102::3]
2025-07-28T08:49:41.934Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T08:49:41.934Z  INFO main_loop: warp::warp_service: User Settings Changed: {}
2025-07-28T08:49:41.934Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC update: SettingsUpdated
2025-07-28T08:49:41.934Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseUpdate: SettingsUpdated
2025-07-28T08:49:41.934Z DEBUG main_loop: warp::warp_service: update_settings: no restart required
2025-07-28T08:49:41.934Z DEBUG main_loop: warp::warp::controller: Stopping WarpConnection state=Disconnected
2025-07-28T08:49:41.935Z DEBUG firewall::change: Firewall allow API ips api_ips=[*************, ************, 2606:4700::6810:1854, 2606:4700::6810:c052]
2025-07-28T08:49:41.935Z DEBUG firewall::change: Changing catch all policy Deny->Deny
2025-07-28T08:49:41.935Z DEBUG firewall::change: Firewall allow managed network endpoints managed_network_endpoints=[]
2025-07-28T08:49:41.935Z DEBUG firewall::change: Firewall allow anycast ips: [*************, 2606:4700:102::3]
2025-07-28T08:49:41.973Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T08:49:41.973Z DEBUG main_loop: warp::warp_service: Determining disconnected reason from connectivity state net_info=IPv4: [eth0; **********; Ethernet; Gateway: Some(**********)], DNS servers:,  127.0.0.11:53
 power_state=None disconnect_reason=None
2025-07-28T08:49:41.973Z  WARN main_loop: warp::warp_service: Disconnecting, but reason is unknown
2025-07-28T08:49:41.973Z DEBUG run: warp_settings::manager: LayerManager update: NetworkPolicy(Some(NetworkPolicySettings { onboarding: None, operation_mode: None, disable_auto_fallback: None, fallback_domains: None, proxy_port: None, split_config: None, gateway_id: None, support_url: None, allow_mode_switch: None, switch_locked: None, auto_connect: None, captive_portal: None, organization: None, allow_updates: None, allowed_to_leave: None, profile_id: None, lan_allow_settings: Some(LanAllowSettings { minutes: None, subnet_size: SubnetSize(24) }), warp_tunnel_protocol: Some(Masque), masque_settings: None, register_interface_ip_with_dns: None, anycast_split_ips: Some(AnycastSplitIps { always_exclude: [*************, 2606:4700:102::3], always_include: [*************, 2606:4700:102::4] }), firewall_scope: None, sccm_vpn_boundary_support: None, enable_post_quantum: None }))
2025-07-28T08:49:41.974Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="api_responses" elapsed=100.808867ms
2025-07-28T08:49:41.974Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="main_loop_compat_rx"
2025-07-28T08:49:41.975Z DEBUG main_loop:handle_command: warp::warp::controller: Stopping WarpConnection state=Disconnected
2025-07-28T08:49:41.975Z DEBUG firewall::change: Firewall allow API ips api_ips=[*************, ************, 2606:4700::6810:1854, 2606:4700::6810:c052]
2025-07-28T08:49:41.975Z DEBUG firewall::change: Changing catch all policy Deny->Deny
2025-07-28T08:49:41.975Z DEBUG firewall::change: Firewall allow managed network endpoints managed_network_endpoints=[]
2025-07-28T08:49:41.975Z DEBUG firewall::change: Firewall allow anycast ips: [*************, 2606:4700:102::3]
2025-07-28T08:49:41.980Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T08:49:41.980Z  INFO main_loop:handle_command: warp::warp_service: captive_portal_fw_until: Indefinitely
2025-07-28T08:49:41.981Z DEBUG main_loop:handle_command: warp::warp::proxying_dns: Using auto fallback: true
2025-07-28T08:49:41.981Z DEBUG main_loop:handle_command: warp::warp::controller: Setting WarpConnection current_state=Disconnected
2025-07-28T08:49:41.981Z DEBUG main_loop:handle_command: warp::warp::controller: Stopping WarpConnection state=Disconnected
2025-07-28T08:49:41.981Z DEBUG main_loop: warp::warp::warp_connection: Current Network: IPv4: [eth0; **********; Ethernet; Gateway: Some(**********)], DNS servers:,  127.0.0.11:53

2025-07-28T08:49:41.981Z  INFO main_loop: warp::warp::warp_connection: Starting Warp Connection operation_mode=Warp dns_mode=DNS Proxy tunnel_mode=Exclude-only Tunnel posture_only=disabled forward_proxy=disabled
2025-07-28T08:49:41.981Z  INFO main_loop: warp::warp::tunnel: Initiate WARP connection protocol=Masque self.masque_settings=MasqueProtocolSettings { http_version: H3WithH2Fallback }
2025-07-28T08:49:41.981Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="main_loop_compat_rx" elapsed=7.828804ms
2025-07-28T08:49:41.981Z DEBUG firewall::change: Firewall allow tunnel UDP ipv4=[*************] ipv6=[2606:4700:103::2]
2025-07-28T08:49:41.982Z  INFO main_loop: warp::warp_service: WARP status: Connecting(CheckingNetwork)
2025-07-28T08:49:41.982Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T08:49:41.982Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Connecting(CheckingNetwork)
2025-07-28T08:49:41.982Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Connecting(CheckingNetwork)
2025-07-28T08:49:41.982Z  INFO main_loop: warp::warp_service: WARP status: Connecting(InitializingSettings)
2025-07-28T08:49:41.982Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Connecting(InitializingSettings)
2025-07-28T08:49:41.982Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Connecting(InitializingSettings)
2025-07-28T08:49:41.982Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=214.639µs
2025-07-28T08:49:41.983Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="registration_change"
2025-07-28T08:49:41.983Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="registration_change" elapsed=130.96µs
2025-07-28T08:49:41.983Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T08:49:41.983Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=143.413µs
2025-07-28T08:49:42.021Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="main_loop_compat_rx"
2025-07-28T08:49:42.021Z DEBUG main_loop:handle_update{update=NetworkInfoChanged}: warp::warp_service::actor_compat: Handling network status change old_info=IPv4: [eth0; **********; Ethernet; Gateway: Some(**********)], DNS servers:,  127.0.0.11:53
 new_info=IPv4: [eth0; **********; Ethernet; Gateway: Some(**********)], DNS servers:,  127.0.0.11:53

2025-07-28T08:49:42.021Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="main_loop_compat_rx" elapsed=411.192µs
2025-07-28T08:49:42.030Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T08:49:42.030Z DEBUG firewall::change: Firewall allow tunnel TCP addresses=[*************:443, *************:500, *************:1701, *************:4443, *************:4500, *************:8095, *************:8443, [2606:4700:103::2]:443, [2606:4700:103::2]:500, [2606:4700:103::2]:1701, [2606:4700:103::2]:4443, [2606:4700:103::2]:4500, [2606:4700:103::2]:8095, [2606:4700:103::2]:8443]
2025-07-28T08:49:42.041Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T08:49:42.041Z DEBUG firewall::change: Firewall allow captive portal detection
2025-07-28T08:49:42.090Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T08:49:42.090Z DEBUG main_loop:connect_with_fallback{primary="masque" fallback="H2"}:happy_eyeballs_with_retry{protocol="masque"}: warp_edge::happy_eyeballs: Attempting happy eyeballs with retries retries=0 retry_delay=0ns stage=FirstRun
2025-07-28T08:49:42.090Z DEBUG main_loop:connect_with_fallback{primary="masque" fallback="H2"}:happy_eyeballs_with_retry{protocol="masque"}: warp_edge::happy_eyeballs: Attempting Happy Eyeballs to *************:443 / [2606:4700:103::2]:443
2025-07-28T08:49:42.090Z DEBUG main_loop:connect_with_fallback{primary="masque" fallback="H2"}:happy_eyeballs_with_retry{protocol="masque"}: warp_edge::happy_eyeballs: Start racer **********:43990 ---> *************:443
2025-07-28T08:49:42.090Z DEBUG captive_portal: Running captive portal detections [legacy,os-detected,http-redirect[cloudflareportal.com],no-dns-redirect[cloudflareportal.com],expected-content,third-party]
2025-07-28T08:49:42.090Z  INFO captive_portal::detections::http_redirect: Checking for captive portal with HTTP-Redirect detection host="connectivitycheck.gstatic.com" ips=None interface=None bind_addr=None
2025-07-28T08:49:42.091Z  INFO run{detection_type=no-dns-redirect[cloudflareportal.com]}: captive_portal::detections::http_redirect: Checking for captive portal with HTTP-Redirect detection host="cloudflareportal.com" ips=Some([**********, **************, **********, 2606:4700:7::1, 2606:4700::1]) interface=None bind_addr=None
2025-07-28T08:49:42.091Z DEBUG run{detection_type=os-detected}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T08:49:42.091Z  INFO run{detection_type=legacy}: captive_portal::detections::legacy: Checking for captive portal with legacy detection urls=["cloudflareportal.com", "cloudflareok.com", "cloudflarecp.com"]
2025-07-28T08:49:42.091Z DEBUG main_loop:connect_with_fallback{primary="masque" fallback="H2"}:happy_eyeballs_with_retry{protocol="masque"}: warp_edge::h3_tun: Creating QUIC Client Connection Parmaeters qlog_setting=Disabled idle_timeout=IdleTimeout { idle_duration: IdleDuration(5s), retries: 0 }
2025-07-28T08:49:42.091Z DEBUG main_loop:connect_with_fallback{primary="masque" fallback="H2"}:happy_eyeballs_with_retry{protocol="masque"}: warp_edge::h3_tun: Sending handshake initiation to *************:443
2025-07-28T08:49:42.091Z  INFO captive_portal::detections::http_redirect: Checking for captive portal with HTTP-Redirect detection host="www.msftconnecttest.com" ips=None interface=None bind_addr=None
2025-07-28T08:49:42.091Z  INFO captive_portal::detections::http_redirect: Checking for captive portal with HTTP-Redirect detection host="captive.apple.com" ips=None interface=None bind_addr=None
2025-07-28T08:49:42.091Z  INFO run{detection_type=http-redirect[cloudflareportal.com]}: captive_portal::detections::http_redirect: Checking for captive portal with HTTP-Redirect detection host="cloudflareportal.com" ips=None interface=None bind_addr=None
2025-07-28T08:49:42.091Z DEBUG main_loop:connect_with_fallback{primary="masque" fallback="H2"}:happy_eyeballs_with_retry{protocol="masque"}: warp_edge::happy_eyeballs: Happy Eyeballs check failed v6=[2606:4700:103::2]:443 err=Connect(Os { code: 101, kind: NetworkUnreachable, message: "Network is unreachable" })
2025-07-28T08:49:42.091Z  INFO slog: created unestablished quiche::Connection slog.target="tokio_quiche::quic" slog.module_path="tokio_quiche::quic" slog.file="/home/<USER>/.cargo/git/checkouts/quiche-4103ffa15137cf39/bfd737b/tokio-quiche/src/quic/mod.rs" slog.line=235 slog.column=5 slog.kv="provided_dcid=None,scid=e0772a114ac940e857ece8392c2b70e6d18d2991"
2025-07-28T08:49:42.091Z  INFO main_loop: warp::warp_service: WARP status: Connecting(PerformingHappyEyeballs((*************:443, [2606:4700:103::2]:443)))
2025-07-28T08:49:42.091Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Connecting(PerformingHappyEyeballs((*************:443, [2606:4700:103::2]:443)))
2025-07-28T08:49:42.091Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T08:49:42.092Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Connecting(PerformingHappyEyeballs((*************:443, [2606:4700:103::2]:443)))
2025-07-28T08:49:42.092Z DEBUG slog: sending client Initials to peer slog.target="tokio_quiche::quic::router::connector" slog.module_path="tokio_quiche::quic::router::connector" slog.file="/home/<USER>/.cargo/git/checkouts/quiche-4103ffa15137cf39/bfd737b/tokio-quiche/src/quic/router/connector.rs" slog.line=266 slog.column=5 slog.kv="scid=e0772a114ac940e857ece8392c2b70e6d18d2991"
2025-07-28T08:49:42.092Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=718.027µs
2025-07-28T08:49:42.094Z  INFO run{detection_type=legacy}: captive_portal::detections::legacy: Successfully resolved DNS queries
2025-07-28T08:49:42.099Z DEBUG slog: sending client Initials to peer slog.target="tokio_quiche::quic::router::connector" slog.module_path="tokio_quiche::quic::router::connector" slog.file="/home/<USER>/.cargo/git/checkouts/quiche-4103ffa15137cf39/bfd737b/tokio-quiche/src/quic/router/connector.rs" slog.line=266 slog.column=5 slog.kv="scid=e0772a114ac940e857ece8392c2b70e6d18d2991"
2025-07-28T08:49:42.103Z DEBUG captive_portal::detections::http_redirect: 204 response for HTTP test. No captive portal detected
2025-07-28T08:49:42.103Z DEBUG captive_portal::detections::http_redirect: 200 response for HTTP test. No captive portal detected
2025-07-28T08:49:42.104Z DEBUG slog: sending client Initials to peer slog.target="tokio_quiche::quic::router::connector" slog.module_path="tokio_quiche::quic::router::connector" slog.file="/home/<USER>/.cargo/git/checkouts/quiche-4103ffa15137cf39/bfd737b/tokio-quiche/src/quic/router/connector.rs" slog.line=266 slog.column=5 slog.kv="scid=e0772a114ac940e857ece8392c2b70e6d18d2991"
2025-07-28T08:49:42.108Z DEBUG captive_portal::detections::http_redirect: 200 response for HTTP test. No captive portal detected
2025-07-28T08:49:42.108Z DEBUG run{detection_type=third-party}: captive_portal::detections::third_party: Third party detections complete detections=[("connectivitycheck.gstatic.com", Ok(NoCaptivePortalDetected)), ("captive.apple.com", Ok(NoCaptivePortalDetected)), ("www.msftconnecttest.com", Ok(NoCaptivePortalDetected))]
2025-07-28T08:49:42.108Z DEBUG run{detection_type=third-party}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T08:49:42.109Z DEBUG slog: QUIC connection established slog.target="tokio_quiche::quic::router::connector" slog.module_path="tokio_quiche::quic::router::connector" slog.file="/home/<USER>/.cargo/git/checkouts/quiche-4103ffa15137cf39/bfd737b/tokio-quiche/src/quic/router/connector.rs" slog.line=176 slog.column=13 slog.kv="scid=e0772a114ac940e857ece8392c2b70e6d18d2991"
2025-07-28T08:49:42.109Z DEBUG run{detection_type=no-dns-redirect[cloudflareportal.com]}: captive_portal::detections::http_redirect: 204 response for HTTP test. No captive portal detected
2025-07-28T08:49:42.110Z DEBUG run{detection_type=no-dns-redirect[cloudflareportal.com]}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T08:49:42.110Z DEBUG main_loop:connect_with_fallback{primary="masque" fallback="H2"}:happy_eyeballs_with_retry{protocol="masque"}: warp_edge::h3_tun: Established QUIC connection with *************:443 scid=e0772a114ac940e857ece8392c2b70e6d18d2991
2025-07-28T08:49:42.110Z DEBUG main_loop:connect_with_fallback{primary="masque" fallback="H2"}:happy_eyeballs_with_retry{protocol="masque"}: warp_edge::h3_tun: Establishing MASQUE tunnel on existing QUIC connection scid=e0772a114ac940e857ece8392c2b70e6d18d2991 endpoint=*************:443
2025-07-28T08:49:42.110Z  INFO main_loop:connect_with_fallback{primary="masque" fallback="H2"}:happy_eyeballs_with_retry{protocol="masque"}:proxy_task{scid=e0772a114ac940e857ece8392c2b70e6d18d2991}: warp_edge::h3_tun: Starting MASQUE proxy task idle_timeout=IdleTimeout { idle_duration: IdleDuration(5s), retries: 0 } keepalive_interval=4s
2025-07-28T08:49:42.110Z  INFO main_loop: warp::warp_service: WARP status: Connecting(EstablishingConnection(*************:443))
2025-07-28T08:49:42.110Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T08:49:42.110Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Connecting(EstablishingConnection(*************:443))
2025-07-28T08:49:42.110Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Connecting(EstablishingConnection(*************:443))
2025-07-28T08:49:42.110Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=386.595µs
2025-07-28T08:49:42.111Z  INFO slog: creating new flow for MASQUE request slog.target="tokio_quiche::http3::driver::client" slog.module_path="tokio_quiche::http3::driver::client" slog.file="/home/<USER>/.cargo/git/checkouts/quiche-4103ffa15137cf39/bfd737b/tokio-quiche/src/http3/driver/client.rs" slog.line=168 slog.column=13 slog.kv="flow_id=0,stream_id=0"
2025-07-28T08:49:42.122Z DEBUG run{detection_type=expected-content}: captive_portal::detections::expected_content: 200 response and correct content for HTTP Content test. No captive portal detected
2025-07-28T08:49:42.122Z DEBUG run{detection_type=expected-content}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T08:49:42.132Z DEBUG run{detection_type=http-redirect[cloudflareportal.com]}: captive_portal::detections::http_redirect: 204 response for HTTP test. No captive portal detected
2025-07-28T08:49:42.132Z DEBUG run{detection_type=http-redirect[cloudflareportal.com]}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T08:49:42.134Z  INFO run{detection_type=legacy}: captive_portal::detections::legacy: Successfully retrieved HTTPs page
2025-07-28T08:49:42.134Z DEBUG run{detection_type=legacy}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T08:49:42.135Z DEBUG captive_portal: Captive portal detection completed results=[("os-detected", Ok(NoCaptivePortalDetected)), ("third-party", Ok(NoCaptivePortalDetected)), ("no-dns-redirect[cloudflareportal.com]", Ok(NoCaptivePortalDetected)), ("expected-content", Ok(NoCaptivePortalDetected)), ("http-redirect[cloudflareportal.com]", Ok(NoCaptivePortalDetected)), ("legacy", Ok(NoCaptivePortalDetected))]
2025-07-28T08:49:42.135Z DEBUG firewall::change: Firewall disallow captive portal detection
2025-07-28T08:49:42.147Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T08:49:42.147Z DEBUG firewall::captive_portal: Firewall captive portal detection expired
2025-07-28T08:49:43.376Z DEBUG slog: got a response for stream_id=0, headers=[":status: 200", "cf-team: 28f1d24dcf0000db0e4e968400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T08:49:43.376Z DEBUG slog: upgraded H3Body: flow_id=0, mode=WithContextId slog.target="httx::body" slog.module_path="httx::body" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/body/mod.rs" slog.line=678 slog.column=5
2025-07-28T08:49:43.377Z DEBUG main_loop:connect_with_fallback{primary="masque" fallback="H2"}: warp::warp::tunnel::fallback: Connecting with primary connection protocol, reverting fallback firewall config
2025-07-28T08:49:43.377Z DEBUG firewall::change: Firewall allow tunnel TCP addresses=[]
2025-07-28T08:49:43.382Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T08:49:43.383Z DEBUG main_loop:start_tunnel_processing{protocol="masque"}: warp::warp::tunnel: Connected to *************:443
2025-07-28T08:49:43.383Z  INFO main_loop:start_tunnel_processing{protocol="masque"}: warp::warp::tunnel: DNS resolver connection is being made inside of the tunnel
2025-07-28T08:49:43.383Z DEBUG main_loop:start_tunnel_processing{protocol="masque"}: warp_primitives::daemon_status: Connection stage 'Establishing connection to *************:443' took 1273ms which is longer than threshold of 500ms
2025-07-28T08:49:43.383Z DEBUG main_loop:connect_with_fallback{primary="masque" fallback="H2"}:tunnel_stats_reporting_task{scid=e0772a114ac940e857ece8392c2b70e6d18d2991}: warp_edge::h3_tun::tunnel_stats: Updating tunnel stats reporting interval from=120s to=15s
2025-07-28T08:49:43.384Z DEBUG slog: got a response for stream_id=4, headers=[":status: 200", "cf-team: 28f1d24dd50000db0e4e969400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T08:49:43.384Z  INFO main_loop: warp::warp_service: WARP status: Connecting(InitializingTunnelInterface)
2025-07-28T08:49:43.384Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Connecting(InitializingTunnelInterface)
2025-07-28T08:49:43.384Z DEBUG firewall::change: Firewall allow interface CloudflareWARP
2025-07-28T08:49:43.384Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Connecting(InitializingTunnelInterface)
2025-07-28T08:49:43.384Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T08:49:43.384Z  INFO main_loop: warp::warp_service: WARP status: Connecting(ConfiguringInitialFirewall)
2025-07-28T08:49:43.384Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Connecting(ConfiguringInitialFirewall)
2025-07-28T08:49:43.384Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Connecting(ConfiguringInitialFirewall)
2025-07-28T08:49:43.384Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=384.412µs
2025-07-28T08:49:43.384Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T08:49:43.385Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=530.559µs
2025-07-28T08:49:43.421Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T08:49:43.474Z  INFO main_loop: warp::warp_service: WARP status: Connecting(SettingRoutes)
2025-07-28T08:49:43.474Z DEBUG firewall::change: Firewall allowing private IPs
2025-07-28T08:49:43.474Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T08:49:43.474Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Connecting(SettingRoutes)
2025-07-28T08:49:43.474Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Connecting(SettingRoutes)
2025-07-28T08:49:43.474Z  INFO main_loop: warp::warp_service: WARP status: Connecting(ConfiguringFirewallRules)
2025-07-28T08:49:43.474Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Connecting(ConfiguringFirewallRules)
2025-07-28T08:49:43.474Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Connecting(ConfiguringFirewallRules)
2025-07-28T08:49:43.475Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=938.766µs
2025-07-28T08:49:43.475Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T08:49:43.475Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=1.074985ms
2025-07-28T08:49:43.482Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T08:49:43.483Z  INFO main_loop: warp::warp::proxying_dns: Initiate DNS connection
2025-07-28T08:49:43.484Z DEBUG tunnel_loop{protocol="masque" con_id="e0772a114ac940e857ece8392c2b70e6d18d2991"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 1, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-07-28T08:49:43.497Z DEBUG main_loop: warp::warp::proxying_dns: Racing DNS IPs ips=[************, ************, 2606:4700:4700::1111, 2606:4700:4700::1001]
2025-07-28T08:49:43.504Z DEBUG main_loop: warp::warp::proxying_dns: DNS TCP connection to Gateway DoH resolver was successful raced_ip=************
2025-07-28T08:49:43.504Z DEBUG main_loop: dns_proxy::proxy: Creating DoH resolver config=ResolverConfig { domain: None, search: [], name_servers: NameServerConfigGroup([NameServerConfig { socket_addr: ************:443, protocol: Https, tls_dns_name: Some("cloudflare-dns.com"), trust_negative_responses: true, tls_config: None, bind_addr: None }, NameServerConfig { socket_addr: ************:443, protocol: Https, tls_dns_name: Some("cloudflare-dns.com"), trust_negative_responses: true, tls_config: None, bind_addr: None }, NameServerConfig { socket_addr: [2606:4700:4700::1111]:443, protocol: Https, tls_dns_name: Some("cloudflare-dns.com"), trust_negative_responses: true, tls_config: None, bind_addr: None }, NameServerConfig { socket_addr: [2606:4700:4700::1001]:443, protocol: Https, tls_dns_name: Some("cloudflare-dns.com"), trust_negative_responses: true, tls_config: None, bind_addr: None }], None) } options=ResolverOpts { ndots: 1, timeout: 4s, attempts: 0, rotate: false, check_names: true, edns0: true, validate: false, ip_strategy: Ipv4thenIpv6, cache_size: 32, use_hosts_file: true, positive_min_ttl: None, negative_min_ttl: None, positive_max_ttl: None, negative_max_ttl: Some(10s), num_concurrent_reqs: 1, preserve_intermediates: true, try_tcp_on_error: false, server_ordering_strategy: QueryStatistics, recursion_desired: true, authentic_data: false, shuffle_dns_servers: false }
2025-07-28T08:49:43.504Z  INFO main_loop: warp::warp_service: WARP status: Connecting(CheckingForRouteToDnsEndpoint)
2025-07-28T08:49:43.504Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Connecting(CheckingForRouteToDnsEndpoint)
2025-07-28T08:49:43.504Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Connecting(CheckingForRouteToDnsEndpoint)
2025-07-28T08:49:43.505Z DEBUG firewall::change: Firewall allow captive portal detection
2025-07-28T08:49:43.505Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T08:49:43.505Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=452.962µs
2025-07-28T08:49:43.513Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T08:49:43.513Z DEBUG captive_portal: Running captive portal detections [legacy,os-detected,http-redirect[cloudflareportal.com],no-dns-redirect[cloudflareportal.com],expected-content,third-party]
2025-07-28T08:49:43.514Z DEBUG run{detection_type=os-detected}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T08:49:43.514Z  INFO captive_portal::detections::http_redirect: Checking for captive portal with HTTP-Redirect detection host="connectivitycheck.gstatic.com" ips=None interface=Some("eth0") bind_addr=Some(**********)
2025-07-28T08:49:43.514Z  INFO captive_portal::detections::http_redirect: Checking for captive portal with HTTP-Redirect detection host="www.msftconnecttest.com" ips=None interface=Some("eth0") bind_addr=Some(**********)
2025-07-28T08:49:43.514Z  INFO run{detection_type=no-dns-redirect[cloudflareportal.com]}: captive_portal::detections::http_redirect: Checking for captive portal with HTTP-Redirect detection host="cloudflareportal.com" ips=Some([**********, **************, **********, 2606:4700:7::1, 2606:4700::1]) interface=Some("eth0") bind_addr=Some(**********)
2025-07-28T08:49:43.514Z  INFO run{detection_type=legacy}: captive_portal::detections::legacy: Checking for captive portal with legacy detection urls=["cloudflareportal.com", "cloudflareok.com", "cloudflarecp.com"]
2025-07-28T08:49:43.515Z  INFO captive_portal::detections::http_redirect: Checking for captive portal with HTTP-Redirect detection host="captive.apple.com" ips=None interface=Some("eth0") bind_addr=Some(**********)
2025-07-28T08:49:43.516Z  INFO run{detection_type=legacy}: captive_portal::detections::legacy: Successfully resolved DNS queries
2025-07-28T08:49:43.516Z  INFO run{detection_type=http-redirect[cloudflareportal.com]}: captive_portal::detections::http_redirect: Checking for captive portal with HTTP-Redirect detection host="cloudflareportal.com" ips=None interface=Some("eth0") bind_addr=Some(**********)
2025-07-28T08:49:43.524Z DEBUG captive_portal::detections::http_redirect: 204 response for HTTP test. No captive portal detected
2025-07-28T08:49:43.526Z DEBUG captive_portal::detections::http_redirect: 200 response for HTTP test. No captive portal detected
2025-07-28T08:49:43.526Z DEBUG captive_portal::detections::http_redirect: 200 response for HTTP test. No captive portal detected
2025-07-28T08:49:43.526Z DEBUG run{detection_type=third-party}: captive_portal::detections::third_party: Third party detections complete detections=[("connectivitycheck.gstatic.com", Ok(NoCaptivePortalDetected)), ("www.msftconnecttest.com", Ok(NoCaptivePortalDetected)), ("captive.apple.com", Ok(NoCaptivePortalDetected))]
2025-07-28T08:49:43.526Z DEBUG run{detection_type=third-party}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T08:49:43.537Z DEBUG run{detection_type=expected-content}: captive_portal::detections::expected_content: 200 response and correct content for HTTP Content test. No captive portal detected
2025-07-28T08:49:43.537Z DEBUG run{detection_type=expected-content}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T08:49:43.538Z DEBUG run{detection_type=http-redirect[cloudflareportal.com]}: captive_portal::detections::http_redirect: 204 response for HTTP test. No captive portal detected
2025-07-28T08:49:43.538Z DEBUG run{detection_type=http-redirect[cloudflareportal.com]}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T08:49:43.542Z DEBUG main_loop: warp::warp::proxying_dns: Connectivity Check resolved query for connectivity.cloudflareclient.com resolved_ips=[**************, **************]
2025-07-28T08:49:43.542Z DEBUG main_loop: dns_proxy::resolver: Ending DoH connection stats=establish_time:2025-07-28 8:49:43.514049013 +00:00:00, ip:2606:4700:4700::1111, attempted_queries:1, successful_queries:1, certificate_errors:0, timeout_errors:0, no_connection_errors:0, proto_errors:0, io_errors:0, msg_errors:0
2025-07-28T08:49:43.542Z DEBUG main_loop: warp::warp::proxying_dns: Acquiring bind semaphore available_permits=1
2025-07-28T08:49:43.543Z DEBUG main_loop: warp::warp::proxying_dns: Binding UDP and TCP sockets dns_servers=[*********, *********]
2025-07-28T08:49:43.543Z DEBUG main_loop: warp::warp::proxying_dns: Successfully bound DNS listen sockets udp_sockets=2 tcp_sockets=2
2025-07-28T08:49:43.544Z DEBUG run{detection_type=no-dns-redirect[cloudflareportal.com]}: captive_portal::detections::http_redirect: 204 response for HTTP test. No captive portal detected
2025-07-28T08:49:43.544Z DEBUG run{detection_type=no-dns-redirect[cloudflareportal.com]}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T08:49:43.545Z DEBUG main_loop: warp::warp::proxying_dns: Read system DNS configuration initial_dns_config=(ResolverConfig { domain: None, search: [], name_servers: NameServerConfigGroup([NameServerConfig { socket_addr: 127.0.0.11:53, protocol: Udp, tls_dns_name: None, trust_negative_responses: false, tls_config: None, bind_addr: None }, NameServerConfig { socket_addr: 127.0.0.11:53, protocol: Tcp, tls_dns_name: None, trust_negative_responses: false, tls_config: None, bind_addr: None }], None) }, ResolverOpts { ndots: 0, timeout: 5s, attempts: 2, rotate: false, check_names: true, edns0: false, validate: false, ip_strategy: Ipv4thenIpv6, cache_size: 32, use_hosts_file: true, positive_min_ttl: None, negative_min_ttl: None, positive_max_ttl: None, negative_max_ttl: None, num_concurrent_reqs: 2, preserve_intermediates: true, try_tcp_on_error: false, server_ordering_strategy: QueryStatistics, recursion_desired: true, authentic_data: false, shuffle_dns_servers: false })
2025-07-28T08:49:43.545Z DEBUG main_loop: warp::warp::proxying_dns: Initializing DNS restore settings net.v4_ifaces=[eth0; **********; Ethernet; Gateway: Some(**********)] net.v6_ifaces=[]
2025-07-28T08:49:43.545Z DEBUG warp_dns: Starting DnsOverWarp task tun_address=**********:0
2025-07-28T08:49:43.545Z DEBUG main_loop: warp::warp::dns_recovery::unix: Applying DNS settings [*********, *********, ::ffff:*********, ::ffff:*********]
2025-07-28T08:49:43.545Z  INFO dns_proxy::proxy: Default fallbacks configured default_fallback_ips=[127.0.0.11:53] config=ResolverConfig { domain: None, search: [], name_servers: NameServerConfigGroup([NameServerConfig { socket_addr: 127.0.0.11:53, protocol: Udp, tls_dns_name: None, trust_negative_responses: true, tls_config: None, bind_addr: None }, NameServerConfig { socket_addr: 127.0.0.11:53, protocol: Tcp, tls_dns_name: None, trust_negative_responses: true, tls_config: None, bind_addr: None }], None) } sys_options=ResolverOpts { ndots: 0, timeout: 11s, attempts: 0, rotate: false, check_names: true, edns0: true, validate: false, ip_strategy: Ipv4thenIpv6, cache_size: 32, use_hosts_file: true, positive_min_ttl: None, negative_min_ttl: None, positive_max_ttl: None, negative_max_ttl: None, num_concurrent_reqs: 8, preserve_intermediates: true, try_tcp_on_error: false, server_ordering_strategy: QueryStatistics, recursion_desired: true, authentic_data: false, shuffle_dns_servers: false }
2025-07-28T08:49:43.546Z DEBUG main_loop: network_info::linux::dns_manager: detected system dns manager file_owner=Unknown os_configuration_owner=File
2025-07-28T08:49:43.550Z  INFO run{detection_type=legacy}: captive_portal::detections::legacy: Successfully retrieved HTTPs page
2025-07-28T08:49:43.550Z DEBUG run{detection_type=legacy}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T08:49:43.550Z DEBUG captive_portal: Captive portal detection completed results=[("os-detected", Ok(NoCaptivePortalDetected)), ("third-party", Ok(NoCaptivePortalDetected)), ("expected-content", Ok(NoCaptivePortalDetected)), ("http-redirect[cloudflareportal.com]", Ok(NoCaptivePortalDetected)), ("no-dns-redirect[cloudflareportal.com]", Ok(NoCaptivePortalDetected)), ("legacy", Ok(NoCaptivePortalDetected))]
2025-07-28T08:49:43.550Z DEBUG firewall::change: Firewall disallow captive portal detection
2025-07-28T08:49:43.552Z  INFO main_loop: warp::warp_service: WARP status: Connecting(PerformingConnectivityChecks)
2025-07-28T08:49:43.552Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Connecting(PerformingConnectivityChecks)
2025-07-28T08:49:43.552Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T08:49:43.552Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Connecting(PerformingConnectivityChecks)
2025-07-28T08:49:43.553Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=796.085µs
2025-07-28T08:49:43.553Z DEBUG connectivity_check: Resolved connectivity-check.warp-svc. to [*********, *********, ::ffff:*********, ::ffff:*********]
2025-07-28T08:49:43.558Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T08:49:43.558Z DEBUG firewall::captive_portal: Firewall captive portal detection expired
2025-07-28T08:49:43.573Z DEBUG do_trace_inner{host="engage.cloudflareclient.com" host_with_protocol="https://engage.cloudflareclient.com" timeout=4s sockets=[*************:443, [2606:4700:102::3]:443] con_check_meta=ConnectivityCheckMetadata { proxy_local_addr: None }}: connectivity_check: fl=929f58
h=engage.cloudflareclient.com
ip=***************
ts=1753692583.57
visit_scheme=https
uag=WARP for Linux
colo=FRA
sliver=none
http=http/1.1
loc=DE
tls=TLSv1.3
sni=plaintext
warp=off
gateway=off
rbi=off
kex=X25519

2025-07-28T08:49:43.585Z DEBUG do_trace_inner{host="connectivity.cloudflareclient.com" host_with_protocol="https://connectivity.cloudflareclient.com" timeout=4s sockets=[*************:443, [2606:4700:102::4]:443] con_check_meta=ConnectivityCheckMetadata { proxy_local_addr: None }}: connectivity_check: fl=71f822
h=connectivity.cloudflareclient.com
ip=**************
ts=1753692583.581
visit_scheme=https
uag=WARP for Linux
colo=FRA
sliver=none
http=http/1.1
loc=DE
tls=TLSv1.3
sni=plaintext
warp=off
gateway=off
rbi=off
kex=X25519

2025-07-28T08:49:43.585Z DEBUG main_loop: warp::warp::connectivity_check: Trace status: Ok(TraceResult { metal_id: "71f822", colo: "FRA", ip: ************** })
2025-07-28T08:49:43.586Z  INFO main_loop: warp::warp_service: WARP status: Connecting(ValidatingDnsConfiguration)
2025-07-28T08:49:43.586Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T08:49:43.586Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Connecting(ValidatingDnsConfiguration)
2025-07-28T08:49:43.586Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Connecting(ValidatingDnsConfiguration)
2025-07-28T08:49:43.586Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=713.327µs
2025-07-28T08:49:43.594Z DEBUG main_loop: connectivity_check: Resolved connectivity.cloudflareclient.com to [2606:4700:7::a29f:8a41, 2606:4700:7::a29f:8941, **************, **************]
2025-07-28T08:49:43.594Z DEBUG main_loop: warp::warp::warp_connection: Connect finished
2025-07-28T08:49:43.594Z DEBUG main_loop: warp::warp::warp_connection: start_status=Ok(())
2025-07-28T08:49:43.594Z DEBUG main_loop: warp::warp::controller: self.warp future resolved
2025-07-28T08:49:43.594Z  INFO main_loop: warp::warp_service: WARP status: Connected
2025-07-28T08:49:43.594Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="tunnel_taskset_errors_fut"
2025-07-28T08:49:43.594Z DEBUG actor_statistics::handler: Recording gauge into histogram context=RecordIntoHistogramInfo { gauge_key: KeyName("conn_attempts_current"), histogram_key: KeyName("conn_attempts_all"), component: "happy_eyeballs" }
2025-07-28T08:49:43.594Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="tunnel_taskset_errors_fut" elapsed=355.817µs
2025-07-28T08:49:43.594Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T08:49:43.595Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Connected
2025-07-28T08:49:43.595Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Connected
2025-07-28T08:49:43.595Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=976.138µs
2025-07-28T08:49:46.022Z DEBUG main_loop:handle_update{update=NetworkInfoChanged}: warp::warp_service::actor_compat: Handling network status change old_info=IPv4: [eth0; **********; Ethernet; Gateway: Some(**********)], DNS servers:,  127.0.0.11:53
 new_info=IPv4: [eth0; **********; Ethernet; Gateway: Some(**********)], DNS servers:,  *********:53,  *********:53

2025-07-28T08:49:46.023Z DEBUG main_loop:handle_update{update=NetworkInfoChanged}: warp::warp::proxying_dns: Reapplying DNS settings when the list of DNS servers contains ourselves new_info.dns_servers=[*********:53, *********:53] ADVERTISED_DNS_TARGETS=[*********, *********, ::ffff:*********, ::ffff:*********]
2025-07-28T08:49:46.023Z  INFO main_loop:handle_update{update=NetworkInfoChanged}: warp::warp::proxying_dns: Reinforcing old DNS settings old_info.dns_servers=[127.0.0.11:53] new_info.dns_servers=[*********:53, *********:53]
2025-07-28T08:49:46.023Z DEBUG main_loop:handle_update{update=NetworkInfoChanged}: warp::warp::dns_recovery::unix: Reverting DNS settings old_dns=RestoreDNS { action: OverwriteResolv([35, 32, 71, 101, 110, 101, 114, 97, 116, 101, 100, 32, 98, 121, 32, 68, 111, 99, 107, 101, 114, 32, 69, 110, 103, 105, 110, 101, 46, 10, 35, 32, 84, 104, 105, 115, 32, 102, 105, 108, 101, 32, 99, 97, 110, 32, 98, 101, 32, 101, 100, 105, 116, 101, 100, 59, 32, 68, 111, 99, 107, 101, 114, 32, 69, 110, 103, 105, 110, 101, 32, 119, 105, 108, 108, 32, 110, 111, 116, 32, 109, 97, 107, 101, 32, 102, 117, 114, 116, 104, 101, 114, 32, 99, 104, 97, 110, 103, 101, 115, 32, 111, 110, 99, 101, 32, 105, 116, 10, 35, 32, 104, 97, 115, 32, 98, 101, 101, 110, 32, 109, 111, 100, 105, 102, 105, 101, 100, 46, 10, 10, 110, 97, 109, 101, 115, 101, 114, 118, 101, 114, 32, 49, 50, 55, 46, 48, 46, 48, 46, 49, 49, 10, 111, 112, 116, 105, 111, 110, 115, 32, 110, 100, 111, 116, 115, 58, 48, 10, 10, 35, 32, 66, 97, 115, 101, 100, 32, 111, 110, 32, 104, 111, 115, 116, 32, 102, 105, 108, 101, 58, 32, 39, 47, 101, 116, 99, 47, 114, 101, 115, 111, 108, 118, 46, 99, 111, 110, 102, 39, 32, 40, 105, 110, 116, 101, 114, 110, 97, 108, 32, 114, 101, 115, 111, 108, 118, 101, 114, 41, 10, 35, 32, 69, 120, 116, 83, 101, 114, 118, 101, 114, 115, 58, 32, 91, 104, 111, 115, 116, 40, 52, 54, 46, 51, 56, 46, 50, 50, 53, 46, 50, 51, 48, 41, 32, 104, 111, 115, 116, 40, 52, 54, 46, 51, 56, 46, 50, 53, 50, 46, 50, 51, 48, 41, 93, 10, 35, 32, 79, 118, 101, 114, 114, 105, 100, 101, 115, 58, 32, 91, 93, 10, 35, 32, 79, 112, 116, 105, 111, 110, 32, 110, 100, 111, 116, 115, 32, 102, 114, 111, 109, 58, 32, 105, 110, 116, 101, 114, 110, 97, 108, 10]), resolv_conf_owner: Unknown, os_config_owner: File, previous_nameservers: Some([127.0.0.11]), warp_nameservers: [*********, *********, ::ffff:*********, ::ffff:*********] }
2025-07-28T08:49:46.023Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="main_loop_compat_rx"
2025-07-28T08:49:46.032Z  WARN main_loop:handle_update{update=NetworkInfoChanged}: network_info::linux::dns_manager: Failed to notify system DNS manager of change. DNS may be broken if systemd-resolved is used owner=Unknown os_config_owner=File err=Failed to restart systemd-resolved

Caused by:
    org.freedesktop.DBus.Error.ServiceUnknown: The name org.freedesktop.systemd1 was not provided by any .service files
2025-07-28T08:49:46.033Z DEBUG main_loop:handle_update{update=NetworkInfoChanged}: warp::warp::dns_recovery::unix: DNS settings reverted (<resolv.conf restored>)
2025-07-28T08:49:46.033Z DEBUG main_loop:handle_update{update=NetworkInfoChanged}: warp::warp::dns_recovery::unix: Applying DNS settings [*********, *********, ::ffff:*********, ::ffff:*********]
2025-07-28T08:49:46.034Z DEBUG main_loop:handle_update{update=NetworkInfoChanged}: network_info::linux::dns_manager: detected system dns manager file_owner=Unknown os_configuration_owner=File
2025-07-28T08:49:46.044Z DEBUG main_loop:handle_update{update=NetworkInfoChanged}: warp::warp::proxying_dns: Skip notifying DNS changes as servers was empty or only contained our own advertised IPs. servers=[*********:53, *********:53] ADVERTISED_DNS_TARGETS=[*********, *********, ::ffff:*********, ::ffff:*********]
2025-07-28T08:49:46.045Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="main_loop_compat_rx" elapsed=22.251015ms
2025-07-28T08:49:46.975Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="main_loop_compat_rx"
2025-07-28T08:49:46.975Z DEBUG main_loop:handle_command: warp::warp_service::actor_compat: Force-fetching registration config id=2d6c00fe-5a6c-46fb-a62a-b9aa5ed62ace
2025-07-28T08:49:46.976Z DEBUG main_loop:handle_command: warp_api_client::executor: Spawning API request for GetRegistrationConfig request_id=f721a4dc-c9c0-4bcf-9bfe-d0b9896a53f6
2025-07-28T08:49:46.976Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="main_loop_compat_rx" elapsed=799.059µs
2025-07-28T08:49:46.976Z DEBUG warp_api_client::apis::registration: Getting registration registration_id=Client(ConsumerRegistrationId(2d6c00fe-5a6c-46fb-a62a-b9aa5ed62ace))
2025-07-28T08:49:46.976Z DEBUG warp_api_client::client: Sending API request GET https://api.cloudflareclient.com.//v0/reg/2d6c00fe-5a6c-46fb-a62a-b9aa5ed62ace
2025-07-28T08:49:47.025Z DEBUG warp_api_endpoints::payloads: Warp endpoint override ports ports=[443, 500, 1701, 4500, 4443, 8443, 8095]
2025-07-28T08:49:47.025Z DEBUG main_loop: warp_api_client::executor: Returning API response for GetRegistrationConfig request_id=f721a4dc-c9c0-4bcf-9bfe-d0b9896a53f6
2025-07-28T08:49:47.025Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="api_responses"
2025-07-28T08:49:47.025Z DEBUG run: warp_settings::manager: LayerManager update: NetworkPolicy(Some(NetworkPolicySettings { onboarding: None, operation_mode: None, disable_auto_fallback: None, fallback_domains: None, proxy_port: None, split_config: None, gateway_id: None, support_url: None, allow_mode_switch: None, switch_locked: None, auto_connect: None, captive_portal: None, organization: None, allow_updates: None, allowed_to_leave: None, profile_id: None, lan_allow_settings: Some(LanAllowSettings { minutes: None, subnet_size: SubnetSize(24) }), warp_tunnel_protocol: Some(Masque), masque_settings: None, register_interface_ip_with_dns: None, anycast_split_ips: Some(AnycastSplitIps { always_exclude: [*************, 2606:4700:102::3], always_include: [*************, 2606:4700:102::4] }), firewall_scope: None, sccm_vpn_boundary_support: None, enable_post_quantum: None }))
2025-07-28T08:49:47.026Z DEBUG main_loop: warp::warp_service::api_handlers: Registration updated current_registration=Registered(reg=2d6c00fe-5a6c-46fb-a62a-b9aa5ed62ace) disposition=CheckAndUpdate
2025-07-28T08:49:47.026Z DEBUG main_loop: warp::warp_service::api_handlers: Registration is up to date, updating refresh time
2025-07-28T08:49:47.026Z DEBUG main_loop: warp::warp_service::api_handlers: Registration refresh time updated 0.00 hours old, refresh at 2025-07-29 8:49:47.025458997 +00:00:00
Registration: 2d6c00fe-5a6c-46fb-a62a-b9aa5ed62ace
Device Identifiers: System User
Auth Method: Consumer
Public key: 3059301306072a8648ce3d020106082a8648ce3d03010703420004113924ef48373289b7e26ff95857ba8b4db98b523b27141ed843834bd7e10785f22e431af487429a370745973dff12d1ba6c99d1ad8dce79a9faa5f050a52165
Interface: WarpEndpoint { v4: **********, v6: 2606:4700:110:8bb0:2b1:9371:a882:ec04 }
Endpoints: [WarpSocketAddrPair { v4: *************:443, v6: [2606:4700:103::2]:443 }, WarpSocketAddrPair { v4: *************:500, v6: [2606:4700:103::2]:500 }, WarpSocketAddrPair { v4: *************:1701, v6: [2606:4700:103::2]:1701 }, WarpSocketAddrPair { v4: *************:4500, v6: [2606:4700:103::2]:4500 }, WarpSocketAddrPair { v4: *************:4443, v6: [2606:4700:103::2]:4443 }, WarpSocketAddrPair { v4: *************:8443, v6: [2606:4700:103::2]:8443 }, WarpSocketAddrPair { v4: *************:8095, v6: [2606:4700:103::2]:8095 }]
Subnet CIDRS: None

Account: Free { id: AccountId(38d2eb15-31ea-470f-92a9-3c26b5351c42), license: "94e5DJS8-Wqh617X9-78cjuC59" }

2025-07-28T08:49:47.026Z  INFO main_loop: warp_storage::registration::registration_storage: Saving registration to active cache config_name=None username="WARPSecret"
2025-07-28T08:49:47.026Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="api_responses" elapsed=1.115942ms
2025-07-28T08:49:47.999Z  INFO actor_emergency_disconnect::handler: Polling API for latest emergency disconnect state
2025-07-28T08:49:53.001Z DEBUG actor_root_ca::handler: Starting root-ca actor loop
2025-07-28T08:49:53.003Z DEBUG handle_registration_changed: root_ca::store: Done installing certificate(s) total=0
2025-07-28T08:49:53.003Z DEBUG handle_registration_changed: root_ca::store: Done installing certificate(s) total=0
2025-07-28T08:49:58.390Z DEBUG slog: got a response for stream_id=8, headers=[":status: 200", "cf-team: 28f1d288740000db0e4ea24400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T08:50:13.391Z DEBUG slog: got a response for stream_id=12, headers=[":status: 200", "cf-team: 28f1d2c30d0000db0e4eb1b400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T08:50:20.480Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T08:50:27.136Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T08:50:28.390Z DEBUG slog: got a response for stream_id=16, headers=[":status: 200", "cf-team: 28f1d2fda40000db0e4eca4400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T08:50:43.389Z DEBUG slog: got a response for stream_id=20, headers=[":status: 200", "cf-team: 28f1d3383c0000db0e4efae400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T08:50:58.390Z DEBUG slog: got a response for stream_id=24, headers=[":status: 200", "cf-team: 28f1d372d40000db0e4f261400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T08:51:01.184Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T08:51:07.328Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T08:51:13.390Z DEBUG slog: got a response for stream_id=28, headers=[":status: 200", "cf-team: 28f1d3ad6d0000db0e4f33c400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T08:51:28.391Z DEBUG slog: got a response for stream_id=32, headers=[":status: 200", "cf-team: 28f1d3e8050000db0e4f5ec400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T08:51:37.988Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 2 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 1162 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 2 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.044493753000000025, count: 1162 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 280.0, count: 14 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 49.0, count: 18 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.000223454 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-07-28T08:51:37.988Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 2 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 1162 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 2 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.044493753000000025, count: 1162 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 280.0, count: 14 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 49.0, count: 18 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.000223454 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-07-28T08:51:38.065Z DEBUG warp_api_client::client: Method: POST, Path: v0/2d6c00fe-5a6c-46fb-a62a-b9aa5ed62ace/observe_events, StructuredLogsEndpoint { device_id: "2d6c00fe-5a6c-46fb-a62a-b9aa5ed62ace", logs: Array [Object {"event": String("UserToggledV0"), "toggle": String("on"), "warp_colo": String("none"), "warp_metal": String("none"), "account_name": String("\"\""), "account_id": String("\"38d2eb1531ea470f92a93c26b5351c42\""), "hostname": String("\"9b0cfffe86d9\""), "serial_number": String("\"\""), "version": Number(0), "client_sample_rate": Number(1), "app_version": String("2025.5.943.0"), "os": String("Linux"), "os_version": String("6.1.81"), "timestamp": Number(****************), "session_id": String("e40f6ab3-311c-43be-bccd-ecb70220fb45")}] }
2025-07-28T08:51:38.429Z ERROR warp_api_client::client: Failed API Request endpoint=StructuredLogsEndpoint { device_id: "2d6c00fe-5a6c-46fb-a62a-b9aa5ed62ace", logs: Array [Object {"event": String("UserToggledV0"), "toggle": String("on"), "warp_colo": String("none"), "warp_metal": String("none"), "account_name": String("\"\""), "account_id": String("\"38d2eb1531ea470f92a93c26b5351c42\""), "hostname": String("\"9b0cfffe86d9\""), "serial_number": String("\"\""), "version": Number(0), "client_sample_rate": Number(1), "app_version": String("2025.5.943.0"), "os": String("Linux"), "os_version": String("6.1.81"), "timestamp": Number(****************), "session_id": String("e40f6ab3-311c-43be-bccd-ecb70220fb45")}] } err=ApiFailure(500, ApiErrors { errors: [], other: {"result": Null, "messages": Array [], "success": Bool(false)} })
2025-07-28T08:51:38.429Z ERROR warp_api_client::client: API request error ApiFailure(500, ApiErrors { errors: [], other: {"result": Null, "messages": Array [], "success": Bool(false)} }), retrying in 1.709258812s
2025-07-28T08:51:40.443Z ERROR warp_api_client::client: Failed API Request endpoint=StructuredLogsEndpoint { device_id: "2d6c00fe-5a6c-46fb-a62a-b9aa5ed62ace", logs: Array [Object {"event": String("UserToggledV0"), "toggle": String("on"), "warp_colo": String("none"), "warp_metal": String("none"), "account_name": String("\"\""), "account_id": String("\"38d2eb1531ea470f92a93c26b5351c42\""), "hostname": String("\"9b0cfffe86d9\""), "serial_number": String("\"\""), "version": Number(0), "client_sample_rate": Number(1), "app_version": String("2025.5.943.0"), "os": String("Linux"), "os_version": String("6.1.81"), "timestamp": Number(****************), "session_id": String("e40f6ab3-311c-43be-bccd-ecb70220fb45")}] } err=ApiFailure(500, ApiErrors { errors: [], other: {"success": Bool(false), "result": Null, "messages": Array []} })
2025-07-28T08:51:40.444Z ERROR warp_api_client::client: API request error ApiFailure(500, ApiErrors { errors: [], other: {"success": Bool(false), "result": Null, "messages": Array []} }), retrying in 3.648406857s
2025-07-28T08:51:43.390Z DEBUG slog: got a response for stream_id=36, headers=[":status: 200", "cf-team: 28f1d4229d0000db0e4f731400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T08:51:43.484Z DEBUG tunnel_loop{protocol="masque" con_id="e0772a114ac940e857ece8392c2b70e6d18d2991"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 5, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-07-28T08:51:44.448Z ERROR warp_api_client::client: Failed API Request endpoint=StructuredLogsEndpoint { device_id: "2d6c00fe-5a6c-46fb-a62a-b9aa5ed62ace", logs: Array [Object {"event": String("UserToggledV0"), "toggle": String("on"), "warp_colo": String("none"), "warp_metal": String("none"), "account_name": String("\"\""), "account_id": String("\"38d2eb1531ea470f92a93c26b5351c42\""), "hostname": String("\"9b0cfffe86d9\""), "serial_number": String("\"\""), "version": Number(0), "client_sample_rate": Number(1), "app_version": String("2025.5.943.0"), "os": String("Linux"), "os_version": String("6.1.81"), "timestamp": Number(****************), "session_id": String("e40f6ab3-311c-43be-bccd-ecb70220fb45")}] } err=ApiFailure(500, ApiErrors { errors: [], other: {"result": Null, "success": Bool(false), "messages": Array []} })
2025-07-28T08:51:44.448Z ERROR warp_api_client::client: API request error ApiFailure(500, ApiErrors { errors: [], other: {"result": Null, "success": Bool(false), "messages": Array []} }), retrying in 2.927517145s
2025-07-28T08:51:47.747Z ERROR warp_api_client::client: Failed API Request endpoint=StructuredLogsEndpoint { device_id: "2d6c00fe-5a6c-46fb-a62a-b9aa5ed62ace", logs: Array [Object {"event": String("UserToggledV0"), "toggle": String("on"), "warp_colo": String("none"), "warp_metal": String("none"), "account_name": String("\"\""), "account_id": String("\"38d2eb1531ea470f92a93c26b5351c42\""), "hostname": String("\"9b0cfffe86d9\""), "serial_number": String("\"\""), "version": Number(0), "client_sample_rate": Number(1), "app_version": String("2025.5.943.0"), "os": String("Linux"), "os_version": String("6.1.81"), "timestamp": Number(****************), "session_id": String("e40f6ab3-311c-43be-bccd-ecb70220fb45")}] } err=ApiFailure(500, ApiErrors { errors: [], other: {"result": Null, "messages": Array [], "success": Bool(false)} })
2025-07-28T08:51:58.390Z DEBUG slog: got a response for stream_id=40, headers=[":status: 200", "cf-team: 28f1d45d340000db0e4f8d0400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T08:52:13.390Z DEBUG slog: got a response for stream_id=44, headers=[":status: 200", "cf-team: 28f1d497cc0000db0e4fade400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T08:52:21.312Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T08:52:27.200Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T08:52:28.390Z DEBUG slog: got a response for stream_id=48, headers=[":status: 200", "cf-team: 28f1d4d2640000db0e4fcd0400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T08:52:43.390Z DEBUG slog: got a response for stream_id=52, headers=[":status: 200", "cf-team: 28f1d50cfc0000db0e4fed9400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T08:52:58.390Z DEBUG slog: got a response for stream_id=56, headers=[":status: 200", "cf-team: 28f1d547950000db0e50024400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T08:53:02.016Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T08:53:07.140Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T08:53:13.390Z DEBUG slog: got a response for stream_id=60, headers=[":status: 200", "cf-team: 28f1d5822c0000db0e50100400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T08:53:28.390Z DEBUG slog: got a response for stream_id=64, headers=[":status: 200", "cf-team: 28f1d5bcc50000db0e50398400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T08:53:37.988Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 2 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 2362 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 2 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.09025339199999988, count: 2362 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 280.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 61.0, count: 32 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.005444652 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-07-28T08:53:37.988Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 2 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 2362 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 2 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.09025339199999988, count: 2362 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 280.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 61.0, count: 32 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.005444652 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-07-28T08:53:43.390Z DEBUG slog: got a response for stream_id=68, headers=[":status: 200", "cf-team: 28f1d5f75c0000db0e5047b400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T08:53:43.484Z DEBUG tunnel_loop{protocol="masque" con_id="e0772a114ac940e857ece8392c2b70e6d18d2991"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 6, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-07-28T08:53:58.390Z DEBUG slog: got a response for stream_id=72, headers=[":status: 200", "cf-team: 28f1d631f40000db0e505b6400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T08:54:11.648Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T08:54:13.390Z DEBUG slog: got a response for stream_id=76, headers=[":status: 200", "cf-team: 28f1d66c8c0000db0e5076c400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T08:54:18.560Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T08:54:18.562Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T08:54:28.390Z DEBUG slog: got a response for stream_id=80, headers=[":status: 200", "cf-team: 28f1d6a7240000db0e50929400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T08:54:43.390Z DEBUG slog: got a response for stream_id=84, headers=[":status: 200", "cf-team: 28f1d6e1bd0000db0e50adb400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T08:54:46.464Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T08:54:55.168Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T08:54:58.390Z DEBUG slog: got a response for stream_id=88, headers=[":status: 200", "cf-team: 28f1d71c540000db0e50c5b400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T08:55:13.390Z DEBUG slog: got a response for stream_id=92, headers=[":status: 200", "cf-team: 28f1d756ec0000db0e50d58400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T08:55:23.328Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T08:55:28.389Z DEBUG slog: got a response for stream_id=96, headers=[":status: 200", "cf-team: 28f1d791840000db0e50f3c400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T08:55:31.264Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T08:55:37.988Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "device-state.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 3562 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 2 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 2 }], summaries: [SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 74.0, count: 44 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 280.0, count: 14 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.13642449099999962, count: 3562 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.993895337 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-07-28T08:55:37.988Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "device-state.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 3562 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 2 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 2 }], summaries: [SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 74.0, count: 44 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 280.0, count: 14 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.13642449099999962, count: 3562 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.993895337 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-07-28T08:55:43.390Z DEBUG slog: got a response for stream_id=100, headers=[":status: 200", "cf-team: 28f1d7cc1d0000db0e51092400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T08:55:43.483Z DEBUG tunnel_loop{protocol="masque" con_id="e0772a114ac940e857ece8392c2b70e6d18d2991"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 7, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-07-28T08:55:58.144Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T08:55:58.390Z DEBUG slog: got a response for stream_id=104, headers=[":status: 200", "cf-team: 28f1d806b40000db0e511e6400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T08:56:03.524Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T08:56:13.390Z DEBUG slog: got a response for stream_id=108, headers=[":status: 200", "cf-team: 28f1d8414d0000db0e512e9400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T08:56:28.390Z DEBUG slog: got a response for stream_id=112, headers=[":status: 200", "cf-team: 28f1d87be40000db0e5143c400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T08:56:30.912Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T08:56:39.360Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T08:56:43.390Z DEBUG slog: got a response for stream_id=116, headers=[":status: 200", "cf-team: 28f1d8b67c0000db0e51549400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T08:56:58.390Z DEBUG slog: got a response for stream_id=120, headers=[":status: 200", "cf-team: 28f1d8f1140000db0e51650400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T08:57:07.776Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T08:57:10.297Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T08:57:13.390Z DEBUG slog: got a response for stream_id=124, headers=[":status: 200", "cf-team: 28f1d92bad0000db0e518f0400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T08:57:15.200Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T08:57:15.456Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T08:57:28.390Z DEBUG slog: got a response for stream_id=128, headers=[":status: 200", "cf-team: 28f1d966440000db0e51a9c400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T08:57:37.988Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 4762 }], summaries: [SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 88.0, count: 58 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.1805430579999999, count: 4762 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 280.0, count: 14 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.007535428 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-07-28T08:57:37.988Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 4762 }], summaries: [SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 88.0, count: 58 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.1805430579999999, count: 4762 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 280.0, count: 14 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.007535428 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-07-28T08:57:42.592Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T08:57:43.390Z DEBUG slog: got a response for stream_id=132, headers=[":status: 200", "cf-team: 28f1d9a0dd0000db0e51ca6400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T08:57:43.484Z DEBUG tunnel_loop{protocol="masque" con_id="e0772a114ac940e857ece8392c2b70e6d18d2991"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 7, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-07-28T08:57:48.480Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T08:57:58.390Z DEBUG slog: got a response for stream_id=136, headers=[":status: 200", "cf-team: 28f1d9db750000db0e51e4a400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T08:58:13.390Z DEBUG slog: got a response for stream_id=140, headers=[":status: 200", "cf-team: 28f1da160d0000db0e520ce400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T08:58:15.360Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T08:58:22.016Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T08:58:22.018Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T08:58:28.390Z DEBUG slog: got a response for stream_id=144, headers=[":status: 200", "cf-team: 28f1da50a50000db0e521c3400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T08:58:43.390Z DEBUG slog: got a response for stream_id=148, headers=[":status: 200", "cf-team: 28f1da8b3d0000db0e522d8400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T08:58:57.092Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T08:58:58.390Z DEBUG slog: got a response for stream_id=152, headers=[":status: 200", "cf-team: 28f1dac5d50000db0e52427400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T08:59:03.232Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T08:59:13.390Z DEBUG slog: got a response for stream_id=156, headers=[":status: 200", "cf-team: 28f1db006c0000db0e5250a400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T08:59:28.390Z DEBUG slog: got a response for stream_id=160, headers=[":status: 200", "cf-team: 28f1db3b050000db0e525e0400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T08:59:37.988Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 5962 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 2 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }], summaries: [SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 94.0, count: 70 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 280.0, count: 14 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.22529357799999955, count: 5962 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.005908 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-07-28T08:59:37.988Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 5962 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 2 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }], summaries: [SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 94.0, count: 70 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 280.0, count: 14 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.22529357799999955, count: 5962 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.005908 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-07-28T08:59:39.328Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T08:59:43.390Z DEBUG slog: got a response for stream_id=164, headers=[":status: 200", "cf-team: 28f1db759c0000db0e52766400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T08:59:43.483Z DEBUG tunnel_loop{protocol="masque" con_id="e0772a114ac940e857ece8392c2b70e6d18d2991"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 8, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-07-28T08:59:47.264Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T08:59:58.390Z DEBUG slog: got a response for stream_id=168, headers=[":status: 200", "cf-team: 28f1dbb0350000db0e5283f400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:00:13.390Z DEBUG slog: got a response for stream_id=172, headers=[":status: 200", "cf-team: 28f1dbeacd0000db0e52b87400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:00:22.336Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:00:28.392Z DEBUG slog: got a response for stream_id=176, headers=[":status: 200", "cf-team: 28f1dc25650000db0e52e34400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:00:31.296Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:00:43.390Z DEBUG slog: got a response for stream_id=180, headers=[":status: 200", "cf-team: 28f1dc5ffd0000db0e5300c400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:00:58.390Z DEBUG slog: got a response for stream_id=184, headers=[":status: 200", "cf-team: 28f1dc9a950000db0e5312d400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:01:07.392Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:01:13.390Z DEBUG slog: got a response for stream_id=188, headers=[":status: 200", "cf-team: 28f1dcd52d0000db0e53259400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:01:15.328Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:01:28.390Z DEBUG slog: got a response for stream_id=192, headers=[":status: 200", "cf-team: 28f1dd0fc50000db0e53427400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:01:37.988Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "device-state.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 2 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 7162 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 2 }], summaries: [SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 101.0, count: 82 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 280.0, count: 14 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.27169118800000003, count: 7162 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.999328805 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-07-28T09:01:37.988Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "device-state.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 2 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 7162 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 2 }], summaries: [SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 101.0, count: 82 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 280.0, count: 14 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.27169118800000003, count: 7162 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.999328805 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-07-28T09:01:43.390Z DEBUG slog: got a response for stream_id=196, headers=[":status: 200", "cf-team: 28f1dd4a5c0000db0e53571400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:01:43.483Z DEBUG tunnel_loop{protocol="masque" con_id="e0772a114ac940e857ece8392c2b70e6d18d2991"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 8, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-07-28T09:01:50.400Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:01:58.390Z DEBUG slog: got a response for stream_id=200, headers=[":status: 200", "cf-team: 28f1dd84f50000db0e53706400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:01:59.360Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:02:13.390Z DEBUG slog: got a response for stream_id=204, headers=[":status: 200", "cf-team: 28f1ddbf8d0000db0e537dd400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:02:28.390Z DEBUG slog: got a response for stream_id=208, headers=[":status: 200", "cf-team: 28f1ddfa250000db0e538c8400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:02:35.456Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:02:42.880Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:02:43.390Z DEBUG slog: got a response for stream_id=212, headers=[":status: 200", "cf-team: 28f1de34bc0000db0e539cb400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:02:58.390Z DEBUG slog: got a response for stream_id=216, headers=[":status: 200", "cf-team: 28f1de6f550000db0e53a94400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:03:13.390Z DEBUG slog: got a response for stream_id=220, headers=[":status: 200", "cf-team: 28f1dea9ec0000db0e53b36400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:03:18.464Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:03:24.096Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:03:28.390Z DEBUG slog: got a response for stream_id=224, headers=[":status: 200", "cf-team: 28f1dee4850000db0e53c77400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:03:37.988Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 8362 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 2 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 2 }], summaries: [SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 125.0, count: 94 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 280.0, count: 14 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.31684232200000095, count: 8362 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.999325313 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-07-28T09:03:37.988Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 8362 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 2 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 2 }], summaries: [SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 125.0, count: 94 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 280.0, count: 14 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.31684232200000095, count: 8362 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.999325313 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-07-28T09:03:43.390Z DEBUG slog: got a response for stream_id=228, headers=[":status: 200", "cf-team: 28f1df1f1c0000db0e53dff400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:03:43.484Z DEBUG tunnel_loop{protocol="masque" con_id="e0772a114ac940e857ece8392c2b70e6d18d2991"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 8, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-07-28T09:03:58.390Z DEBUG slog: got a response for stream_id=232, headers=[":status: 200", "cf-team: 28f1df59b40000db0e53f01400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:04:01.984Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:04:07.360Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:04:13.390Z DEBUG slog: got a response for stream_id=236, headers=[":status: 200", "cf-team: 28f1df944c0000db0e5403d400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:04:28.390Z DEBUG slog: got a response for stream_id=240, headers=[":status: 200", "cf-team: 28f1dfcee40000db0e542fa400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:04:43.390Z DEBUG slog: got a response for stream_id=244, headers=[":status: 200", "cf-team: 28f1e0097c0000db0e54515400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:04:46.528Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:04:54.464Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:04:58.390Z DEBUG slog: got a response for stream_id=248, headers=[":status: 200", "cf-team: 28f1e044140000db0e546d1400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:05:13.390Z DEBUG slog: got a response for stream_id=252, headers=[":status: 200", "cf-team: 28f1e07ead0000db0e54987400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:05:28.390Z DEBUG slog: got a response for stream_id=256, headers=[":status: 200", "cf-team: 28f1e0b9450000db0e54b81400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:05:33.632Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:05:37.988Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 9562 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }], summaries: [SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 137.0, count: 106 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 280.0, count: 14 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.3615183910000002, count: 9562 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.995096692 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-07-28T09:05:37.988Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 9562 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }], summaries: [SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 137.0, count: 106 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 280.0, count: 14 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.3615183910000002, count: 9562 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.995096692 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-07-28T09:05:39.264Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:05:43.390Z DEBUG slog: got a response for stream_id=260, headers=[":status: 200", "cf-team: 28f1e0f3dc0000db0e54c88400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:05:43.484Z DEBUG tunnel_loop{protocol="masque" con_id="e0772a114ac940e857ece8392c2b70e6d18d2991"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 8, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-07-28T09:05:58.390Z DEBUG slog: got a response for stream_id=264, headers=[":status: 200", "cf-team: 28f1e12e750000db0e54da5400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:06:13.390Z DEBUG slog: got a response for stream_id=268, headers=[":status: 200", "cf-team: 28f1e1690d0000db0e55070400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:06:18.688Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:06:25.088Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:06:28.390Z DEBUG slog: got a response for stream_id=272, headers=[":status: 200", "cf-team: 28f1e1a3a50000db0e551ac400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:06:43.390Z DEBUG slog: got a response for stream_id=276, headers=[":status: 200", "cf-team: 28f1e1de3d0000db0e55273400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:06:58.390Z DEBUG slog: got a response for stream_id=280, headers=[":status: 200", "cf-team: 28f1e218d50000db0e55306400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:07:03.744Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:07:11.168Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:07:13.391Z DEBUG slog: got a response for stream_id=284, headers=[":status: 200", "cf-team: 28f1e2536d0000db0e5544c400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:07:28.390Z DEBUG slog: got a response for stream_id=288, headers=[":status: 200", "cf-team: 28f1e28e050000db0e5559c400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:07:37.988Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 10762 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 2 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 280.0, count: 14 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.4067444200000017, count: 10762 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 150.0, count: 118 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.006820363 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-07-28T09:07:37.988Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 10762 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 2 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 280.0, count: 14 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.4067444200000017, count: 10762 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 150.0, count: 118 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.006820363 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-07-28T09:07:43.390Z DEBUG slog: got a response for stream_id=292, headers=[":status: 200", "cf-team: 28f1e2c89c0000db0e55790400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:07:43.484Z DEBUG tunnel_loop{protocol="masque" con_id="e0772a114ac940e857ece8392c2b70e6d18d2991"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 9, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-07-28T09:07:50.848Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:07:58.390Z DEBUG slog: got a response for stream_id=296, headers=[":status: 200", "cf-team: 28f1e303350000db0e5583d400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:07:59.296Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:08:13.390Z DEBUG slog: got a response for stream_id=300, headers=[":status: 200", "cf-team: 28f1e33dcd0000db0e55928400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:08:28.390Z DEBUG slog: got a response for stream_id=304, headers=[":status: 200", "cf-team: 28f1e378640000db0e559ef400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:08:37.952Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:08:43.328Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:08:43.390Z DEBUG slog: got a response for stream_id=308, headers=[":status: 200", "cf-team: 28f1e3b2fc0000db0e55b53400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:08:58.389Z DEBUG slog: got a response for stream_id=312, headers=[":status: 200", "cf-team: 28f1e3ed940000db0e55c29400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:09:13.390Z DEBUG slog: got a response for stream_id=316, headers=[":status: 200", "cf-team: 28f1e4282d0000db0e55d9b400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:09:23.008Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:09:28.390Z DEBUG slog: got a response for stream_id=320, headers=[":status: 200", "cf-team: 28f1e462c50000db0e55e97400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:09:31.204Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:09:37.988Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 2 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 11962 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 280.0, count: 14 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.45345912900000285, count: 11962 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 163.0, count: 130 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.006150067 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-07-28T09:09:37.988Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 2 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 11962 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 280.0, count: 14 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.45345912900000285, count: 11962 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 163.0, count: 130 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.006150067 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-07-28T09:09:43.390Z DEBUG slog: got a response for stream_id=324, headers=[":status: 200", "cf-team: 28f1e49d5d0000db0e56060400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:09:43.484Z DEBUG tunnel_loop{protocol="masque" con_id="e0772a114ac940e857ece8392c2b70e6d18d2991"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 9, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-07-28T09:09:49.632Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:09:55.264Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:09:58.390Z DEBUG slog: got a response for stream_id=328, headers=[":status: 200", "cf-team: 28f1e4d7f40000db0e561e7400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:10:13.390Z DEBUG slog: got a response for stream_id=332, headers=[":status: 200", "cf-team: 28f1e5128d0000db0e5629e400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:10:14.208Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:10:23.168Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:10:28.390Z DEBUG slog: got a response for stream_id=336, headers=[":status: 200", "cf-team: 28f1e54d250000db0e56381400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:10:42.880Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:10:43.390Z DEBUG slog: got a response for stream_id=340, headers=[":status: 200", "cf-team: 28f1e587bd0000db0e564c6400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:10:48.512Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:10:58.389Z DEBUG slog: got a response for stream_id=344, headers=[":status: 200", "cf-team: 28f1e5c2540000db0e5660c400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:11:07.456Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:11:13.389Z DEBUG slog: got a response for stream_id=348, headers=[":status: 200", "cf-team: 28f1e5fcec0000db0e5672e400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:11:15.136Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:11:28.390Z DEBUG slog: got a response for stream_id=352, headers=[":status: 200", "cf-team: 28f1e637850000db0e568a1400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:11:34.080Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:11:37.987Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 13162 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 2 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.501121558000003, count: 13162 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 280.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 175.0, count: 142 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.006114204 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-07-28T09:11:37.987Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 13162 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 2 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.501121558000003, count: 13162 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 280.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 175.0, count: 142 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.006114204 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-07-28T09:11:39.200Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:11:43.390Z DEBUG slog: got a response for stream_id=356, headers=[":status: 200", "cf-team: 28f1e6721c0000db0e56bda400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:11:43.484Z DEBUG tunnel_loop{protocol="masque" con_id="e0772a114ac940e857ece8392c2b70e6d18d2991"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 9, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-07-28T09:11:58.390Z DEBUG slog: got a response for stream_id=360, headers=[":status: 200", "cf-team: 28f1e6acb50000db0e56d5c400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:11:58.656Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:12:07.364Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:12:13.391Z DEBUG slog: got a response for stream_id=364, headers=[":status: 200", "cf-team: 28f1e6e74d0000db0e56f92400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:12:25.280Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:12:28.390Z DEBUG slog: got a response for stream_id=368, headers=[":status: 200", "cf-team: 28f1e721e50000db0e570d9400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:12:31.168Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:12:43.390Z DEBUG slog: got a response for stream_id=372, headers=[":status: 200", "cf-team: 28f1e75c7c0000db0e5720e400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:12:49.856Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:12:55.236Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:12:58.390Z DEBUG slog: got a response for stream_id=376, headers=[":status: 200", "cf-team: 28f1e797150000db0e5732e400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:13:13.390Z DEBUG slog: got a response for stream_id=380, headers=[":status: 200", "cf-team: 28f1e7d1ad0000db0e57424400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:13:14.432Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:13:23.136Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:13:28.390Z DEBUG slog: got a response for stream_id=384, headers=[":status: 200", "cf-team: 28f1e80c450000db0e575ab400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:13:37.988Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 14362 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 2 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 2 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.5502784890000018, count: 14362 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 280.0, count: 14 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 181.0, count: 154 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.000961404 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-07-28T09:13:37.988Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 14362 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 2 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 2 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.5502784890000018, count: 14362 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 280.0, count: 14 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 181.0, count: 154 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.000961404 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-07-28T09:13:41.056Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:13:43.390Z DEBUG slog: got a response for stream_id=388, headers=[":status: 200", "cf-team: 28f1e846dd0000db0e576b0400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:13:43.484Z DEBUG tunnel_loop{protocol="masque" con_id="e0772a114ac940e857ece8392c2b70e6d18d2991"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 9, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-07-28T09:13:47.200Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:13:58.390Z DEBUG slog: got a response for stream_id=392, headers=[":status: 200", "cf-team: 28f1e881740000db0e57800400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:14:13.390Z DEBUG slog: got a response for stream_id=396, headers=[":status: 200", "cf-team: 28f1e8bc0d0000db0e578d8400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:14:16.640Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:14:23.296Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:14:28.389Z DEBUG slog: got a response for stream_id=400, headers=[":status: 200", "cf-team: 28f1e8f6a40000db0e57a1f400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:14:43.390Z DEBUG slog: got a response for stream_id=404, headers=[":status: 200", "cf-team: 28f1e9313c0000db0e57b13400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:14:52.736Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:15:42.205Z  INFO warp::warp_service: Starting WarpService pid=20
2025-07-28T09:15:42.205Z  INFO warp::warp_service: Version: 2025.5.943.0
2025-07-28T09:15:42.212Z DEBUG warp_settings::raw_settings: Loading settings from file "/var/lib/cloudflare-warp/settings.json"
2025-07-28T09:15:42.213Z DEBUG warp_settings::raw_settings: Loading consumer settings from file "/var/lib/cloudflare-warp/consumer-settings.json"
2025-07-28T09:15:42.213Z  INFO warp_settings::manager: Consumer preferences not loaded e=No such file or directory (os error 2)
2025-07-28T09:15:42.213Z DEBUG warp_settings::manager::final_overrides: Loading final overrides settings from file "/var/lib/cloudflare-warp/final-overrides-settings.json"
2025-07-28T09:15:42.213Z  INFO warp_settings::manager: Final override settings not loaded e=No such file or directory (os error 2)
2025-07-28T09:15:42.213Z DEBUG warp_settings::manager: Starting local policy file watch parent_path="/var/lib/cloudflare-warp"
2025-07-28T09:15:42.281Z DEBUG warp_api_client::addresses: Initializing base API endpoint settings consumer=ConsumerEndpointConfig { ips: [************, *************, 2606:4700::6810:c052, 2606:4700::6810:1854], api_sni: ApiSni("api.cloudflareclient.com.") } zt=ZeroTrustEndpointConfig { ips: [***************, ***************, 2606:4700:7::a29f:8969, 2606:4700:7::a29f:8a69], api_sni: ApiSni("zero-trust-client.cloudflareclient.com."), notifications_sni: NotificationsSni("notifications.cloudflareclient.com") } compliance_environment=Normal
2025-07-28T09:15:42.281Z DEBUG warp::warp_service: DNS lock file detected. Did the daemon not exit gracefully?
2025-07-28T09:15:42.285Z  WARN network_info::linux::dns_manager: Failed to notify system DNS manager of change. DNS may be broken if systemd-resolved is used owner=Unknown os_config_owner=File err=Failed to restart systemd-resolved

Caused by:
    org.freedesktop.DBus.Error.ServiceUnknown: The name org.freedesktop.systemd1 was not provided by any .service files
2025-07-28T09:15:42.285Z DEBUG warp::warp_service: Restored default DNS resolvers from DNS lock file: <resolv.conf restored>
2025-07-28T09:15:42.286Z  WARN network_info::linux::dns_manager::dns_owner: Could not determine resolv.conf file owner: No comment line contained an owner slug
2025-07-28T09:15:42.291Z DEBUG warp::warp_service: Main loop refactor feature enabled
2025-07-28T09:15:42.292Z DEBUG actor_watchdog: Initializing watchdog actor
2025-07-28T09:15:42.292Z DEBUG watchdog: warp::watchdog: Kicking off watchdog
2025-07-28T09:15:42.292Z DEBUG actor_alternate_network: Initializing alternate network actor
2025-07-28T09:15:42.292Z DEBUG actor_main_loop_compat: Initializing main loop compat actor
2025-07-28T09:15:42.292Z DEBUG actor_statistics: Initializing statistics actor
2025-07-28T09:15:42.292Z DEBUG actor_connectivity: Initializing connectivity actor
2025-07-28T09:15:42.292Z DEBUG actor_remote_dex_command: Initializing remote DEX command actor
2025-07-28T09:15:42.292Z DEBUG actor_device_state: Initializing device state actor
2025-07-28T09:15:42.294Z DEBUG actor_emergency_disconnect: Initializing emergency disconnect actor
2025-07-28T09:15:42.294Z DEBUG actor_registration_verifier: Initializing registration verifier actor
2025-07-28T09:15:42.294Z DEBUG actor_root_ca: Initializing root-ca actor
2025-07-28T09:15:42.294Z DEBUG actor_root_ca: Sleeping for 15s to allow main loop boot-up
2025-07-28T09:15:42.295Z  INFO main_loop: warp_net::ipc: Bound ipc socket name="cloudflare-warp/warp_service" path="/run/cloudflare-warp/warp_service"
2025-07-28T09:15:42.295Z  INFO main_loop: warp::warp_service: Warp IPC listening on "cloudflare-warp/warp_service"
2025-07-28T09:15:42.295Z  INFO firewall::engine: Firewall engine running
2025-07-28T09:15:42.298Z DEBUG network_info::linux::power_notifier: Initializing dbus connection
2025-07-28T09:15:42.298Z DEBUG main_loop: warp_storage::registration::registration_storage: WARPSecret registration loaded 0.43 hours old, refresh at 2025-07-29 8:49:47.025458997 +00:00:00
Registration: 2d6c00fe-5a6c-46fb-a62a-b9aa5ed62ace
Device Identifiers: System User
Auth Method: Consumer
Public key: 3059301306072a8648ce3d020106082a8648ce3d03010703420004113924ef48373289b7e26ff95857ba8b4db98b523b27141ed843834bd7e10785f22e431af487429a370745973dff12d1ba6c99d1ad8dce79a9faa5f050a52165
Interface: WarpEndpoint { v4: **********, v6: 2606:4700:110:8bb0:2b1:9371:a882:ec04 }
Endpoints: [WarpSocketAddrPair { v4: *************:443, v6: [2606:4700:103::2]:443 }, WarpSocketAddrPair { v4: *************:500, v6: [2606:4700:103::2]:500 }, WarpSocketAddrPair { v4: *************:1701, v6: [2606:4700:103::2]:1701 }, WarpSocketAddrPair { v4: *************:4500, v6: [2606:4700:103::2]:4500 }, WarpSocketAddrPair { v4: *************:4443, v6: [2606:4700:103::2]:4443 }, WarpSocketAddrPair { v4: *************:8443, v6: [2606:4700:103::2]:8443 }, WarpSocketAddrPair { v4: *************:8095, v6: [2606:4700:103::2]:8095 }]
Subnet CIDRS: None

Account: Free { id: AccountId(38d2eb15-31ea-470f-92a9-3c26b5351c42), license: "94e5DJS8-Wqh617X9-78cjuC59" }

2025-07-28T09:15:42.299Z DEBUG run: warp_settings::manager: LayerManager update: UserOverrides(<mutator>)
2025-07-28T09:15:42.299Z DEBUG run: warp_settings::manager: UserOverrides updated: UserOverrides { version: Some(Current), always_on: Some(true), operation_mode: None, disable_for_wifi: None, disable_for_ethernet: None, disable_for_networks: None, families: None, dns_log_until: FutureSystemTime(None), gateway_id: None, onboarding: None, organization: None, split_config: None, fallback_domains: None, proxy_port: None, disable_connectivity_checks: None, override_api_endpoint: None, override_doh_endpoint: None, override_warp_endpoint: None, override_tunnel_mtu: None, qlog_log_until: FutureSystemTime(None), masque_settings: None, compliance_environment: None }
2025-07-28T09:15:42.329Z DEBUG run: warp_settings::manager: LayerManager update: NetworkPolicy(Some(NetworkPolicySettings { onboarding: None, operation_mode: None, disable_auto_fallback: None, fallback_domains: None, proxy_port: None, split_config: None, gateway_id: None, support_url: None, allow_mode_switch: None, switch_locked: None, auto_connect: None, captive_portal: None, organization: None, allow_updates: None, allowed_to_leave: None, profile_id: None, lan_allow_settings: Some(LanAllowSettings { minutes: None, subnet_size: SubnetSize(24) }), warp_tunnel_protocol: Some(Masque), masque_settings: None, register_interface_ip_with_dns: None, anycast_split_ips: Some(AnycastSplitIps { always_exclude: [*************, 2606:4700:102::3], always_include: [*************, 2606:4700:102::4] }), firewall_scope: None, sccm_vpn_boundary_support: None, enable_post_quantum: None }))
2025-07-28T09:15:42.329Z DEBUG warp_api_client::client::ops: Sending API request GET api.cloudflareclient.com./v0/client_config
2025-07-28T09:15:42.329Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "device-state.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "alternate-network.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 0 }, CounterPayload { name: "root-ca.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "watchdog.updates_recv", labels: {}, count: 0 }, CounterPayload { name: "main-loop-compat.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "remote-dex-command.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "emergency-disconnect.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "emergency-disconnect.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "connectivity.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "alternate-network.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "connectivity.updates_recv", labels: {}, count: 0 }, CounterPayload { name: "watchdog.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "message_bus.bus_driver_request_timeouts", labels: {}, count: 0 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "remote-dex-command.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "alternate-network.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "emergency-disconnect.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "connectivity.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "remote-dex-command.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "statistics.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "connectivity.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "registration-verifier.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "device-state.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "remote-dex-command.updates_recv", labels: {}, count: 0 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "statistics.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "main-loop-compat.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "statistics.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "root-ca.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "watchdog.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 0 }, CounterPayload { name: "registration-verifier.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "root-ca.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "emergency-disconnect.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "root-ca.requests_recv", labels: {}, count: 0 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.002967987, count: 1 }, SummaryPayload { name: "connectivity.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "alternate-network.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "device-state.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "root-ca.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "statistics.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "emergency-disconnect.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "watchdog.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "remote-dex-command.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "main-loop-compat.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "registration-verifier.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 0.00132924 }, GaugesPayload { name: "message_bus.bus_driver_current_actors", labels: {}, value: 10.0 }] } context="registration change"}: actor_statistics::handler: Starting to upload stats
2025-07-28T09:15:42.329Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "device-state.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "alternate-network.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 0 }, CounterPayload { name: "root-ca.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "watchdog.updates_recv", labels: {}, count: 0 }, CounterPayload { name: "main-loop-compat.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "remote-dex-command.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "emergency-disconnect.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "emergency-disconnect.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "connectivity.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "alternate-network.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "connectivity.updates_recv", labels: {}, count: 0 }, CounterPayload { name: "watchdog.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "message_bus.bus_driver_request_timeouts", labels: {}, count: 0 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "remote-dex-command.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "alternate-network.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "emergency-disconnect.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "connectivity.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "remote-dex-command.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "statistics.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "connectivity.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "registration-verifier.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "device-state.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "remote-dex-command.updates_recv", labels: {}, count: 0 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "statistics.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "main-loop-compat.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "statistics.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "root-ca.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "watchdog.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 0 }, CounterPayload { name: "registration-verifier.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "root-ca.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "emergency-disconnect.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "root-ca.requests_recv", labels: {}, count: 0 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.002967987, count: 1 }, SummaryPayload { name: "connectivity.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "alternate-network.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "device-state.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "root-ca.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "statistics.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "emergency-disconnect.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "watchdog.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "remote-dex-command.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "main-loop-compat.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "registration-verifier.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 0.00132924 }, GaugesPayload { name: "message_bus.bus_driver_current_actors", labels: {}, value: 10.0 }] } context="registration change"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-07-28T09:15:42.329Z  INFO actor_statistics::handler: Switched to new registration for stats. All stats have been reset. new_registration_id=None
2025-07-28T09:15:42.372Z DEBUG run: warp_settings::manager: LayerManager update: NetworkDefaults(NetworkDefaults { split_config: Some(Exclude { ips: [(10.0.0.0/8, None), (**********/10, None), (***********/16, None), (**********/12, None), (*********/24, None), (***********/16, None), (*********/24, None), (240.0.0.0/4, None), (***************/32, None), (***************/32, None), (fe80::/10, None), (fd00::/8, None), (ff01::/16, None), (ff02::/16, None), (ff03::/16, None), (ff04::/16, None), (ff05::/16, None), (fc00::/7, None), (**********/16, None), (**********/16, None), (***********/22, None), (************/18, None), (***********/23, None), (2620:149:a44::/48, None), (2403:300:a42::/48, None), (2403:300:a51::/48, None), (2a01:b740:a42::/48, None)], hosts: [] }) })
2025-07-28T09:15:42.372Z DEBUG main_loop: warp::warp_service: Checking for registration vs settings account mismatch reg_is_teams=false settings_is_teams=false reg_org=None settings_org=None
2025-07-28T09:15:42.372Z DEBUG firewall::change: Firewall allow API ips api_ips=[************, *************, 2606:4700::6810:c052, 2606:4700::6810:1854]
2025-07-28T09:15:42.372Z DEBUG firewall::change: Changing catch all policy Deny->Deny
2025-07-28T09:15:42.372Z DEBUG firewall::change: Firewall allow managed network endpoints managed_network_endpoints=[]
2025-07-28T09:15:42.372Z DEBUG firewall::change: Firewall allow anycast ips: [*************, 2606:4700:102::3]
2025-07-28T09:15:42.372Z  INFO firewall::engine: Firewall starting
2025-07-28T09:15:42.377Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T09:15:42.378Z  INFO main_loop: warp::warp_service: New User Settings
2025-07-28T09:15:42.378Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC update: SettingsUpdated
2025-07-28T09:15:42.378Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseUpdate: SettingsUpdated
2025-07-28T09:15:42.378Z DEBUG main_loop: warp::warp_service: update_settings: no restart required
2025-07-28T09:15:42.378Z DEBUG main_loop: warp::warp::controller: Stopping WarpConnection state=Disconnected
2025-07-28T09:15:42.378Z DEBUG firewall::change: Firewall allow API ips api_ips=[************, *************, 2606:4700::6810:c052, 2606:4700::6810:1854]
2025-07-28T09:15:42.378Z DEBUG firewall::change: Changing catch all policy Deny->Deny
2025-07-28T09:15:42.378Z DEBUG firewall::change: Firewall allow managed network endpoints managed_network_endpoints=[]
2025-07-28T09:15:42.378Z DEBUG firewall::change: Firewall allow anycast ips: [*************, 2606:4700:102::3]
2025-07-28T09:15:42.425Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T09:15:42.425Z DEBUG main_loop: warp::warp_service: Determining disconnected reason from connectivity state net_info=IPv4: [eth0; **********; Ethernet; Gateway: Some(**********)], DNS servers:,  127.0.0.11:53
 power_state=None disconnect_reason=None
2025-07-28T09:15:42.425Z  WARN main_loop: warp::warp_service: Disconnecting, but reason is unknown
2025-07-28T09:15:42.425Z DEBUG main_loop: warp_api_client::executor: Spawning API request for GetRegistrationConfig request_id=1bcb4580-cdaa-4efd-a2f7-63ce96fbc0be
2025-07-28T09:15:42.426Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 2 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "root-ca.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "statistics.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "emergency-disconnect.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "registration-verifier.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 4.4956e-5, count: 1 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 51.0, count: 2 }], gauges: [] } context="registration change"}: actor_statistics::handler: Starting to upload stats
2025-07-28T09:15:42.426Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 2 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "root-ca.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "statistics.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "emergency-disconnect.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "registration-verifier.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 4.4956e-5, count: 1 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 51.0, count: 2 }], gauges: [] } context="registration change"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-07-28T09:15:42.426Z  INFO actor_statistics::handler: Switched to new registration for stats. All stats have been reset. new_registration_id=None
2025-07-28T09:15:42.426Z DEBUG warp_api_client::apis::registration: Getting registration registration_id=Client(ConsumerRegistrationId(2d6c00fe-5a6c-46fb-a62a-b9aa5ed62ace))
2025-07-28T09:15:42.426Z DEBUG warp_api_client::client: Sending API request GET https://api.cloudflareclient.com.//v0/reg/2d6c00fe-5a6c-46fb-a62a-b9aa5ed62ace
2025-07-28T09:15:42.427Z DEBUG main_loop: warp::warp_service: Determining disconnected reason from connectivity state net_info=IPv4: [eth0; **********; Ethernet; Gateway: Some(**********)], DNS servers:,  127.0.0.11:53
 power_state=None disconnect_reason=None
2025-07-28T09:15:42.427Z  WARN main_loop: warp::warp_service: Disconnecting, but reason is unknown
2025-07-28T09:15:42.427Z  INFO warp::warp_service: Spawning registration changed IPC loop
2025-07-28T09:15:42.427Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="main_loop_compat_rx"
2025-07-28T09:15:42.429Z DEBUG main_loop:handle_command: warp::warp::controller: Stopping WarpConnection state=Disconnected
2025-07-28T09:15:42.429Z DEBUG firewall::change: Firewall allow API ips api_ips=[************, *************, 2606:4700::6810:c052, 2606:4700::6810:1854]
2025-07-28T09:15:42.429Z DEBUG firewall::change: Changing catch all policy Deny->Deny
2025-07-28T09:15:42.429Z DEBUG firewall::change: Firewall allow managed network endpoints managed_network_endpoints=[]
2025-07-28T09:15:42.429Z DEBUG firewall::change: Firewall allow anycast ips: [*************, 2606:4700:102::3]
2025-07-28T09:15:42.434Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T09:15:42.435Z  INFO main_loop:handle_command: warp::warp_service: captive_portal_fw_until: Indefinitely
2025-07-28T09:15:42.435Z DEBUG main_loop:handle_command: warp::warp::proxying_dns: Using auto fallback: true
2025-07-28T09:15:42.435Z DEBUG main_loop:handle_command: warp::warp::controller: Setting WarpConnection current_state=Disconnected
2025-07-28T09:15:42.435Z DEBUG main_loop:handle_command: warp::warp::controller: Stopping WarpConnection state=Disconnected
2025-07-28T09:15:42.436Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="main_loop_compat_rx" elapsed=8.45045ms
2025-07-28T09:15:42.436Z DEBUG main_loop: warp::warp::warp_connection: Current Network: IPv4: [eth0; **********; Ethernet; Gateway: Some(**********)], DNS servers:,  127.0.0.11:53

2025-07-28T09:15:42.436Z  INFO main_loop: warp::warp::warp_connection: Starting Warp Connection operation_mode=Warp dns_mode=DNS Proxy tunnel_mode=Exclude-only Tunnel posture_only=disabled forward_proxy=disabled
2025-07-28T09:15:42.436Z  INFO main_loop: warp::warp::tunnel: Initiate WARP connection protocol=Masque self.masque_settings=MasqueProtocolSettings { http_version: H3WithH2Fallback }
2025-07-28T09:15:42.436Z DEBUG firewall::change: Firewall allow tunnel UDP ipv4=[*************] ipv6=[2606:4700:103::2]
2025-07-28T09:15:42.436Z  INFO main_loop: warp::warp_service: WARP status: Connecting(CheckingNetwork)
2025-07-28T09:15:42.436Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Connecting(CheckingNetwork)
2025-07-28T09:15:42.436Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Connecting(CheckingNetwork)
2025-07-28T09:15:42.436Z DEBUG main_loop: warp::warp_service: Checking for registration vs settings account mismatch reg_is_teams=false settings_is_teams=false reg_org=None settings_org=None
2025-07-28T09:15:42.437Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T09:15:42.437Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=591.496µs
2025-07-28T09:15:42.437Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="settings_changed"
2025-07-28T09:15:42.489Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T09:15:42.489Z DEBUG firewall::change: Firewall allow API ips api_ips=[************, *************, 2606:4700::6810:c052, 2606:4700::6810:1854]
2025-07-28T09:15:42.489Z DEBUG firewall::change: Changing catch all policy Deny->Deny
2025-07-28T09:15:42.489Z DEBUG firewall::change: Firewall allow managed network endpoints managed_network_endpoints=[]
2025-07-28T09:15:42.489Z DEBUG firewall::change: Firewall allow anycast ips: [*************, 2606:4700:102::3]
2025-07-28T09:15:42.494Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T09:15:42.495Z  INFO main_loop: warp::warp_service: User Settings Changed: {}
2025-07-28T09:15:42.495Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC update: SettingsUpdated
2025-07-28T09:15:42.495Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseUpdate: SettingsUpdated
2025-07-28T09:15:42.495Z DEBUG main_loop: warp::warp_service: update_settings: no restart required
2025-07-28T09:15:42.495Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="settings_changed" elapsed=58.794234ms
2025-07-28T09:15:42.495Z DEBUG firewall::change: Firewall allow tunnel TCP addresses=[*************:443, *************:500, *************:1701, *************:4443, *************:4500, *************:8095, *************:8443, [2606:4700:103::2]:443, [2606:4700:103::2]:500, [2606:4700:103::2]:1701, [2606:4700:103::2]:4443, [2606:4700:103::2]:4500, [2606:4700:103::2]:8095, [2606:4700:103::2]:8443]
2025-07-28T09:15:42.533Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T09:15:42.534Z DEBUG firewall::change: Firewall allow captive portal detection
2025-07-28T09:15:42.546Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T09:15:42.546Z DEBUG main_loop:connect_with_fallback{primary="masque" fallback="H2"}:happy_eyeballs_with_retry{protocol="masque"}: warp_edge::happy_eyeballs: Attempting happy eyeballs with retries retries=0 retry_delay=0ns stage=FirstRun
2025-07-28T09:15:42.546Z DEBUG captive_portal: Running captive portal detections [legacy,os-detected,http-redirect[cloudflareportal.com],no-dns-redirect[cloudflareportal.com],expected-content,third-party]
2025-07-28T09:15:42.546Z DEBUG main_loop:connect_with_fallback{primary="masque" fallback="H2"}:happy_eyeballs_with_retry{protocol="masque"}: warp_edge::happy_eyeballs: Attempting Happy Eyeballs to *************:443 / [2606:4700:103::2]:443
2025-07-28T09:15:42.547Z  INFO captive_portal::detections::http_redirect: Checking for captive portal with HTTP-Redirect detection host="connectivitycheck.gstatic.com" ips=None interface=None bind_addr=None
2025-07-28T09:15:42.547Z DEBUG main_loop:connect_with_fallback{primary="masque" fallback="H2"}:happy_eyeballs_with_retry{protocol="masque"}: warp_edge::happy_eyeballs: Start racer **********:54237 ---> *************:443
2025-07-28T09:15:42.547Z  INFO run{detection_type=legacy}: captive_portal::detections::legacy: Checking for captive portal with legacy detection urls=["cloudflareportal.com", "cloudflareok.com", "cloudflarecp.com"]
2025-07-28T09:15:42.547Z  INFO run{detection_type=no-dns-redirect[cloudflareportal.com]}: captive_portal::detections::http_redirect: Checking for captive portal with HTTP-Redirect detection host="cloudflareportal.com" ips=Some([**********, **************, **********, 2606:4700:7::1, 2606:4700::1]) interface=None bind_addr=None
2025-07-28T09:15:42.547Z  INFO captive_portal::detections::http_redirect: Checking for captive portal with HTTP-Redirect detection host="www.msftconnecttest.com" ips=None interface=None bind_addr=None
2025-07-28T09:15:42.547Z  INFO captive_portal::detections::http_redirect: Checking for captive portal with HTTP-Redirect detection host="captive.apple.com" ips=None interface=None bind_addr=None
2025-07-28T09:15:42.547Z DEBUG run{detection_type=os-detected}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T09:15:42.548Z  INFO run{detection_type=http-redirect[cloudflareportal.com]}: captive_portal::detections::http_redirect: Checking for captive portal with HTTP-Redirect detection host="cloudflareportal.com" ips=None interface=None bind_addr=None
2025-07-28T09:15:42.550Z DEBUG main_loop:connect_with_fallback{primary="masque" fallback="H2"}:happy_eyeballs_with_retry{protocol="masque"}: warp_edge::h3_tun: Creating QUIC Client Connection Parmaeters qlog_setting=Disabled idle_timeout=IdleTimeout { idle_duration: IdleDuration(5s), retries: 0 }
2025-07-28T09:15:42.551Z DEBUG main_loop:connect_with_fallback{primary="masque" fallback="H2"}:happy_eyeballs_with_retry{protocol="masque"}: warp_edge::h3_tun: Sending handshake initiation to *************:443
2025-07-28T09:15:42.551Z  INFO slog: created unestablished quiche::Connection slog.target="tokio_quiche::quic" slog.module_path="tokio_quiche::quic" slog.file="/home/<USER>/.cargo/git/checkouts/quiche-4103ffa15137cf39/bfd737b/tokio-quiche/src/quic/mod.rs" slog.line=235 slog.column=5 slog.kv="provided_dcid=None,scid=f9762ad6da1e4d210b479bd63a9bd787dd881053"
2025-07-28T09:15:42.551Z DEBUG main_loop:connect_with_fallback{primary="masque" fallback="H2"}:happy_eyeballs_with_retry{protocol="masque"}: warp_edge::happy_eyeballs: Happy Eyeballs check failed v6=[2606:4700:103::2]:443 err=Connect(Os { code: 101, kind: NetworkUnreachable, message: "Network is unreachable" })
2025-07-28T09:15:42.551Z  INFO main_loop: warp::warp_service: WARP status: Connecting(PerformingHappyEyeballs((*************:443, [2606:4700:103::2]:443)))
2025-07-28T09:15:42.551Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Connecting(PerformingHappyEyeballs((*************:443, [2606:4700:103::2]:443)))
2025-07-28T09:15:42.551Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Connecting(PerformingHappyEyeballs((*************:443, [2606:4700:103::2]:443)))
2025-07-28T09:15:42.552Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T09:15:42.552Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=215.721µs
2025-07-28T09:15:42.552Z DEBUG slog: sending client Initials to peer slog.target="tokio_quiche::quic::router::connector" slog.module_path="tokio_quiche::quic::router::connector" slog.file="/home/<USER>/.cargo/git/checkouts/quiche-4103ffa15137cf39/bfd737b/tokio-quiche/src/quic/router/connector.rs" slog.line=266 slog.column=5 slog.kv="scid=f9762ad6da1e4d210b479bd63a9bd787dd881053"
2025-07-28T09:15:42.556Z DEBUG captive_portal::detections::http_redirect: 204 response for HTTP test. No captive portal detected
2025-07-28T09:15:42.556Z  INFO run{detection_type=legacy}: captive_portal::detections::legacy: Successfully resolved DNS queries
2025-07-28T09:15:42.558Z DEBUG slog: sending client Initials to peer slog.target="tokio_quiche::quic::router::connector" slog.module_path="tokio_quiche::quic::router::connector" slog.file="/home/<USER>/.cargo/git/checkouts/quiche-4103ffa15137cf39/bfd737b/tokio-quiche/src/quic/router/connector.rs" slog.line=266 slog.column=5 slog.kv="scid=f9762ad6da1e4d210b479bd63a9bd787dd881053"
2025-07-28T09:15:42.558Z DEBUG captive_portal::detections::http_redirect: 200 response for HTTP test. No captive portal detected
2025-07-28T09:15:42.560Z DEBUG captive_portal::detections::http_redirect: 200 response for HTTP test. No captive portal detected
2025-07-28T09:15:42.560Z DEBUG run{detection_type=third-party}: captive_portal::detections::third_party: Third party detections complete detections=[("connectivitycheck.gstatic.com", Ok(NoCaptivePortalDetected)), ("www.msftconnecttest.com", Ok(NoCaptivePortalDetected)), ("captive.apple.com", Ok(NoCaptivePortalDetected))]
2025-07-28T09:15:42.560Z DEBUG run{detection_type=third-party}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T09:15:42.564Z DEBUG slog: sending client Initials to peer slog.target="tokio_quiche::quic::router::connector" slog.module_path="tokio_quiche::quic::router::connector" slog.file="/home/<USER>/.cargo/git/checkouts/quiche-4103ffa15137cf39/bfd737b/tokio-quiche/src/quic/router/connector.rs" slog.line=266 slog.column=5 slog.kv="scid=f9762ad6da1e4d210b479bd63a9bd787dd881053"
2025-07-28T09:15:42.568Z DEBUG slog: QUIC connection established slog.target="tokio_quiche::quic::router::connector" slog.module_path="tokio_quiche::quic::router::connector" slog.file="/home/<USER>/.cargo/git/checkouts/quiche-4103ffa15137cf39/bfd737b/tokio-quiche/src/quic/router/connector.rs" slog.line=176 slog.column=13 slog.kv="scid=f9762ad6da1e4d210b479bd63a9bd787dd881053"
2025-07-28T09:15:42.569Z DEBUG main_loop:connect_with_fallback{primary="masque" fallback="H2"}:happy_eyeballs_with_retry{protocol="masque"}: warp_edge::h3_tun: Established QUIC connection with *************:443 scid=f9762ad6da1e4d210b479bd63a9bd787dd881053
2025-07-28T09:15:42.569Z DEBUG main_loop:connect_with_fallback{primary="masque" fallback="H2"}:happy_eyeballs_with_retry{protocol="masque"}: warp_edge::h3_tun: Establishing MASQUE tunnel on existing QUIC connection scid=f9762ad6da1e4d210b479bd63a9bd787dd881053 endpoint=*************:443
2025-07-28T09:15:42.569Z  INFO main_loop:connect_with_fallback{primary="masque" fallback="H2"}:happy_eyeballs_with_retry{protocol="masque"}:proxy_task{scid=f9762ad6da1e4d210b479bd63a9bd787dd881053}: warp_edge::h3_tun: Starting MASQUE proxy task idle_timeout=IdleTimeout { idle_duration: IdleDuration(5s), retries: 0 } keepalive_interval=4s
2025-07-28T09:15:42.569Z  INFO slog: creating new flow for MASQUE request slog.target="tokio_quiche::http3::driver::client" slog.module_path="tokio_quiche::http3::driver::client" slog.file="/home/<USER>/.cargo/git/checkouts/quiche-4103ffa15137cf39/bfd737b/tokio-quiche/src/http3/driver/client.rs" slog.line=168 slog.column=13 slog.kv="flow_id=0,stream_id=0"
2025-07-28T09:15:42.569Z  INFO main_loop: warp::warp_service: WARP status: Connecting(EstablishingConnection(*************:443))
2025-07-28T09:15:42.569Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Connecting(EstablishingConnection(*************:443))
2025-07-28T09:15:42.569Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Connecting(EstablishingConnection(*************:443))
2025-07-28T09:15:42.570Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T09:15:42.570Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=195.371µs
2025-07-28T09:15:42.571Z DEBUG run{detection_type=no-dns-redirect[cloudflareportal.com]}: captive_portal::detections::http_redirect: 204 response for HTTP test. No captive portal detected
2025-07-28T09:15:42.571Z DEBUG run{detection_type=no-dns-redirect[cloudflareportal.com]}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T09:15:42.578Z DEBUG run{detection_type=expected-content}: captive_portal::detections::expected_content: 200 response and correct content for HTTP Content test. No captive portal detected
2025-07-28T09:15:42.578Z DEBUG run{detection_type=expected-content}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T09:15:42.586Z DEBUG run{detection_type=http-redirect[cloudflareportal.com]}: captive_portal::detections::http_redirect: 204 response for HTTP test. No captive portal detected
2025-07-28T09:15:42.586Z DEBUG run{detection_type=http-redirect[cloudflareportal.com]}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T09:15:42.593Z  INFO run{detection_type=legacy}: captive_portal::detections::legacy: Successfully retrieved HTTPs page
2025-07-28T09:15:42.594Z DEBUG run{detection_type=legacy}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T09:15:42.594Z DEBUG captive_portal: Captive portal detection completed results=[("os-detected", Ok(NoCaptivePortalDetected)), ("third-party", Ok(NoCaptivePortalDetected)), ("no-dns-redirect[cloudflareportal.com]", Ok(NoCaptivePortalDetected)), ("expected-content", Ok(NoCaptivePortalDetected)), ("http-redirect[cloudflareportal.com]", Ok(NoCaptivePortalDetected)), ("legacy", Ok(NoCaptivePortalDetected))]
2025-07-28T09:15:42.594Z DEBUG firewall::change: Firewall disallow captive portal detection
2025-07-28T09:15:42.606Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T09:15:42.606Z DEBUG firewall::captive_portal: Firewall captive portal detection expired
2025-07-28T09:15:42.620Z DEBUG warp_api_endpoints::payloads: Warp endpoint override ports ports=[443, 500, 1701, 4500, 4443, 8443, 8095]
2025-07-28T09:15:42.621Z DEBUG main_loop: warp_api_client::executor: Returning API response for GetRegistrationConfig request_id=1bcb4580-cdaa-4efd-a2f7-63ce96fbc0be
2025-07-28T09:15:42.621Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="api_responses"
2025-07-28T09:15:42.621Z DEBUG run: warp_settings::manager: LayerManager update: NetworkPolicy(Some(NetworkPolicySettings { onboarding: None, operation_mode: None, disable_auto_fallback: None, fallback_domains: None, proxy_port: None, split_config: None, gateway_id: None, support_url: None, allow_mode_switch: None, switch_locked: None, auto_connect: None, captive_portal: None, organization: None, allow_updates: None, allowed_to_leave: None, profile_id: None, lan_allow_settings: Some(LanAllowSettings { minutes: None, subnet_size: SubnetSize(24) }), warp_tunnel_protocol: Some(Masque), masque_settings: None, register_interface_ip_with_dns: None, anycast_split_ips: Some(AnycastSplitIps { always_exclude: [*************, 2606:4700:102::3], always_include: [*************, 2606:4700:102::4] }), firewall_scope: None, sccm_vpn_boundary_support: None, enable_post_quantum: None }))
2025-07-28T09:15:42.621Z DEBUG main_loop: warp::warp_service::api_handlers: Registration updated current_registration=RefreshInProgress(reg=2d6c00fe-5a6c-46fb-a62a-b9aa5ed62ace) disposition=CheckAndUpdate
2025-07-28T09:15:42.621Z DEBUG main_loop: warp::warp_service::api_handlers: Registration is up to date, updating refresh time
2025-07-28T09:15:42.621Z DEBUG main_loop: warp::warp_service::api_handlers: Registration refresh time updated 0.00 hours old, refresh at 2025-07-29 9:15:42.620967782 +00:00:00
Registration: 2d6c00fe-5a6c-46fb-a62a-b9aa5ed62ace
Device Identifiers: System User
Auth Method: Consumer
Public key: 3059301306072a8648ce3d020106082a8648ce3d03010703420004113924ef48373289b7e26ff95857ba8b4db98b523b27141ed843834bd7e10785f22e431af487429a370745973dff12d1ba6c99d1ad8dce79a9faa5f050a52165
Interface: WarpEndpoint { v4: **********, v6: 2606:4700:110:8bb0:2b1:9371:a882:ec04 }
Endpoints: [WarpSocketAddrPair { v4: *************:443, v6: [2606:4700:103::2]:443 }, WarpSocketAddrPair { v4: *************:500, v6: [2606:4700:103::2]:500 }, WarpSocketAddrPair { v4: *************:1701, v6: [2606:4700:103::2]:1701 }, WarpSocketAddrPair { v4: *************:4500, v6: [2606:4700:103::2]:4500 }, WarpSocketAddrPair { v4: *************:4443, v6: [2606:4700:103::2]:4443 }, WarpSocketAddrPair { v4: *************:8443, v6: [2606:4700:103::2]:8443 }, WarpSocketAddrPair { v4: *************:8095, v6: [2606:4700:103::2]:8095 }]
Subnet CIDRS: None

Account: Free { id: AccountId(38d2eb15-31ea-470f-92a9-3c26b5351c42), license: "94e5DJS8-Wqh617X9-78cjuC59" }

2025-07-28T09:15:42.622Z  INFO main_loop: warp_storage::registration::registration_storage: Saving registration to active cache config_name=None username="WARPSecret"
2025-07-28T09:15:42.623Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="api_responses" elapsed=1.612047ms
2025-07-28T09:15:42.870Z DEBUG slog: got a response for stream_id=0, headers=[":status: 200", "cf-team: 28f1ea19940000d2c32891d400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:15:42.870Z DEBUG slog: upgraded H3Body: flow_id=0, mode=WithContextId slog.target="httx::body" slog.module_path="httx::body" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/body/mod.rs" slog.line=678 slog.column=5
2025-07-28T09:15:42.870Z DEBUG main_loop:connect_with_fallback{primary="masque" fallback="H2"}: warp::warp::tunnel::fallback: Connecting with primary connection protocol, reverting fallback firewall config
2025-07-28T09:15:42.870Z DEBUG firewall::change: Firewall allow tunnel TCP addresses=[]
2025-07-28T09:15:42.876Z DEBUG slog: got a response for stream_id=4, headers=[":status: 200", "cf-team: 28f1ea199b0000d2c32891e400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:15:42.877Z DEBUG main_loop:connect_with_fallback{primary="masque" fallback="H2"}:tunnel_stats_reporting_task{scid=f9762ad6da1e4d210b479bd63a9bd787dd881053}: warp_edge::h3_tun::tunnel_stats: Updating tunnel stats reporting interval from=120s to=15s
2025-07-28T09:15:42.880Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T09:15:42.880Z DEBUG main_loop:start_tunnel_processing{protocol="masque"}: warp::warp::tunnel: Connected to *************:443
2025-07-28T09:15:42.880Z  INFO main_loop:start_tunnel_processing{protocol="masque"}: warp::warp::tunnel: DNS resolver connection is being made inside of the tunnel
2025-07-28T09:15:42.881Z  INFO main_loop: warp::warp_service: WARP status: Connecting(InitializingTunnelInterface)
2025-07-28T09:15:42.881Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Connecting(InitializingTunnelInterface)
2025-07-28T09:15:42.881Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Connecting(InitializingTunnelInterface)
2025-07-28T09:15:42.882Z DEBUG firewall::change: Firewall allow interface CloudflareWARP
2025-07-28T09:15:42.882Z  INFO main_loop: warp::warp_service: WARP status: Connecting(ConfiguringInitialFirewall)
2025-07-28T09:15:42.882Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Connecting(ConfiguringInitialFirewall)
2025-07-28T09:15:42.882Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Connecting(ConfiguringInitialFirewall)
2025-07-28T09:15:42.882Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T09:15:42.882Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=593.449µs
2025-07-28T09:15:42.882Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T09:15:42.882Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=326.421µs
2025-07-28T09:15:42.913Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T09:15:42.932Z  INFO main_loop: warp::warp_service: WARP status: Connecting(SettingRoutes)
2025-07-28T09:15:42.932Z DEBUG firewall::change: Firewall allowing private IPs
2025-07-28T09:15:42.932Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T09:15:42.932Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Connecting(SettingRoutes)
2025-07-28T09:15:42.932Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Connecting(SettingRoutes)
2025-07-28T09:15:42.932Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=241.84µs
2025-07-28T09:15:42.932Z  INFO main_loop: warp::warp_service: WARP status: Connecting(ConfiguringFirewallRules)
2025-07-28T09:15:42.932Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T09:15:42.933Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Connecting(ConfiguringFirewallRules)
2025-07-28T09:15:42.933Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Connecting(ConfiguringFirewallRules)
2025-07-28T09:15:42.933Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=204.719µs
2025-07-28T09:15:42.938Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T09:15:42.938Z  INFO main_loop: warp::warp::proxying_dns: Initiate DNS connection
2025-07-28T09:15:42.939Z DEBUG main_loop: warp::warp::proxying_dns: Racing DNS IPs ips=[************, ************, 2606:4700:4700::1111, 2606:4700:4700::1001]
2025-07-28T09:15:42.939Z DEBUG tunnel_loop{protocol="masque" con_id="f9762ad6da1e4d210b479bd63a9bd787dd881053"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 1, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-07-28T09:15:42.946Z DEBUG main_loop: warp::warp::proxying_dns: DNS TCP connection to Gateway DoH resolver was successful raced_ip=************
2025-07-28T09:15:42.946Z DEBUG main_loop: dns_proxy::proxy: Creating DoH resolver config=ResolverConfig { domain: None, search: [], name_servers: NameServerConfigGroup([NameServerConfig { socket_addr: ************:443, protocol: Https, tls_dns_name: Some("cloudflare-dns.com"), trust_negative_responses: true, tls_config: None, bind_addr: None }, NameServerConfig { socket_addr: ************:443, protocol: Https, tls_dns_name: Some("cloudflare-dns.com"), trust_negative_responses: true, tls_config: None, bind_addr: None }, NameServerConfig { socket_addr: [2606:4700:4700::1111]:443, protocol: Https, tls_dns_name: Some("cloudflare-dns.com"), trust_negative_responses: true, tls_config: None, bind_addr: None }, NameServerConfig { socket_addr: [2606:4700:4700::1001]:443, protocol: Https, tls_dns_name: Some("cloudflare-dns.com"), trust_negative_responses: true, tls_config: None, bind_addr: None }], None) } options=ResolverOpts { ndots: 1, timeout: 4s, attempts: 0, rotate: false, check_names: true, edns0: true, validate: false, ip_strategy: Ipv4thenIpv6, cache_size: 32, use_hosts_file: true, positive_min_ttl: None, negative_min_ttl: None, positive_max_ttl: None, negative_max_ttl: Some(10s), num_concurrent_reqs: 1, preserve_intermediates: true, try_tcp_on_error: false, server_ordering_strategy: QueryStatistics, recursion_desired: true, authentic_data: false, shuffle_dns_servers: false }
2025-07-28T09:15:42.946Z DEBUG firewall::change: Firewall allow captive portal detection
2025-07-28T09:15:42.947Z  INFO main_loop: warp::warp_service: WARP status: Connecting(CheckingForRouteToDnsEndpoint)
2025-07-28T09:15:42.947Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Connecting(CheckingForRouteToDnsEndpoint)
2025-07-28T09:15:42.947Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Connecting(CheckingForRouteToDnsEndpoint)
2025-07-28T09:15:42.947Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T09:15:42.947Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=291.595µs
2025-07-28T09:15:42.973Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T09:15:42.974Z DEBUG captive_portal: Running captive portal detections [legacy,os-detected,http-redirect[cloudflareportal.com],no-dns-redirect[cloudflareportal.com],expected-content,third-party]
2025-07-28T09:15:42.974Z  INFO captive_portal::detections::http_redirect: Checking for captive portal with HTTP-Redirect detection host="connectivitycheck.gstatic.com" ips=None interface=Some("eth0") bind_addr=Some(**********)
2025-07-28T09:15:42.974Z DEBUG run{detection_type=os-detected}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T09:15:42.974Z  INFO captive_portal::detections::http_redirect: Checking for captive portal with HTTP-Redirect detection host="www.msftconnecttest.com" ips=None interface=Some("eth0") bind_addr=Some(**********)
2025-07-28T09:15:42.974Z  INFO captive_portal::detections::http_redirect: Checking for captive portal with HTTP-Redirect detection host="captive.apple.com" ips=None interface=Some("eth0") bind_addr=Some(**********)
2025-07-28T09:15:42.974Z  INFO run{detection_type=http-redirect[cloudflareportal.com]}: captive_portal::detections::http_redirect: Checking for captive portal with HTTP-Redirect detection host="cloudflareportal.com" ips=None interface=Some("eth0") bind_addr=Some(**********)
2025-07-28T09:15:42.975Z  INFO run{detection_type=no-dns-redirect[cloudflareportal.com]}: captive_portal::detections::http_redirect: Checking for captive portal with HTTP-Redirect detection host="cloudflareportal.com" ips=Some([**********, **************, **********, 2606:4700:7::1, 2606:4700::1]) interface=Some("eth0") bind_addr=Some(**********)
2025-07-28T09:15:42.975Z  INFO run{detection_type=legacy}: captive_portal::detections::legacy: Checking for captive portal with legacy detection urls=["cloudflareportal.com", "cloudflareok.com", "cloudflarecp.com"]
2025-07-28T09:15:42.976Z  INFO run{detection_type=legacy}: captive_portal::detections::legacy: Successfully resolved DNS queries
2025-07-28T09:15:42.984Z DEBUG captive_portal::detections::http_redirect: 204 response for HTTP test. No captive portal detected
2025-07-28T09:15:42.985Z DEBUG captive_portal::detections::http_redirect: 200 response for HTTP test. No captive portal detected
2025-07-28T09:15:42.985Z DEBUG captive_portal::detections::http_redirect: 200 response for HTTP test. No captive portal detected
2025-07-28T09:15:42.985Z DEBUG run{detection_type=third-party}: captive_portal::detections::third_party: Third party detections complete detections=[("connectivitycheck.gstatic.com", Ok(NoCaptivePortalDetected)), ("www.msftconnecttest.com", Ok(NoCaptivePortalDetected)), ("captive.apple.com", Ok(NoCaptivePortalDetected))]
2025-07-28T09:15:42.986Z DEBUG run{detection_type=third-party}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T09:15:42.996Z DEBUG run{detection_type=no-dns-redirect[cloudflareportal.com]}: captive_portal::detections::http_redirect: 204 response for HTTP test. No captive portal detected
2025-07-28T09:15:42.996Z DEBUG run{detection_type=no-dns-redirect[cloudflareportal.com]}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T09:15:42.997Z DEBUG run{detection_type=http-redirect[cloudflareportal.com]}: captive_portal::detections::http_redirect: 204 response for HTTP test. No captive portal detected
2025-07-28T09:15:42.997Z DEBUG run{detection_type=http-redirect[cloudflareportal.com]}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T09:15:43.000Z DEBUG run{detection_type=expected-content}: captive_portal::detections::expected_content: 200 response and correct content for HTTP Content test. No captive portal detected
2025-07-28T09:15:43.000Z DEBUG run{detection_type=expected-content}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T09:15:43.000Z DEBUG main_loop: warp::warp::proxying_dns: Connectivity Check resolved query for connectivity.cloudflareclient.com resolved_ips=[**************, **************]
2025-07-28T09:15:43.001Z DEBUG main_loop: dns_proxy::resolver: Ending DoH connection stats=establish_time:2025-07-28 9:15:42.974069605 +00:00:00, ip:************, attempted_queries:1, successful_queries:1, certificate_errors:0, timeout_errors:0, no_connection_errors:0, proto_errors:0, io_errors:0, msg_errors:0
2025-07-28T09:15:43.001Z DEBUG main_loop: warp::warp::proxying_dns: Acquiring bind semaphore available_permits=1
2025-07-28T09:15:43.001Z DEBUG main_loop: warp::warp::proxying_dns: Binding UDP and TCP sockets dns_servers=[*********, *********]
2025-07-28T09:15:43.001Z DEBUG main_loop: warp::warp::proxying_dns: Successfully bound DNS listen sockets udp_sockets=2 tcp_sockets=2
2025-07-28T09:15:43.004Z DEBUG main_loop: warp::warp::proxying_dns: Read system DNS configuration initial_dns_config=(ResolverConfig { domain: None, search: [], name_servers: NameServerConfigGroup([NameServerConfig { socket_addr: 127.0.0.11:53, protocol: Udp, tls_dns_name: None, trust_negative_responses: false, tls_config: None, bind_addr: None }, NameServerConfig { socket_addr: 127.0.0.11:53, protocol: Tcp, tls_dns_name: None, trust_negative_responses: false, tls_config: None, bind_addr: None }], None) }, ResolverOpts { ndots: 0, timeout: 5s, attempts: 2, rotate: false, check_names: true, edns0: false, validate: false, ip_strategy: Ipv4thenIpv6, cache_size: 32, use_hosts_file: true, positive_min_ttl: None, negative_min_ttl: None, positive_max_ttl: None, negative_max_ttl: None, num_concurrent_reqs: 2, preserve_intermediates: true, try_tcp_on_error: false, server_ordering_strategy: QueryStatistics, recursion_desired: true, authentic_data: false, shuffle_dns_servers: false })
2025-07-28T09:15:43.004Z DEBUG main_loop: warp::warp::proxying_dns: Initializing DNS restore settings net.v4_ifaces=[eth0; **********; Ethernet; Gateway: Some(**********)] net.v6_ifaces=[]
2025-07-28T09:15:43.005Z DEBUG warp_dns: Starting DnsOverWarp task tun_address=**********:0
2025-07-28T09:15:43.005Z DEBUG main_loop: warp::warp::dns_recovery::unix: Applying DNS settings [*********, *********, ::ffff:*********, ::ffff:*********]
2025-07-28T09:15:43.005Z  INFO dns_proxy::proxy: Default fallbacks configured default_fallback_ips=[127.0.0.11:53] config=ResolverConfig { domain: None, search: [], name_servers: NameServerConfigGroup([NameServerConfig { socket_addr: 127.0.0.11:53, protocol: Udp, tls_dns_name: None, trust_negative_responses: true, tls_config: None, bind_addr: None }, NameServerConfig { socket_addr: 127.0.0.11:53, protocol: Tcp, tls_dns_name: None, trust_negative_responses: true, tls_config: None, bind_addr: None }], None) } sys_options=ResolverOpts { ndots: 0, timeout: 11s, attempts: 0, rotate: false, check_names: true, edns0: true, validate: false, ip_strategy: Ipv4thenIpv6, cache_size: 32, use_hosts_file: true, positive_min_ttl: None, negative_min_ttl: None, positive_max_ttl: None, negative_max_ttl: None, num_concurrent_reqs: 8, preserve_intermediates: true, try_tcp_on_error: false, server_ordering_strategy: QueryStatistics, recursion_desired: true, authentic_data: false, shuffle_dns_servers: false }
2025-07-28T09:15:43.006Z DEBUG main_loop: network_info::linux::dns_manager: detected system dns manager file_owner=Unknown os_configuration_owner=File
2025-07-28T09:15:43.013Z  INFO run{detection_type=legacy}: captive_portal::detections::legacy: Successfully retrieved HTTPs page
2025-07-28T09:15:43.013Z DEBUG run{detection_type=legacy}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T09:15:43.013Z DEBUG captive_portal: Captive portal detection completed results=[("os-detected", Ok(NoCaptivePortalDetected)), ("third-party", Ok(NoCaptivePortalDetected)), ("no-dns-redirect[cloudflareportal.com]", Ok(NoCaptivePortalDetected)), ("http-redirect[cloudflareportal.com]", Ok(NoCaptivePortalDetected)), ("expected-content", Ok(NoCaptivePortalDetected)), ("legacy", Ok(NoCaptivePortalDetected))]
2025-07-28T09:15:43.014Z DEBUG firewall::change: Firewall disallow captive portal detection
2025-07-28T09:15:43.021Z  INFO main_loop: warp::warp_service: WARP status: Connecting(PerformingConnectivityChecks)
2025-07-28T09:15:43.021Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Connecting(PerformingConnectivityChecks)
2025-07-28T09:15:43.021Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T09:15:43.021Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Connecting(PerformingConnectivityChecks)
2025-07-28T09:15:43.021Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=301.845µs
2025-07-28T09:15:43.023Z DEBUG connectivity_check: Resolved connectivity-check.warp-svc. to [*********, *********, ::ffff:*********, ::ffff:*********]
2025-07-28T09:15:43.023Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T09:15:43.023Z DEBUG firewall::captive_portal: Firewall captive portal detection expired
2025-07-28T09:15:43.059Z DEBUG do_trace_inner{host="engage.cloudflareclient.com" host_with_protocol="https://engage.cloudflareclient.com" timeout=4s sockets=[*************:443, [2606:4700:102::3]:443] con_check_meta=ConnectivityCheckMetadata { proxy_local_addr: None }}: connectivity_check: fl=472f454
h=engage.cloudflareclient.com
ip=***************
ts=1753694143.057
visit_scheme=https
uag=WARP for Linux
colo=FRA
sliver=none
http=http/1.1
loc=DE
tls=TLSv1.3
sni=plaintext
warp=off
gateway=off
rbi=off
kex=X25519

2025-07-28T09:15:43.101Z DEBUG do_trace_inner{host="connectivity.cloudflareclient.com" host_with_protocol="https://connectivity.cloudflareclient.com" timeout=4s sockets=[*************:443, [2606:4700:102::4]:443] con_check_meta=ConnectivityCheckMetadata { proxy_local_addr: None }}: connectivity_check: fl=71f1277
h=connectivity.cloudflareclient.com
ip=**************
ts=1753694143.051
visit_scheme=https
uag=WARP for Linux
colo=FRA
sliver=none
http=http/1.1
loc=DE
tls=TLSv1.3
sni=plaintext
warp=off
gateway=off
rbi=off
kex=X25519

2025-07-28T09:15:43.102Z DEBUG main_loop: warp::warp::connectivity_check: Trace status: Ok(TraceResult { metal_id: "71f1277", colo: "FRA", ip: ************** })
2025-07-28T09:15:43.102Z  INFO main_loop: warp::warp_service: WARP status: Connecting(ValidatingDnsConfiguration)
2025-07-28T09:15:43.102Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Connecting(ValidatingDnsConfiguration)
2025-07-28T09:15:43.102Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Connecting(ValidatingDnsConfiguration)
2025-07-28T09:15:43.102Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T09:15:43.103Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=404.761µs
2025-07-28T09:15:43.111Z DEBUG main_loop: connectivity_check: Resolved connectivity.cloudflareclient.com to [2606:4700:7::a29f:8941, 2606:4700:7::a29f:8a41, **************, **************]
2025-07-28T09:15:43.111Z DEBUG main_loop: warp::warp::warp_connection: Connect finished
2025-07-28T09:15:43.112Z DEBUG main_loop: warp::warp::warp_connection: start_status=Ok(())
2025-07-28T09:15:43.112Z DEBUG actor_statistics::handler: Recording gauge into histogram context=RecordIntoHistogramInfo { gauge_key: KeyName("conn_attempts_current"), histogram_key: KeyName("conn_attempts_all"), component: "happy_eyeballs" }
2025-07-28T09:15:43.112Z DEBUG main_loop: warp::warp::controller: self.warp future resolved
2025-07-28T09:15:43.112Z  INFO main_loop: warp::warp_service: WARP status: Connected
2025-07-28T09:15:43.112Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="tunnel_taskset_errors_fut"
2025-07-28T09:15:43.112Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="tunnel_taskset_errors_fut" elapsed=318.947µs
2025-07-28T09:15:43.112Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T09:15:43.112Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Connected
2025-07-28T09:15:43.112Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Connected
2025-07-28T09:15:43.112Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=796.476µs
2025-07-28T09:15:44.175Z  WARN warp_net::ipc::unix: Failed to get path for client error="return code = -1, errno = 13, message = 'Permission denied'" pid=71
2025-07-28T09:15:44.175Z  WARN warp_net::ipc::unix: Failed to get path for client error="return code = -1, errno = 13, message = 'Permission denied'" pid=71
2025-07-28T09:15:44.175Z  INFO warp::warp_service::ipc_loop: IPC: new connection exec_context=Regular process_name="<Unknown>" pid=71
2025-07-28T09:15:44.175Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="handle_ipc_request"
2025-07-28T09:15:44.175Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc request: 26e5ef9f-eea4-4c04-88b3-b1c577b8aabe; GetAppSettings
2025-07-28T09:15:44.175Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="handle_ipc_request" elapsed=334.427µs
2025-07-28T09:15:44.176Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc request: 3b845926-745c-42b9-a0fc-14738e5c888a; GetRegistrationInfo
2025-07-28T09:15:44.176Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="handle_ipc_request"
2025-07-28T09:15:44.176Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc response: 3b845926-745c-42b9-a0fc-14738e5c888a; RegistrationInfo: Some(RegistrationInfo { id: Client(ConsumerRegistrationId(2d6c00fe-5a6c-46fb-a62a-b9aa5ed62ace)), device_id: PhysicalDeviceId(2d6c00fe-5a6c-46fb-a62a-b9aa5ed62ace), public_key: [48, 89, 48, 19, 6, 7, 42, 134, 72, 206, 61, 2, 1, 6, 8, 42, 134, 72, 206, 61, 3, 1, 7, 3, 66, 0, 4, 17, 57, 36, 239, 72, 55, 50, 137, 183, 226, 111, 249, 88, 87, 186, 139, 77, 185, 139, 82, 59, 39, 20, 30, 216, 67, 131, 75, 215, 225, 7, 133, 242, 46, 67, 26, 244, 135, 66, 154, 55, 7, 69, 151, 61, 255, 18, 209, 186, 108, 153, 209, 173, 141, 206, 121, 169, 250, 165, 240, 80, 165, 33, 101], managed: false, account: Free { id: AccountId(38d2eb15-31ea-470f-92a9-3c26b5351c42), license: "94e5DJS8-Wqh617X9-78cjuC59" }, alternate_networks: None })
2025-07-28T09:15:44.176Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="handle_ipc_request" elapsed=341.53µs
2025-07-28T09:15:44.176Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc request: efa33655-3fc4-4d83-9437-72a2b40df918; SetQlog(setting=Disabled)
2025-07-28T09:15:44.176Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="handle_ipc_request"
2025-07-28T09:15:44.177Z DEBUG run: warp_settings::manager: LayerManager update: UserOverrides(<mutator>)
2025-07-28T09:15:44.177Z DEBUG run: warp_settings::manager: UserOverrides updated: UserOverrides { version: Some(Current), always_on: Some(true), operation_mode: None, disable_for_wifi: None, disable_for_ethernet: None, disable_for_networks: None, families: None, dns_log_until: FutureSystemTime(None), gateway_id: None, onboarding: None, organization: None, split_config: None, fallback_domains: None, proxy_port: None, disable_connectivity_checks: None, override_api_endpoint: None, override_doh_endpoint: None, override_warp_endpoint: None, override_tunnel_mtu: None, qlog_log_until: FutureSystemTime(None), masque_settings: None, compliance_environment: None }
2025-07-28T09:15:44.177Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc response: efa33655-3fc4-4d83-9437-72a2b40df918; Success
2025-07-28T09:15:44.177Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="handle_ipc_request" elapsed=587.979µs
2025-07-28T09:15:44.177Z  INFO warp::warp_service::ipc_loop: IPC connection ended
2025-07-28T09:15:46.302Z DEBUG main_loop:handle_update{update=NetworkInfoChanged}: warp::warp_service::actor_compat: Handling network status change old_info=IPv4: [eth0; **********; Ethernet; Gateway: Some(**********)], DNS servers:,  127.0.0.11:53
 new_info=IPv4: [eth0; **********; Ethernet; Gateway: Some(**********)], DNS servers:,  *********:53,  *********:53

2025-07-28T09:15:46.302Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="main_loop_compat_rx"
2025-07-28T09:15:46.302Z DEBUG main_loop:handle_update{update=NetworkInfoChanged}: warp::warp::proxying_dns: Reapplying DNS settings when the list of DNS servers contains ourselves new_info.dns_servers=[*********:53, *********:53] ADVERTISED_DNS_TARGETS=[*********, *********, ::ffff:*********, ::ffff:*********]
2025-07-28T09:15:46.302Z  INFO main_loop:handle_update{update=NetworkInfoChanged}: warp::warp::proxying_dns: Reinforcing old DNS settings old_info.dns_servers=[127.0.0.11:53] new_info.dns_servers=[*********:53, *********:53]
2025-07-28T09:15:46.302Z DEBUG main_loop:handle_update{update=NetworkInfoChanged}: warp::warp::dns_recovery::unix: Reverting DNS settings old_dns=RestoreDNS { action: OverwriteResolv([35, 32, 71, 101, 110, 101, 114, 97, 116, 101, 100, 32, 98, 121, 32, 68, 111, 99, 107, 101, 114, 32, 69, 110, 103, 105, 110, 101, 46, 10, 35, 32, 84, 104, 105, 115, 32, 102, 105, 108, 101, 32, 99, 97, 110, 32, 98, 101, 32, 101, 100, 105, 116, 101, 100, 59, 32, 68, 111, 99, 107, 101, 114, 32, 69, 110, 103, 105, 110, 101, 32, 119, 105, 108, 108, 32, 110, 111, 116, 32, 109, 97, 107, 101, 32, 102, 117, 114, 116, 104, 101, 114, 32, 99, 104, 97, 110, 103, 101, 115, 32, 111, 110, 99, 101, 32, 105, 116, 10, 35, 32, 104, 97, 115, 32, 98, 101, 101, 110, 32, 109, 111, 100, 105, 102, 105, 101, 100, 46, 10, 10, 110, 97, 109, 101, 115, 101, 114, 118, 101, 114, 32, 49, 50, 55, 46, 48, 46, 48, 46, 49, 49, 10, 111, 112, 116, 105, 111, 110, 115, 32, 110, 100, 111, 116, 115, 58, 48, 10, 10, 35, 32, 66, 97, 115, 101, 100, 32, 111, 110, 32, 104, 111, 115, 116, 32, 102, 105, 108, 101, 58, 32, 39, 47, 101, 116, 99, 47, 114, 101, 115, 111, 108, 118, 46, 99, 111, 110, 102, 39, 32, 40, 105, 110, 116, 101, 114, 110, 97, 108, 32, 114, 101, 115, 111, 108, 118, 101, 114, 41, 10, 35, 32, 69, 120, 116, 83, 101, 114, 118, 101, 114, 115, 58, 32, 91, 104, 111, 115, 116, 40, 52, 54, 46, 51, 56, 46, 50, 50, 53, 46, 50, 51, 48, 41, 32, 104, 111, 115, 116, 40, 52, 54, 46, 51, 56, 46, 50, 53, 50, 46, 50, 51, 48, 41, 93, 10, 35, 32, 79, 118, 101, 114, 114, 105, 100, 101, 115, 58, 32, 91, 93, 10, 35, 32, 79, 112, 116, 105, 111, 110, 32, 110, 100, 111, 116, 115, 32, 102, 114, 111, 109, 58, 32, 105, 110, 116, 101, 114, 110, 97, 108, 10]), resolv_conf_owner: Unknown, os_config_owner: File, previous_nameservers: Some([127.0.0.11]), warp_nameservers: [*********, *********, ::ffff:*********, ::ffff:*********] }
2025-07-28T09:15:46.306Z  WARN main_loop:handle_update{update=NetworkInfoChanged}: network_info::linux::dns_manager: Failed to notify system DNS manager of change. DNS may be broken if systemd-resolved is used owner=Unknown os_config_owner=File err=Failed to restart systemd-resolved

Caused by:
    org.freedesktop.DBus.Error.ServiceUnknown: The name org.freedesktop.systemd1 was not provided by any .service files
2025-07-28T09:15:46.306Z DEBUG main_loop:handle_update{update=NetworkInfoChanged}: warp::warp::dns_recovery::unix: DNS settings reverted (<resolv.conf restored>)
2025-07-28T09:15:46.307Z DEBUG main_loop:handle_update{update=NetworkInfoChanged}: warp::warp::dns_recovery::unix: Applying DNS settings [*********, *********, ::ffff:*********, ::ffff:*********]
2025-07-28T09:15:46.308Z DEBUG main_loop:handle_update{update=NetworkInfoChanged}: network_info::linux::dns_manager: detected system dns manager file_owner=Unknown os_configuration_owner=File
2025-07-28T09:15:46.314Z DEBUG main_loop:handle_update{update=NetworkInfoChanged}: warp::warp::proxying_dns: Skip notifying DNS changes as servers was empty or only contained our own advertised IPs. servers=[*********:53, *********:53] ADVERTISED_DNS_TARGETS=[*********, *********, ::ffff:*********, ::ffff:*********]
2025-07-28T09:15:46.315Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="main_loop_compat_rx" elapsed=12.274787ms
2025-07-28T09:15:49.281Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:15:52.294Z  INFO actor_emergency_disconnect::handler: Polling API for latest emergency disconnect state
2025-07-28T09:15:54.432Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:15:57.296Z DEBUG actor_root_ca::handler: Starting root-ca actor loop
2025-07-28T09:15:57.297Z DEBUG handle_registration_changed: root_ca::store: Done installing certificate(s) total=0
2025-07-28T09:15:57.297Z DEBUG handle_registration_changed: root_ca::store: Done installing certificate(s) total=0
2025-07-28T09:15:57.883Z DEBUG slog: got a response for stream_id=8, headers=[":status: 200", "cf-team: 28f1ea54390000d2c328a7c400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:16:04.576Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:16:10.560Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:16:12.884Z DEBUG slog: got a response for stream_id=12, headers=[":status: 200", "cf-team: 28f1ea8ed10000d2c328b9d400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:16:27.883Z DEBUG slog: got a response for stream_id=16, headers=[":status: 200", "cf-team: 28f1eac96a0000d2c328cc7400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:16:33.088Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:16:39.744Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:16:42.883Z DEBUG slog: got a response for stream_id=20, headers=[":status: 200", "cf-team: 28f1eb04020000d2c328ea6400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:16:57.883Z DEBUG slog: got a response for stream_id=24, headers=[":status: 200", "cf-team: 28f1eb3e9a0000d2c328fac400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:17:01.760Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:17:07.648Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:17:12.883Z DEBUG slog: got a response for stream_id=28, headers=[":status: 200", "cf-team: 28f1eb79310000d2c32906f400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:17:27.883Z DEBUG slog: got a response for stream_id=32, headers=[":status: 200", "cf-team: 28f1ebb3ca0000d2c32924f400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:17:30.432Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:17:35.808Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:17:42.294Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 1199 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 36 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 203.0, count: 11 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.046151189, count: 1199 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 164.0, count: 152 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.993219924 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-07-28T09:17:42.294Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 1199 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 36 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 203.0, count: 11 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.046151189, count: 1199 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 164.0, count: 152 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.993219924 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-07-28T09:17:42.883Z DEBUG slog: got a response for stream_id=36, headers=[":status: 200", "cf-team: 28f1ebee620000d2c329385400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:17:42.940Z DEBUG tunnel_loop{protocol="masque" con_id="f9762ad6da1e4d210b479bd63a9bd787dd881053"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 6, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-07-28T09:17:57.883Z DEBUG slog: got a response for stream_id=40, headers=[":status: 200", "cf-team: 28f1ec28fa0000d2c32948a400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:17:59.104Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:18:06.272Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:18:12.884Z DEBUG slog: got a response for stream_id=44, headers=[":status: 200", "cf-team: 28f1ec63920000d2c329521400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:18:27.883Z DEBUG slog: got a response for stream_id=48, headers=[":status: 200", "cf-team: 28f1ec9e290000d2c329651400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:18:29.824Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:18:35.712Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:18:42.883Z DEBUG slog: got a response for stream_id=52, headers=[":status: 200", "cf-team: 28f1ecd8c20000d2c32977e400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:18:57.883Z DEBUG slog: got a response for stream_id=56, headers=[":status: 200", "cf-team: 28f1ed135a0000d2c32987a400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:18:58.496Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:19:03.616Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:19:12.883Z DEBUG slog: got a response for stream_id=60, headers=[":status: 200", "cf-team: 28f1ed4df20000d2c3299e1400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:19:25.124Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:19:27.883Z DEBUG slog: got a response for stream_id=64, headers=[":status: 200", "cf-team: 28f1ed888a0000d2c329ad3400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:19:31.776Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:19:42.294Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 61 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 2398 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 203.0, count: 11 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 247.0, count: 302 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.0924224220000001, count: 2398 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.00666685 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-07-28T09:19:42.294Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 61 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 2398 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 203.0, count: 11 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 247.0, count: 302 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.0924224220000001, count: 2398 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.00666685 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-07-28T09:19:42.883Z DEBUG slog: got a response for stream_id=68, headers=[":status: 200", "cf-team: 28f1edc3220000d2c329bf2400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:19:42.940Z DEBUG tunnel_loop{protocol="masque" con_id="f9762ad6da1e4d210b479bd63a9bd787dd881053"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 6, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-07-28T09:19:53.792Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:19:57.883Z DEBUG slog: got a response for stream_id=72, headers=[":status: 200", "cf-team: 28f1edfdba0000d2c329e01400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:19:59.680Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:20:12.883Z DEBUG slog: got a response for stream_id=76, headers=[":status: 200", "cf-team: 28f1ee38520000d2c329faf400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:20:20.416Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:20:26.048Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:20:27.883Z DEBUG slog: got a response for stream_id=80, headers=[":status: 200", "cf-team: 28f1ee72ea0000d2c32a0a5400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:20:42.883Z DEBUG slog: got a response for stream_id=84, headers=[":status: 200", "cf-team: 28f1eead820000d2c32a1e6400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:20:47.040Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:20:52.160Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:20:57.883Z DEBUG slog: got a response for stream_id=88, headers=[":status: 200", "cf-team: 28f1eee8190000d2c32a36e400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:21:12.883Z DEBUG slog: got a response for stream_id=92, headers=[":status: 200", "cf-team: 28f1ef22b20000d2c32a4d5400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:21:13.664Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:21:18.784Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:21:27.883Z DEBUG slog: got a response for stream_id=96, headers=[":status: 200", "cf-team: 28f1ef5d4a0000d2c32a5d9400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:21:40.288Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:21:42.294Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 89 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 3598 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.13912272099999984, count: 3598 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 367.0, count: 444 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 203.0, count: 11 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.999744265 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-07-28T09:21:42.294Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 89 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 3598 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.13912272099999984, count: 3598 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 367.0, count: 444 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 203.0, count: 11 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.999744265 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-07-28T09:21:42.883Z DEBUG slog: got a response for stream_id=100, headers=[":status: 200", "cf-team: 28f1ef97e20000d2c32a745400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:21:42.939Z DEBUG tunnel_loop{protocol="masque" con_id="f9762ad6da1e4d210b479bd63a9bd787dd881053"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 7, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-07-28T09:21:46.432Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:21:57.883Z DEBUG slog: got a response for stream_id=104, headers=[":status: 200", "cf-team: 28f1efd27a0000d2c32a899400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:22:06.912Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:22:12.883Z DEBUG slog: got a response for stream_id=108, headers=[":status: 200", "cf-team: 28f1f00d110000d2c32a97f400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:22:13.824Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:22:27.883Z DEBUG slog: got a response for stream_id=112, headers=[":status: 200", "cf-team: 28f1f047a90000d2c32aac7400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:22:35.584Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:22:41.472Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:22:42.883Z DEBUG slog: got a response for stream_id=116, headers=[":status: 200", "cf-team: 28f1f082420000d2c32abef400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:22:57.883Z DEBUG slog: got a response for stream_id=120, headers=[":status: 200", "cf-team: 28f1f0bcda0000d2c32ad3b400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:23:02.208Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:23:07.328Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:23:12.883Z DEBUG slog: got a response for stream_id=124, headers=[":status: 200", "cf-team: 28f1f0f7710000d2c32af6d400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:23:27.883Z DEBUG slog: got a response for stream_id=128, headers=[":status: 200", "cf-team: 28f1f1320a0000d2c32b12a400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:23:28.832Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:23:34.208Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:23:42.294Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 117 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 4798 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 443.0, count: 572 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.1859986299999994, count: 4798 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 203.0, count: 11 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.998920834 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-07-28T09:23:42.294Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 117 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 4798 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 443.0, count: 572 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.1859986299999994, count: 4798 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 203.0, count: 11 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.998920834 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-07-28T09:23:42.883Z DEBUG slog: got a response for stream_id=132, headers=[":status: 200", "cf-team: 28f1f16ca20000d2c32b24c400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:23:42.940Z DEBUG tunnel_loop{protocol="masque" con_id="f9762ad6da1e4d210b479bd63a9bd787dd881053"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 7, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-07-28T09:23:55.456Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:23:57.883Z DEBUG slog: got a response for stream_id=136, headers=[":status: 200", "cf-team: 28f1f1a73a0000d2c32b3cc400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:24:01.088Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:24:12.883Z DEBUG slog: got a response for stream_id=140, headers=[":status: 200", "cf-team: 28f1f1e1d10000d2c32b4e7400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:24:20.036Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:24:25.408Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:24:27.883Z DEBUG slog: got a response for stream_id=144, headers=[":status: 200", "cf-team: 28f1f21c6a0000d2c32b6e7400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:24:42.883Z DEBUG slog: got a response for stream_id=148, headers=[":status: 200", "cf-team: 28f1f257010000d2c32b8c2400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:24:44.608Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:24:51.008Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:24:57.883Z DEBUG slog: got a response for stream_id=152, headers=[":status: 200", "cf-team: 28f1f2919a0000d2c32b9c9400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:25:09.184Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:25:12.883Z DEBUG slog: got a response for stream_id=156, headers=[":status: 200", "cf-team: 28f1f2cc310000d2c32bb90400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:25:14.304Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:25:27.882Z DEBUG slog: got a response for stream_id=160, headers=[":status: 200", "cf-team: 28f1f306c90000d2c32bce2400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:25:33.764Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:25:39.136Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:25:42.294Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 149 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 5998 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.23082627499999905, count: 5998 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 203.0, count: 11 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 554.0, count: 712 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.991684428 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-07-28T09:25:42.294Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 149 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 5998 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.23082627499999905, count: 5998 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 203.0, count: 11 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 554.0, count: 712 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.991684428 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-07-28T09:25:42.884Z DEBUG slog: got a response for stream_id=164, headers=[":status: 200", "cf-team: 28f1f341620000d2c32bdf9400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:25:42.940Z DEBUG tunnel_loop{protocol="masque" con_id="f9762ad6da1e4d210b479bd63a9bd787dd881053"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 8, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-07-28T09:25:57.882Z DEBUG slog: got a response for stream_id=168, headers=[":status: 200", "cf-team: 28f1f37bf90000d2c32bfdc400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:25:58.336Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:26:03.712Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:26:12.882Z DEBUG slog: got a response for stream_id=172, headers=[":status: 200", "cf-team: 28f1f3b6910000d2c32c108400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:26:22.912Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:26:27.883Z DEBUG slog: got a response for stream_id=176, headers=[":status: 200", "cf-team: 28f1f3f1290000d2c32c1e7400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:26:28.032Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:26:28.034Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:26:42.883Z DEBUG slog: got a response for stream_id=180, headers=[":status: 200", "cf-team: 28f1f42bc20000d2c32c2df400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:27:38.576Z  INFO warp::warp_service: Starting WarpService pid=20
2025-07-28T09:27:38.576Z  INFO warp::warp_service: Version: 2025.5.943.0
2025-07-28T09:27:38.583Z DEBUG warp_settings::raw_settings: Loading settings from file "/var/lib/cloudflare-warp/settings.json"
2025-07-28T09:27:38.583Z DEBUG warp_settings::raw_settings: Loading consumer settings from file "/var/lib/cloudflare-warp/consumer-settings.json"
2025-07-28T09:27:38.583Z  INFO warp_settings::manager: Consumer preferences not loaded e=No such file or directory (os error 2)
2025-07-28T09:27:38.584Z DEBUG warp_settings::manager::final_overrides: Loading final overrides settings from file "/var/lib/cloudflare-warp/final-overrides-settings.json"
2025-07-28T09:27:38.584Z  INFO warp_settings::manager: Final override settings not loaded e=No such file or directory (os error 2)
2025-07-28T09:27:38.584Z DEBUG warp_settings::manager: Starting local policy file watch parent_path="/var/lib/cloudflare-warp"
2025-07-28T09:27:38.651Z DEBUG warp_api_client::addresses: Initializing base API endpoint settings consumer=ConsumerEndpointConfig { ips: [************, *************, 2606:4700::6810:1854, 2606:4700::6810:c052], api_sni: ApiSni("api.cloudflareclient.com.") } zt=ZeroTrustEndpointConfig { ips: [***************, ***************, 2606:4700:7::a29f:8969, 2606:4700:7::a29f:8a69], api_sni: ApiSni("zero-trust-client.cloudflareclient.com."), notifications_sni: NotificationsSni("notifications.cloudflareclient.com") } compliance_environment=Normal
2025-07-28T09:27:38.652Z DEBUG warp::warp_service: DNS lock file detected. Did the daemon not exit gracefully?
2025-07-28T09:27:38.654Z  WARN network_info::linux::dns_manager: Failed to notify system DNS manager of change. DNS may be broken if systemd-resolved is used owner=Unknown os_config_owner=File err=Failed to restart systemd-resolved

Caused by:
    org.freedesktop.DBus.Error.ServiceUnknown: The name org.freedesktop.systemd1 was not provided by any .service files
2025-07-28T09:27:38.654Z DEBUG warp::warp_service: Restored default DNS resolvers from DNS lock file: <resolv.conf restored>
2025-07-28T09:27:38.654Z  WARN network_info::linux::dns_manager::dns_owner: Could not determine resolv.conf file owner: No comment line contained an owner slug
2025-07-28T09:27:38.661Z DEBUG warp::warp_service: Main loop refactor feature enabled
2025-07-28T09:27:38.661Z DEBUG actor_watchdog: Initializing watchdog actor
2025-07-28T09:27:38.661Z DEBUG watchdog: warp::watchdog: Kicking off watchdog
2025-07-28T09:27:38.661Z DEBUG actor_alternate_network: Initializing alternate network actor
2025-07-28T09:27:38.662Z DEBUG actor_main_loop_compat: Initializing main loop compat actor
2025-07-28T09:27:38.662Z DEBUG actor_statistics: Initializing statistics actor
2025-07-28T09:27:38.662Z DEBUG actor_connectivity: Initializing connectivity actor
2025-07-28T09:27:38.662Z DEBUG actor_remote_dex_command: Initializing remote DEX command actor
2025-07-28T09:27:38.662Z DEBUG actor_device_state: Initializing device state actor
2025-07-28T09:27:38.662Z DEBUG actor_emergency_disconnect: Initializing emergency disconnect actor
2025-07-28T09:27:38.663Z DEBUG actor_registration_verifier: Initializing registration verifier actor
2025-07-28T09:27:38.663Z DEBUG actor_root_ca: Initializing root-ca actor
2025-07-28T09:27:38.663Z DEBUG actor_root_ca: Sleeping for 15s to allow main loop boot-up
2025-07-28T09:27:38.663Z  INFO main_loop: warp_net::ipc: Bound ipc socket name="cloudflare-warp/warp_service" path="/run/cloudflare-warp/warp_service"
2025-07-28T09:27:38.663Z  INFO main_loop: warp::warp_service: Warp IPC listening on "cloudflare-warp/warp_service"
2025-07-28T09:27:38.664Z  INFO firewall::engine: Firewall engine running
2025-07-28T09:27:38.685Z DEBUG network_info::linux::power_notifier: Initializing dbus connection
2025-07-28T09:27:38.685Z DEBUG main_loop: warp_storage::registration::registration_storage: WARPSecret registration loaded 0.20 hours old, refresh at 2025-07-29 9:15:42.620967782 +00:00:00
Registration: 2d6c00fe-5a6c-46fb-a62a-b9aa5ed62ace
Device Identifiers: System User
Auth Method: Consumer
Public key: 3059301306072a8648ce3d020106082a8648ce3d03010703420004113924ef48373289b7e26ff95857ba8b4db98b523b27141ed843834bd7e10785f22e431af487429a370745973dff12d1ba6c99d1ad8dce79a9faa5f050a52165
Interface: WarpEndpoint { v4: **********, v6: 2606:4700:110:8bb0:2b1:9371:a882:ec04 }
Endpoints: [WarpSocketAddrPair { v4: *************:443, v6: [2606:4700:103::2]:443 }, WarpSocketAddrPair { v4: *************:500, v6: [2606:4700:103::2]:500 }, WarpSocketAddrPair { v4: *************:1701, v6: [2606:4700:103::2]:1701 }, WarpSocketAddrPair { v4: *************:4500, v6: [2606:4700:103::2]:4500 }, WarpSocketAddrPair { v4: *************:4443, v6: [2606:4700:103::2]:4443 }, WarpSocketAddrPair { v4: *************:8443, v6: [2606:4700:103::2]:8443 }, WarpSocketAddrPair { v4: *************:8095, v6: [2606:4700:103::2]:8095 }]
Subnet CIDRS: None

Account: Free { id: AccountId(38d2eb15-31ea-470f-92a9-3c26b5351c42), license: "94e5DJS8-Wqh617X9-78cjuC59" }

2025-07-28T09:27:38.685Z DEBUG run: warp_settings::manager: LayerManager update: UserOverrides(<mutator>)
2025-07-28T09:27:38.685Z DEBUG run: warp_settings::manager: UserOverrides updated: UserOverrides { version: Some(Current), always_on: Some(true), operation_mode: None, disable_for_wifi: None, disable_for_ethernet: None, disable_for_networks: None, families: None, dns_log_until: FutureSystemTime(None), gateway_id: None, onboarding: None, organization: None, split_config: None, fallback_domains: None, proxy_port: None, disable_connectivity_checks: None, override_api_endpoint: None, override_doh_endpoint: None, override_warp_endpoint: None, override_tunnel_mtu: None, qlog_log_until: FutureSystemTime(None), masque_settings: None, compliance_environment: None }
2025-07-28T09:27:38.686Z DEBUG run: warp_settings::manager: LayerManager update: NetworkPolicy(Some(NetworkPolicySettings { onboarding: None, operation_mode: None, disable_auto_fallback: None, fallback_domains: None, proxy_port: None, split_config: None, gateway_id: None, support_url: None, allow_mode_switch: None, switch_locked: None, auto_connect: None, captive_portal: None, organization: None, allow_updates: None, allowed_to_leave: None, profile_id: None, lan_allow_settings: Some(LanAllowSettings { minutes: None, subnet_size: SubnetSize(24) }), warp_tunnel_protocol: Some(Masque), masque_settings: None, register_interface_ip_with_dns: None, anycast_split_ips: Some(AnycastSplitIps { always_exclude: [*************, 2606:4700:102::3], always_include: [*************, 2606:4700:102::4] }), firewall_scope: None, sccm_vpn_boundary_support: None, enable_post_quantum: None }))
2025-07-28T09:27:38.686Z DEBUG warp_api_client::client::ops: Sending API request GET api.cloudflareclient.com./v0/client_config
2025-07-28T09:27:38.686Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "watchdog.updates_recv", labels: {}, count: 0 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "statistics.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "device-state.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 2 }, CounterPayload { name: "remote-dex-command.updates_recv", labels: {}, count: 0 }, CounterPayload { name: "remote-dex-command.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 0 }, CounterPayload { name: "connectivity.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 0 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "emergency-disconnect.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "main-loop-compat.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "alternate-network.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "statistics.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "root-ca.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "device-state.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "root-ca.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "message_bus.bus_driver_request_timeouts", labels: {}, count: 0 }, CounterPayload { name: "remote-dex-command.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "alternate-network.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "watchdog.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "alternate-network.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "root-ca.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "device-state.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "main-loop-compat.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "emergency-disconnect.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "emergency-disconnect.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "emergency-disconnect.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "watchdog.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "connectivity.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "root-ca.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "connectivity.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "remote-dex-command.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "registration-verifier.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "connectivity.updates_recv", labels: {}, count: 0 }], summaries: [SummaryPayload { name: "device-state.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "statistics.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "emergency-disconnect.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.002048008, count: 1 }, SummaryPayload { name: "remote-dex-command.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "main-loop-compat.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "registration-verifier.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "root-ca.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "watchdog.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "connectivity.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "alternate-network.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 0.000672259 }, GaugesPayload { name: "message_bus.bus_driver_current_actors", labels: {}, value: 10.0 }] } context="registration change"}: actor_statistics::handler: Starting to upload stats
2025-07-28T09:27:38.686Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "watchdog.updates_recv", labels: {}, count: 0 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "statistics.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "device-state.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 2 }, CounterPayload { name: "remote-dex-command.updates_recv", labels: {}, count: 0 }, CounterPayload { name: "remote-dex-command.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 0 }, CounterPayload { name: "connectivity.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 0 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "emergency-disconnect.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "main-loop-compat.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "alternate-network.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "statistics.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "root-ca.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "device-state.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "root-ca.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "message_bus.bus_driver_request_timeouts", labels: {}, count: 0 }, CounterPayload { name: "remote-dex-command.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "alternate-network.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "watchdog.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "alternate-network.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "root-ca.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "device-state.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "main-loop-compat.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "emergency-disconnect.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "emergency-disconnect.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "emergency-disconnect.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "watchdog.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "connectivity.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "root-ca.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "connectivity.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "remote-dex-command.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "registration-verifier.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "connectivity.updates_recv", labels: {}, count: 0 }], summaries: [SummaryPayload { name: "device-state.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "statistics.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "emergency-disconnect.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.002048008, count: 1 }, SummaryPayload { name: "remote-dex-command.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "main-loop-compat.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "registration-verifier.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "root-ca.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "watchdog.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "connectivity.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "alternate-network.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 0.000672259 }, GaugesPayload { name: "message_bus.bus_driver_current_actors", labels: {}, value: 10.0 }] } context="registration change"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-07-28T09:27:38.687Z  INFO actor_statistics::handler: Switched to new registration for stats. All stats have been reset. new_registration_id=None
2025-07-28T09:27:38.722Z DEBUG run: warp_settings::manager: LayerManager update: NetworkDefaults(NetworkDefaults { split_config: Some(Exclude { ips: [(10.0.0.0/8, None), (**********/10, None), (***********/16, None), (**********/12, None), (*********/24, None), (***********/16, None), (*********/24, None), (240.0.0.0/4, None), (***************/32, None), (***************/32, None), (fe80::/10, None), (fd00::/8, None), (ff01::/16, None), (ff02::/16, None), (ff03::/16, None), (ff04::/16, None), (ff05::/16, None), (fc00::/7, None), (**********/16, None), (**********/16, None), (***********/22, None), (************/18, None), (***********/23, None), (2620:149:a44::/48, None), (2403:300:a42::/48, None), (2403:300:a51::/48, None), (2a01:b740:a42::/48, None)], hosts: [] }) })
2025-07-28T09:27:38.722Z DEBUG main_loop: warp::warp_service: Checking for registration vs settings account mismatch reg_is_teams=false settings_is_teams=false reg_org=None settings_org=None
2025-07-28T09:27:38.722Z DEBUG firewall::change: Firewall allow API ips api_ips=[************, *************, 2606:4700::6810:1854, 2606:4700::6810:c052]
2025-07-28T09:27:38.722Z DEBUG firewall::change: Changing catch all policy Deny->Deny
2025-07-28T09:27:38.722Z DEBUG firewall::change: Firewall allow managed network endpoints managed_network_endpoints=[]
2025-07-28T09:27:38.722Z DEBUG firewall::change: Firewall allow anycast ips: [*************, 2606:4700:102::3]
2025-07-28T09:27:38.722Z  INFO firewall::engine: Firewall starting
2025-07-28T09:27:38.728Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T09:27:38.728Z  INFO main_loop: warp::warp_service: New User Settings
2025-07-28T09:27:38.728Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC update: SettingsUpdated
2025-07-28T09:27:38.728Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseUpdate: SettingsUpdated
2025-07-28T09:27:38.728Z DEBUG main_loop: warp::warp_service: update_settings: no restart required
2025-07-28T09:27:38.728Z DEBUG main_loop: warp::warp::controller: Stopping WarpConnection state=Disconnected
2025-07-28T09:27:38.728Z DEBUG firewall::change: Firewall allow API ips api_ips=[************, *************, 2606:4700::6810:1854, 2606:4700::6810:c052]
2025-07-28T09:27:38.728Z DEBUG firewall::change: Changing catch all policy Deny->Deny
2025-07-28T09:27:38.728Z DEBUG firewall::change: Firewall allow managed network endpoints managed_network_endpoints=[]
2025-07-28T09:27:38.728Z DEBUG firewall::change: Firewall allow anycast ips: [*************, 2606:4700:102::3]
2025-07-28T09:27:38.761Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T09:27:38.761Z DEBUG main_loop: warp::warp_service: Determining disconnected reason from connectivity state net_info=IPv4: [eth0; **********; Ethernet; Gateway: Some(**********)], DNS servers:,  127.0.0.11:53
 power_state=None disconnect_reason=None
2025-07-28T09:27:38.761Z  WARN main_loop: warp::warp_service: Disconnecting, but reason is unknown
2025-07-28T09:27:38.761Z DEBUG main_loop: warp_api_client::executor: Spawning API request for GetRegistrationConfig request_id=6583e334-6a10-4cf2-b080-9cc6420aa6d9
2025-07-28T09:27:38.762Z DEBUG main_loop: warp::warp_service: Determining disconnected reason from connectivity state net_info=IPv4: [eth0; **********; Ethernet; Gateway: Some(**********)], DNS servers:,  127.0.0.11:53
 power_state=None disconnect_reason=None
2025-07-28T09:27:38.762Z  WARN main_loop: warp::warp_service: Disconnecting, but reason is unknown
2025-07-28T09:27:38.762Z  INFO warp::warp_service: Spawning registration changed IPC loop
2025-07-28T09:27:38.762Z DEBUG warp_api_client::apis::registration: Getting registration registration_id=Client(ConsumerRegistrationId(2d6c00fe-5a6c-46fb-a62a-b9aa5ed62ace))
2025-07-28T09:27:38.762Z DEBUG warp_api_client::client: Sending API request GET https://api.cloudflareclient.com.//v0/reg/2d6c00fe-5a6c-46fb-a62a-b9aa5ed62ace
2025-07-28T09:27:38.762Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 2 }, CounterPayload { name: "emergency-disconnect.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "registration-verifier.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "root-ca.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 2 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 37.0, count: 2 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.000546329, count: 1 }], gauges: [] } context="registration change"}: actor_statistics::handler: Starting to upload stats
2025-07-28T09:27:38.762Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 2 }, CounterPayload { name: "emergency-disconnect.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "registration-verifier.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "root-ca.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 2 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 37.0, count: 2 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.000546329, count: 1 }], gauges: [] } context="registration change"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-07-28T09:27:38.762Z  INFO actor_statistics::handler: Switched to new registration for stats. All stats have been reset. new_registration_id=None
2025-07-28T09:27:38.763Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="main_loop_compat_rx"
2025-07-28T09:27:38.764Z DEBUG main_loop:handle_command: warp::warp::controller: Stopping WarpConnection state=Disconnected
2025-07-28T09:27:38.764Z DEBUG firewall::change: Firewall allow API ips api_ips=[************, *************, 2606:4700::6810:1854, 2606:4700::6810:c052]
2025-07-28T09:27:38.764Z DEBUG firewall::change: Changing catch all policy Deny->Deny
2025-07-28T09:27:38.764Z DEBUG firewall::change: Firewall allow managed network endpoints managed_network_endpoints=[]
2025-07-28T09:27:38.764Z DEBUG firewall::change: Firewall allow anycast ips: [*************, 2606:4700:102::3]
2025-07-28T09:27:38.771Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T09:27:38.771Z  INFO main_loop:handle_command: warp::warp_service: captive_portal_fw_until: Indefinitely
2025-07-28T09:27:38.771Z DEBUG main_loop:handle_command: warp::warp::proxying_dns: Using auto fallback: true
2025-07-28T09:27:38.771Z DEBUG main_loop:handle_command: warp::warp::controller: Setting WarpConnection current_state=Disconnected
2025-07-28T09:27:38.771Z DEBUG main_loop:handle_command: warp::warp::controller: Stopping WarpConnection state=Disconnected
2025-07-28T09:27:38.771Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="main_loop_compat_rx" elapsed=9.008631ms
2025-07-28T09:27:38.771Z DEBUG main_loop: warp::warp::warp_connection: Current Network: IPv4: [eth0; **********; Ethernet; Gateway: Some(**********)], DNS servers:,  127.0.0.11:53

2025-07-28T09:27:38.772Z  INFO main_loop: warp::warp::warp_connection: Starting Warp Connection operation_mode=Warp dns_mode=DNS Proxy tunnel_mode=Exclude-only Tunnel posture_only=disabled forward_proxy=disabled
2025-07-28T09:27:38.772Z  INFO main_loop: warp::warp::tunnel: Initiate WARP connection protocol=Masque self.masque_settings=MasqueProtocolSettings { http_version: H3WithH2Fallback }
2025-07-28T09:27:38.772Z  INFO main_loop: warp::warp_service: WARP status: Connecting(CheckingNetwork)
2025-07-28T09:27:38.772Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Connecting(CheckingNetwork)
2025-07-28T09:27:38.772Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Connecting(CheckingNetwork)
2025-07-28T09:27:38.772Z DEBUG main_loop: warp::warp_service: Checking for registration vs settings account mismatch reg_is_teams=false settings_is_teams=false reg_org=None settings_org=None
2025-07-28T09:27:38.772Z DEBUG firewall::change: Firewall allow tunnel UDP ipv4=[*************] ipv6=[2606:4700:103::2]
2025-07-28T09:27:38.772Z DEBUG firewall::change: Firewall allow API ips api_ips=[************, *************, 2606:4700::6810:1854, 2606:4700::6810:c052]
2025-07-28T09:27:38.772Z DEBUG firewall::change: Changing catch all policy Deny->Deny
2025-07-28T09:27:38.772Z DEBUG firewall::change: Firewall allow managed network endpoints managed_network_endpoints=[]
2025-07-28T09:27:38.772Z DEBUG firewall::change: Firewall allow anycast ips: [*************, 2606:4700:102::3]
2025-07-28T09:27:38.772Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T09:27:38.772Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=285.594µs
2025-07-28T09:27:38.772Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="settings_changed"
2025-07-28T09:27:38.801Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T09:27:38.801Z  INFO main_loop: warp::warp_service: User Settings Changed: {}
2025-07-28T09:27:38.801Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC update: SettingsUpdated
2025-07-28T09:27:38.801Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseUpdate: SettingsUpdated
2025-07-28T09:27:38.801Z DEBUG main_loop: warp::warp_service: update_settings: no restart required
2025-07-28T09:27:38.801Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="settings_changed" elapsed=29.059352ms
2025-07-28T09:27:38.801Z DEBUG firewall::change: Firewall allow tunnel TCP addresses=[*************:443, *************:500, *************:1701, *************:4443, *************:4500, *************:8095, *************:8443, [2606:4700:103::2]:443, [2606:4700:103::2]:500, [2606:4700:103::2]:1701, [2606:4700:103::2]:4443, [2606:4700:103::2]:4500, [2606:4700:103::2]:8095, [2606:4700:103::2]:8443]
2025-07-28T09:27:38.812Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T09:27:38.813Z DEBUG firewall::change: Firewall allow captive portal detection
2025-07-28T09:27:38.857Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T09:27:38.858Z DEBUG main_loop:connect_with_fallback{primary="masque" fallback="H2"}:happy_eyeballs_with_retry{protocol="masque"}: warp_edge::happy_eyeballs: Attempting happy eyeballs with retries retries=0 retry_delay=0ns stage=FirstRun
2025-07-28T09:27:38.858Z DEBUG captive_portal: Running captive portal detections [legacy,os-detected,http-redirect[cloudflareportal.com],no-dns-redirect[cloudflareportal.com],expected-content,third-party]
2025-07-28T09:27:38.858Z DEBUG run{detection_type=os-detected}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T09:27:38.858Z  INFO run{detection_type=legacy}: captive_portal::detections::legacy: Checking for captive portal with legacy detection urls=["cloudflareportal.com", "cloudflareok.com", "cloudflarecp.com"]
2025-07-28T09:27:38.858Z  INFO captive_portal::detections::http_redirect: Checking for captive portal with HTTP-Redirect detection host="connectivitycheck.gstatic.com" ips=None interface=None bind_addr=None
2025-07-28T09:27:38.858Z  INFO captive_portal::detections::http_redirect: Checking for captive portal with HTTP-Redirect detection host="www.msftconnecttest.com" ips=None interface=None bind_addr=None
2025-07-28T09:27:38.858Z  INFO run{detection_type=http-redirect[cloudflareportal.com]}: captive_portal::detections::http_redirect: Checking for captive portal with HTTP-Redirect detection host="cloudflareportal.com" ips=None interface=None bind_addr=None
2025-07-28T09:27:38.859Z DEBUG main_loop:connect_with_fallback{primary="masque" fallback="H2"}:happy_eyeballs_with_retry{protocol="masque"}: warp_edge::happy_eyeballs: Attempting Happy Eyeballs to *************:443 / [2606:4700:103::2]:443
2025-07-28T09:27:38.859Z DEBUG main_loop:connect_with_fallback{primary="masque" fallback="H2"}:happy_eyeballs_with_retry{protocol="masque"}: warp_edge::happy_eyeballs: Start racer **********:55852 ---> *************:443
2025-07-28T09:27:38.859Z DEBUG main_loop:connect_with_fallback{primary="masque" fallback="H2"}:happy_eyeballs_with_retry{protocol="masque"}: warp_edge::h3_tun: Creating QUIC Client Connection Parmaeters qlog_setting=Disabled idle_timeout=IdleTimeout { idle_duration: IdleDuration(5s), retries: 0 }
2025-07-28T09:27:38.860Z DEBUG main_loop:connect_with_fallback{primary="masque" fallback="H2"}:happy_eyeballs_with_retry{protocol="masque"}: warp_edge::h3_tun: Sending handshake initiation to *************:443
2025-07-28T09:27:38.860Z  INFO captive_portal::detections::http_redirect: Checking for captive portal with HTTP-Redirect detection host="captive.apple.com" ips=None interface=None bind_addr=None
2025-07-28T09:27:38.861Z  INFO run{detection_type=no-dns-redirect[cloudflareportal.com]}: captive_portal::detections::http_redirect: Checking for captive portal with HTTP-Redirect detection host="cloudflareportal.com" ips=Some([**********, **************, **********, 2606:4700:7::1, 2606:4700::1]) interface=None bind_addr=None
2025-07-28T09:27:38.861Z  INFO slog: created unestablished quiche::Connection slog.target="tokio_quiche::quic" slog.module_path="tokio_quiche::quic" slog.file="/home/<USER>/.cargo/git/checkouts/quiche-4103ffa15137cf39/bfd737b/tokio-quiche/src/quic/mod.rs" slog.line=235 slog.column=5 slog.kv="provided_dcid=None,scid=32a76b55bcf96cbf19dc0d297b20f87cc3db7621"
2025-07-28T09:27:38.861Z DEBUG main_loop:connect_with_fallback{primary="masque" fallback="H2"}:happy_eyeballs_with_retry{protocol="masque"}: warp_edge::happy_eyeballs: Happy Eyeballs check failed v6=[2606:4700:103::2]:443 err=Connect(Os { code: 101, kind: NetworkUnreachable, message: "Network is unreachable" })
2025-07-28T09:27:38.861Z  INFO main_loop: warp::warp_service: WARP status: Connecting(PerformingHappyEyeballs((*************:443, [2606:4700:103::2]:443)))
2025-07-28T09:27:38.861Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Connecting(PerformingHappyEyeballs((*************:443, [2606:4700:103::2]:443)))
2025-07-28T09:27:38.861Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Connecting(PerformingHappyEyeballs((*************:443, [2606:4700:103::2]:443)))
2025-07-28T09:27:38.862Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T09:27:38.862Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=548.283µs
2025-07-28T09:27:38.862Z DEBUG slog: sending client Initials to peer slog.target="tokio_quiche::quic::router::connector" slog.module_path="tokio_quiche::quic::router::connector" slog.file="/home/<USER>/.cargo/git/checkouts/quiche-4103ffa15137cf39/bfd737b/tokio-quiche/src/quic/router/connector.rs" slog.line=266 slog.column=5 slog.kv="scid=32a76b55bcf96cbf19dc0d297b20f87cc3db7621"
2025-07-28T09:27:38.867Z DEBUG slog: sending client Initials to peer slog.target="tokio_quiche::quic::router::connector" slog.module_path="tokio_quiche::quic::router::connector" slog.file="/home/<USER>/.cargo/git/checkouts/quiche-4103ffa15137cf39/bfd737b/tokio-quiche/src/quic/router/connector.rs" slog.line=266 slog.column=5 slog.kv="scid=32a76b55bcf96cbf19dc0d297b20f87cc3db7621"
2025-07-28T09:27:38.867Z  INFO run{detection_type=legacy}: captive_portal::detections::legacy: Successfully resolved DNS queries
2025-07-28T09:27:38.868Z DEBUG captive_portal::detections::http_redirect: 204 response for HTTP test. No captive portal detected
2025-07-28T09:27:38.870Z DEBUG captive_portal::detections::http_redirect: 200 response for HTTP test. No captive portal detected
2025-07-28T09:27:38.871Z DEBUG captive_portal::detections::http_redirect: 200 response for HTTP test. No captive portal detected
2025-07-28T09:27:38.871Z DEBUG run{detection_type=third-party}: captive_portal::detections::third_party: Third party detections complete detections=[("connectivitycheck.gstatic.com", Ok(NoCaptivePortalDetected)), ("www.msftconnecttest.com", Ok(NoCaptivePortalDetected)), ("captive.apple.com", Ok(NoCaptivePortalDetected))]
2025-07-28T09:27:38.871Z DEBUG run{detection_type=third-party}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T09:27:38.873Z DEBUG slog: sending client Initials to peer slog.target="tokio_quiche::quic::router::connector" slog.module_path="tokio_quiche::quic::router::connector" slog.file="/home/<USER>/.cargo/git/checkouts/quiche-4103ffa15137cf39/bfd737b/tokio-quiche/src/quic/router/connector.rs" slog.line=266 slog.column=5 slog.kv="scid=32a76b55bcf96cbf19dc0d297b20f87cc3db7621"
2025-07-28T09:27:38.877Z DEBUG slog: QUIC connection established slog.target="tokio_quiche::quic::router::connector" slog.module_path="tokio_quiche::quic::router::connector" slog.file="/home/<USER>/.cargo/git/checkouts/quiche-4103ffa15137cf39/bfd737b/tokio-quiche/src/quic/router/connector.rs" slog.line=176 slog.column=13 slog.kv="scid=32a76b55bcf96cbf19dc0d297b20f87cc3db7621"
2025-07-28T09:27:38.878Z DEBUG main_loop:connect_with_fallback{primary="masque" fallback="H2"}:happy_eyeballs_with_retry{protocol="masque"}: warp_edge::h3_tun: Established QUIC connection with *************:443 scid=32a76b55bcf96cbf19dc0d297b20f87cc3db7621
2025-07-28T09:27:38.878Z DEBUG main_loop:connect_with_fallback{primary="masque" fallback="H2"}:happy_eyeballs_with_retry{protocol="masque"}: warp_edge::h3_tun: Establishing MASQUE tunnel on existing QUIC connection scid=32a76b55bcf96cbf19dc0d297b20f87cc3db7621 endpoint=*************:443
2025-07-28T09:27:38.878Z  INFO main_loop:connect_with_fallback{primary="masque" fallback="H2"}:happy_eyeballs_with_retry{protocol="masque"}:proxy_task{scid=32a76b55bcf96cbf19dc0d297b20f87cc3db7621}: warp_edge::h3_tun: Starting MASQUE proxy task idle_timeout=IdleTimeout { idle_duration: IdleDuration(5s), retries: 0 } keepalive_interval=4s
2025-07-28T09:27:38.878Z  INFO main_loop: warp::warp_service: WARP status: Connecting(EstablishingConnection(*************:443))
2025-07-28T09:27:38.878Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T09:27:38.878Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Connecting(EstablishingConnection(*************:443))
2025-07-28T09:27:38.878Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Connecting(EstablishingConnection(*************:443))
2025-07-28T09:27:38.878Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=76.485µs
2025-07-28T09:27:38.878Z  INFO slog: creating new flow for MASQUE request slog.target="tokio_quiche::http3::driver::client" slog.module_path="tokio_quiche::http3::driver::client" slog.file="/home/<USER>/.cargo/git/checkouts/quiche-4103ffa15137cf39/bfd737b/tokio-quiche/src/http3/driver/client.rs" slog.line=168 slog.column=13 slog.kv="flow_id=0,stream_id=0"
2025-07-28T09:27:38.884Z DEBUG run{detection_type=expected-content}: captive_portal::detections::expected_content: 200 response and correct content for HTTP Content test. No captive portal detected
2025-07-28T09:27:38.884Z DEBUG run{detection_type=expected-content}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T09:27:38.887Z DEBUG run{detection_type=no-dns-redirect[cloudflareportal.com]}: captive_portal::detections::http_redirect: 204 response for HTTP test. No captive portal detected
2025-07-28T09:27:38.887Z DEBUG run{detection_type=no-dns-redirect[cloudflareportal.com]}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T09:27:38.896Z DEBUG run{detection_type=http-redirect[cloudflareportal.com]}: captive_portal::detections::http_redirect: 204 response for HTTP test. No captive portal detected
2025-07-28T09:27:38.896Z DEBUG run{detection_type=http-redirect[cloudflareportal.com]}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T09:27:38.903Z  INFO run{detection_type=legacy}: captive_portal::detections::legacy: Successfully retrieved HTTPs page
2025-07-28T09:27:38.903Z DEBUG run{detection_type=legacy}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T09:27:38.904Z DEBUG captive_portal: Captive portal detection completed results=[("os-detected", Ok(NoCaptivePortalDetected)), ("third-party", Ok(NoCaptivePortalDetected)), ("expected-content", Ok(NoCaptivePortalDetected)), ("no-dns-redirect[cloudflareportal.com]", Ok(NoCaptivePortalDetected)), ("http-redirect[cloudflareportal.com]", Ok(NoCaptivePortalDetected)), ("legacy", Ok(NoCaptivePortalDetected))]
2025-07-28T09:27:38.904Z DEBUG firewall::change: Firewall disallow captive portal detection
2025-07-28T09:27:38.914Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T09:27:38.915Z DEBUG firewall::captive_portal: Firewall captive portal detection expired
2025-07-28T09:27:38.936Z DEBUG warp_api_endpoints::payloads: Warp endpoint override ports ports=[443, 500, 1701, 4500, 4443, 8443, 8095]
2025-07-28T09:27:38.936Z DEBUG main_loop: warp_api_client::executor: Returning API response for GetRegistrationConfig request_id=6583e334-6a10-4cf2-b080-9cc6420aa6d9
2025-07-28T09:27:38.936Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="api_responses"
2025-07-28T09:27:38.936Z DEBUG run: warp_settings::manager: LayerManager update: NetworkPolicy(Some(NetworkPolicySettings { onboarding: None, operation_mode: None, disable_auto_fallback: None, fallback_domains: None, proxy_port: None, split_config: None, gateway_id: None, support_url: None, allow_mode_switch: None, switch_locked: None, auto_connect: None, captive_portal: None, organization: None, allow_updates: None, allowed_to_leave: None, profile_id: None, lan_allow_settings: Some(LanAllowSettings { minutes: None, subnet_size: SubnetSize(24) }), warp_tunnel_protocol: Some(Masque), masque_settings: None, register_interface_ip_with_dns: None, anycast_split_ips: Some(AnycastSplitIps { always_exclude: [*************, 2606:4700:102::3], always_include: [*************, 2606:4700:102::4] }), firewall_scope: None, sccm_vpn_boundary_support: None, enable_post_quantum: None }))
2025-07-28T09:27:38.936Z DEBUG main_loop: warp::warp_service::api_handlers: Registration updated current_registration=RefreshInProgress(reg=2d6c00fe-5a6c-46fb-a62a-b9aa5ed62ace) disposition=CheckAndUpdate
2025-07-28T09:27:38.936Z DEBUG main_loop: warp::warp_service::api_handlers: Registration is up to date, updating refresh time
2025-07-28T09:27:38.936Z DEBUG main_loop: warp::warp_service::api_handlers: Registration refresh time updated 0.00 hours old, refresh at 2025-07-29 9:27:38.936173967 +00:00:00
Registration: 2d6c00fe-5a6c-46fb-a62a-b9aa5ed62ace
Device Identifiers: System User
Auth Method: Consumer
Public key: 3059301306072a8648ce3d020106082a8648ce3d03010703420004113924ef48373289b7e26ff95857ba8b4db98b523b27141ed843834bd7e10785f22e431af487429a370745973dff12d1ba6c99d1ad8dce79a9faa5f050a52165
Interface: WarpEndpoint { v4: **********, v6: 2606:4700:110:8bb0:2b1:9371:a882:ec04 }
Endpoints: [WarpSocketAddrPair { v4: *************:443, v6: [2606:4700:103::2]:443 }, WarpSocketAddrPair { v4: *************:500, v6: [2606:4700:103::2]:500 }, WarpSocketAddrPair { v4: *************:1701, v6: [2606:4700:103::2]:1701 }, WarpSocketAddrPair { v4: *************:4500, v6: [2606:4700:103::2]:4500 }, WarpSocketAddrPair { v4: *************:4443, v6: [2606:4700:103::2]:4443 }, WarpSocketAddrPair { v4: *************:8443, v6: [2606:4700:103::2]:8443 }, WarpSocketAddrPair { v4: *************:8095, v6: [2606:4700:103::2]:8095 }]
Subnet CIDRS: None

Account: Free { id: AccountId(38d2eb15-31ea-470f-92a9-3c26b5351c42), license: "94e5DJS8-Wqh617X9-78cjuC59" }

2025-07-28T09:27:38.937Z  INFO main_loop: warp_storage::registration::registration_storage: Saving registration to active cache config_name=None username="WARPSecret"
2025-07-28T09:27:38.937Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="api_responses" elapsed=1.081768ms
2025-07-28T09:27:39.167Z DEBUG slog: got a response for stream_id=0, headers=[":status: 200", "cf-team: 28f1f5079e0000dc6e3384c400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:27:39.167Z DEBUG slog: upgraded H3Body: flow_id=0, mode=WithContextId slog.target="httx::body" slog.module_path="httx::body" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/body/mod.rs" slog.line=678 slog.column=5
2025-07-28T09:27:39.167Z DEBUG main_loop:connect_with_fallback{primary="masque" fallback="H2"}: warp::warp::tunnel::fallback: Connecting with primary connection protocol, reverting fallback firewall config
2025-07-28T09:27:39.168Z DEBUG firewall::change: Firewall allow tunnel TCP addresses=[]
2025-07-28T09:27:39.175Z DEBUG slog: got a response for stream_id=4, headers=[":status: 200", "cf-team: 28f1f507a60000dc6e3384e400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:27:39.175Z DEBUG main_loop:connect_with_fallback{primary="masque" fallback="H2"}:tunnel_stats_reporting_task{scid=32a76b55bcf96cbf19dc0d297b20f87cc3db7621}: warp_edge::h3_tun::tunnel_stats: Updating tunnel stats reporting interval from=120s to=15s
2025-07-28T09:27:39.178Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T09:27:39.178Z DEBUG main_loop:start_tunnel_processing{protocol="masque"}: warp::warp::tunnel: Connected to *************:443
2025-07-28T09:27:39.178Z  INFO main_loop:start_tunnel_processing{protocol="masque"}: warp::warp::tunnel: DNS resolver connection is being made inside of the tunnel
2025-07-28T09:27:39.179Z  INFO main_loop: warp::warp_service: WARP status: Connecting(ConfiguringInitialFirewall)
2025-07-28T09:27:39.179Z DEBUG firewall::change: Firewall allow interface CloudflareWARP
2025-07-28T09:27:39.179Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Connecting(ConfiguringInitialFirewall)
2025-07-28T09:27:39.179Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Connecting(ConfiguringInitialFirewall)
2025-07-28T09:27:39.179Z  INFO main_loop: warp::warp_service: WARP status: Connecting(ConfiguringInitialFirewall)
2025-07-28T09:27:39.179Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Connecting(ConfiguringInitialFirewall)
2025-07-28T09:27:39.179Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Connecting(ConfiguringInitialFirewall)
2025-07-28T09:27:39.179Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T09:27:39.179Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=111.903µs
2025-07-28T09:27:39.179Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T09:27:39.179Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=107.795µs
2025-07-28T09:27:39.219Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T09:27:39.237Z  INFO main_loop: warp::warp_service: WARP status: Connecting(SettingRoutes)
2025-07-28T09:27:39.237Z DEBUG firewall::change: Firewall allowing private IPs
2025-07-28T09:27:39.237Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Connecting(SettingRoutes)
2025-07-28T09:27:39.237Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Connecting(SettingRoutes)
2025-07-28T09:27:39.237Z  INFO main_loop: warp::warp_service: WARP status: Connecting(ConfiguringFirewallRules)
2025-07-28T09:27:39.237Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Connecting(ConfiguringFirewallRules)
2025-07-28T09:27:39.237Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Connecting(ConfiguringFirewallRules)
2025-07-28T09:27:39.238Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T09:27:39.238Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=786.626µs
2025-07-28T09:27:39.238Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T09:27:39.238Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=744.116µs
2025-07-28T09:27:39.245Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T09:27:39.245Z  INFO main_loop: warp::warp::proxying_dns: Initiate DNS connection
2025-07-28T09:27:39.245Z DEBUG main_loop: warp::warp::proxying_dns: Racing DNS IPs ips=[************, ************, 2606:4700:4700::1111, 2606:4700:4700::1001]
2025-07-28T09:27:39.246Z DEBUG tunnel_loop{protocol="masque" con_id="32a76b55bcf96cbf19dc0d297b20f87cc3db7621"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 1, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-07-28T09:27:39.253Z DEBUG main_loop: warp::warp::proxying_dns: DNS TCP connection to Gateway DoH resolver was successful raced_ip=************
2025-07-28T09:27:39.253Z DEBUG main_loop: dns_proxy::proxy: Creating DoH resolver config=ResolverConfig { domain: None, search: [], name_servers: NameServerConfigGroup([NameServerConfig { socket_addr: ************:443, protocol: Https, tls_dns_name: Some("cloudflare-dns.com"), trust_negative_responses: true, tls_config: None, bind_addr: None }, NameServerConfig { socket_addr: ************:443, protocol: Https, tls_dns_name: Some("cloudflare-dns.com"), trust_negative_responses: true, tls_config: None, bind_addr: None }, NameServerConfig { socket_addr: [2606:4700:4700::1111]:443, protocol: Https, tls_dns_name: Some("cloudflare-dns.com"), trust_negative_responses: true, tls_config: None, bind_addr: None }, NameServerConfig { socket_addr: [2606:4700:4700::1001]:443, protocol: Https, tls_dns_name: Some("cloudflare-dns.com"), trust_negative_responses: true, tls_config: None, bind_addr: None }], None) } options=ResolverOpts { ndots: 1, timeout: 4s, attempts: 0, rotate: false, check_names: true, edns0: true, validate: false, ip_strategy: Ipv4thenIpv6, cache_size: 32, use_hosts_file: true, positive_min_ttl: None, negative_min_ttl: None, positive_max_ttl: None, negative_max_ttl: Some(10s), num_concurrent_reqs: 1, preserve_intermediates: true, try_tcp_on_error: false, server_ordering_strategy: QueryStatistics, recursion_desired: true, authentic_data: false, shuffle_dns_servers: false }
2025-07-28T09:27:39.253Z DEBUG firewall::change: Firewall allow captive portal detection
2025-07-28T09:27:39.254Z  INFO main_loop: warp::warp_service: WARP status: Connecting(CheckingForRouteToDnsEndpoint)
2025-07-28T09:27:39.254Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T09:27:39.254Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Connecting(CheckingForRouteToDnsEndpoint)
2025-07-28T09:27:39.254Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Connecting(CheckingForRouteToDnsEndpoint)
2025-07-28T09:27:39.254Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=239.145µs
2025-07-28T09:27:39.289Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T09:27:39.290Z DEBUG captive_portal: Running captive portal detections [legacy,os-detected,http-redirect[cloudflareportal.com],no-dns-redirect[cloudflareportal.com],expected-content,third-party]
2025-07-28T09:27:39.290Z  INFO captive_portal::detections::http_redirect: Checking for captive portal with HTTP-Redirect detection host="www.msftconnecttest.com" ips=None interface=Some("eth0") bind_addr=Some(**********)
2025-07-28T09:27:39.290Z  INFO run{detection_type=http-redirect[cloudflareportal.com]}: captive_portal::detections::http_redirect: Checking for captive portal with HTTP-Redirect detection host="cloudflareportal.com" ips=None interface=Some("eth0") bind_addr=Some(**********)
2025-07-28T09:27:39.290Z DEBUG run{detection_type=os-detected}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T09:27:39.291Z  INFO run{detection_type=legacy}: captive_portal::detections::legacy: Checking for captive portal with legacy detection urls=["cloudflareportal.com", "cloudflareok.com", "cloudflarecp.com"]
2025-07-28T09:27:39.291Z  INFO captive_portal::detections::http_redirect: Checking for captive portal with HTTP-Redirect detection host="connectivitycheck.gstatic.com" ips=None interface=Some("eth0") bind_addr=Some(**********)
2025-07-28T09:27:39.290Z  INFO run{detection_type=no-dns-redirect[cloudflareportal.com]}: captive_portal::detections::http_redirect: Checking for captive portal with HTTP-Redirect detection host="cloudflareportal.com" ips=Some([**********, **************, **********, 2606:4700:7::1, 2606:4700::1]) interface=Some("eth0") bind_addr=Some(**********)
2025-07-28T09:27:39.292Z  INFO run{detection_type=legacy}: captive_portal::detections::legacy: Successfully resolved DNS queries
2025-07-28T09:27:39.292Z  INFO captive_portal::detections::http_redirect: Checking for captive portal with HTTP-Redirect detection host="captive.apple.com" ips=None interface=Some("eth0") bind_addr=Some(**********)
2025-07-28T09:27:39.302Z DEBUG captive_portal::detections::http_redirect: 204 response for HTTP test. No captive portal detected
2025-07-28T09:27:39.303Z DEBUG captive_portal::detections::http_redirect: 200 response for HTTP test. No captive portal detected
2025-07-28T09:27:39.313Z DEBUG run{detection_type=no-dns-redirect[cloudflareportal.com]}: captive_portal::detections::http_redirect: 204 response for HTTP test. No captive portal detected
2025-07-28T09:27:39.313Z DEBUG run{detection_type=no-dns-redirect[cloudflareportal.com]}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T09:27:39.314Z DEBUG run{detection_type=expected-content}: captive_portal::detections::expected_content: 200 response and correct content for HTTP Content test. No captive portal detected
2025-07-28T09:27:39.314Z DEBUG run{detection_type=expected-content}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T09:27:39.316Z DEBUG run{detection_type=http-redirect[cloudflareportal.com]}: captive_portal::detections::http_redirect: 204 response for HTTP test. No captive portal detected
2025-07-28T09:27:39.316Z DEBUG run{detection_type=http-redirect[cloudflareportal.com]}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T09:27:39.321Z DEBUG main_loop: warp::warp::proxying_dns: Connectivity Check resolved query for connectivity.cloudflareclient.com resolved_ips=[**************, **************]
2025-07-28T09:27:39.321Z DEBUG main_loop: dns_proxy::resolver: Ending DoH connection stats=establish_time:2025-07-28 9:27:39.290394185 +00:00:00, ip:2606:4700:4700::1001, attempted_queries:1, successful_queries:1, certificate_errors:0, timeout_errors:0, no_connection_errors:0, proto_errors:0, io_errors:0, msg_errors:0
2025-07-28T09:27:39.321Z DEBUG main_loop: warp::warp::proxying_dns: Acquiring bind semaphore available_permits=1
2025-07-28T09:27:39.321Z DEBUG main_loop: warp::warp::proxying_dns: Binding UDP and TCP sockets dns_servers=[*********, *********]
2025-07-28T09:27:39.322Z DEBUG main_loop: warp::warp::proxying_dns: Successfully bound DNS listen sockets udp_sockets=2 tcp_sockets=2
2025-07-28T09:27:39.323Z DEBUG main_loop: warp::warp::proxying_dns: Read system DNS configuration initial_dns_config=(ResolverConfig { domain: None, search: [], name_servers: NameServerConfigGroup([NameServerConfig { socket_addr: 127.0.0.11:53, protocol: Udp, tls_dns_name: None, trust_negative_responses: false, tls_config: None, bind_addr: None }, NameServerConfig { socket_addr: 127.0.0.11:53, protocol: Tcp, tls_dns_name: None, trust_negative_responses: false, tls_config: None, bind_addr: None }], None) }, ResolverOpts { ndots: 0, timeout: 5s, attempts: 2, rotate: false, check_names: true, edns0: false, validate: false, ip_strategy: Ipv4thenIpv6, cache_size: 32, use_hosts_file: true, positive_min_ttl: None, negative_min_ttl: None, positive_max_ttl: None, negative_max_ttl: None, num_concurrent_reqs: 2, preserve_intermediates: true, try_tcp_on_error: false, server_ordering_strategy: QueryStatistics, recursion_desired: true, authentic_data: false, shuffle_dns_servers: false })
2025-07-28T09:27:39.323Z DEBUG warp_dns: Starting DnsOverWarp task tun_address=**********:0
2025-07-28T09:27:39.323Z DEBUG main_loop: warp::warp::proxying_dns: Initializing DNS restore settings net.v4_ifaces=[eth0; **********; Ethernet; Gateway: Some(**********)] net.v6_ifaces=[]
2025-07-28T09:27:39.323Z DEBUG main_loop: warp::warp::dns_recovery::unix: Applying DNS settings [*********, *********, ::ffff:*********, ::ffff:*********]
2025-07-28T09:27:39.324Z  INFO dns_proxy::proxy: Default fallbacks configured default_fallback_ips=[127.0.0.11:53] config=ResolverConfig { domain: None, search: [], name_servers: NameServerConfigGroup([NameServerConfig { socket_addr: 127.0.0.11:53, protocol: Udp, tls_dns_name: None, trust_negative_responses: true, tls_config: None, bind_addr: None }, NameServerConfig { socket_addr: 127.0.0.11:53, protocol: Tcp, tls_dns_name: None, trust_negative_responses: true, tls_config: None, bind_addr: None }], None) } sys_options=ResolverOpts { ndots: 0, timeout: 11s, attempts: 0, rotate: false, check_names: true, edns0: true, validate: false, ip_strategy: Ipv4thenIpv6, cache_size: 32, use_hosts_file: true, positive_min_ttl: None, negative_min_ttl: None, positive_max_ttl: None, negative_max_ttl: None, num_concurrent_reqs: 8, preserve_intermediates: true, try_tcp_on_error: false, server_ordering_strategy: QueryStatistics, recursion_desired: true, authentic_data: false, shuffle_dns_servers: false }
2025-07-28T09:27:39.324Z  INFO run{detection_type=legacy}: captive_portal::detections::legacy: Successfully retrieved HTTPs page
2025-07-28T09:27:39.324Z DEBUG run{detection_type=legacy}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T09:27:39.325Z DEBUG main_loop: network_info::linux::dns_manager: detected system dns manager file_owner=Unknown os_configuration_owner=File
2025-07-28T09:27:39.329Z  INFO main_loop: warp::warp_service: WARP status: Connecting(PerformingConnectivityChecks)
2025-07-28T09:27:39.329Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Connecting(PerformingConnectivityChecks)
2025-07-28T09:27:39.329Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T09:27:39.330Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Connecting(PerformingConnectivityChecks)
2025-07-28T09:27:39.330Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=1.015632ms
2025-07-28T09:27:39.331Z DEBUG connectivity_check: Resolved connectivity-check.warp-svc. to [*********, *********, ::ffff:*********, ::ffff:*********]
2025-07-28T09:27:39.351Z DEBUG do_trace_inner{host="engage.cloudflareclient.com" host_with_protocol="https://engage.cloudflareclient.com" timeout=4s sockets=[*************:443, [2606:4700:102::3]:443] con_check_meta=ConnectivityCheckMetadata { proxy_local_addr: None }}: connectivity_check: fl=471f53
h=engage.cloudflareclient.com
ip=***************
ts=1753694859.347
visit_scheme=https
uag=WARP for Linux
colo=FRA
sliver=none
http=http/1.1
loc=DE
tls=TLSv1.3
sni=plaintext
warp=off
gateway=off
rbi=off
kex=X25519

2025-07-28T09:27:39.364Z DEBUG do_trace_inner{host="connectivity.cloudflareclient.com" host_with_protocol="https://connectivity.cloudflareclient.com" timeout=4s sockets=[*************:443, [2606:4700:102::4]:443] con_check_meta=ConnectivityCheckMetadata { proxy_local_addr: None }}: connectivity_check: fl=71f790
h=connectivity.cloudflareclient.com
ip=**************
ts=1753694859.361
visit_scheme=https
uag=WARP for Linux
colo=FRA
sliver=005-tier1
http=http/1.1
loc=DE
tls=TLSv1.3
sni=plaintext
warp=off
gateway=off
rbi=off
kex=X25519

2025-07-28T09:27:39.365Z DEBUG main_loop: warp::warp::connectivity_check: Trace status: Ok(TraceResult { metal_id: "71f790", colo: "FRA", ip: ************** })
2025-07-28T09:27:39.365Z  INFO main_loop: warp::warp_service: WARP status: Connecting(ValidatingDnsConfiguration)
2025-07-28T09:27:39.365Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Connecting(ValidatingDnsConfiguration)
2025-07-28T09:27:39.365Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Connecting(ValidatingDnsConfiguration)
2025-07-28T09:27:39.365Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T09:27:39.365Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=454.003µs
2025-07-28T09:27:39.373Z DEBUG main_loop: connectivity_check: Resolved connectivity.cloudflareclient.com to [2606:4700:7::a29f:8941, 2606:4700:7::a29f:8a41, **************, **************]
2025-07-28T09:27:39.373Z DEBUG main_loop: warp::warp::warp_connection: Connect finished
2025-07-28T09:27:39.374Z DEBUG actor_statistics::handler: Recording gauge into histogram context=RecordIntoHistogramInfo { gauge_key: KeyName("conn_attempts_current"), histogram_key: KeyName("conn_attempts_all"), component: "happy_eyeballs" }
2025-07-28T09:27:39.374Z DEBUG main_loop: warp::warp::warp_connection: start_status=Ok(())
2025-07-28T09:27:39.374Z DEBUG main_loop: warp::warp::controller: self.warp future resolved
2025-07-28T09:27:39.374Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="tunnel_taskset_errors_fut"
2025-07-28T09:27:39.374Z  INFO main_loop: warp::warp_service: WARP status: Connected
2025-07-28T09:27:39.374Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="tunnel_taskset_errors_fut" elapsed=153.882µs
2025-07-28T09:27:39.374Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T09:27:39.374Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Connected
2025-07-28T09:27:39.374Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Connected
2025-07-28T09:27:39.375Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=566.087µs
2025-07-28T09:27:40.550Z  WARN warp_net::ipc::unix: Failed to get path for client error="return code = -1, errno = 13, message = 'Permission denied'" pid=72
2025-07-28T09:27:40.550Z  WARN warp_net::ipc::unix: Failed to get path for client error="return code = -1, errno = 13, message = 'Permission denied'" pid=72
2025-07-28T09:27:40.550Z  INFO warp::warp_service::ipc_loop: IPC: new connection exec_context=Regular process_name="<Unknown>" pid=72
2025-07-28T09:27:40.551Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc request: 585156eb-2f18-4529-803c-6dbaf6caa475; GetAppSettings
2025-07-28T09:27:40.551Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="handle_ipc_request"
2025-07-28T09:27:40.551Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="handle_ipc_request" elapsed=235.909µs
2025-07-28T09:27:40.552Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="handle_ipc_request"
2025-07-28T09:27:40.552Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc request: 989c6035-6c8f-421c-8d1d-a87188650c41; GetRegistrationInfo
2025-07-28T09:27:40.552Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc response: 989c6035-6c8f-421c-8d1d-a87188650c41; RegistrationInfo: Some(RegistrationInfo { id: Client(ConsumerRegistrationId(2d6c00fe-5a6c-46fb-a62a-b9aa5ed62ace)), device_id: PhysicalDeviceId(2d6c00fe-5a6c-46fb-a62a-b9aa5ed62ace), public_key: [48, 89, 48, 19, 6, 7, 42, 134, 72, 206, 61, 2, 1, 6, 8, 42, 134, 72, 206, 61, 3, 1, 7, 3, 66, 0, 4, 17, 57, 36, 239, 72, 55, 50, 137, 183, 226, 111, 249, 88, 87, 186, 139, 77, 185, 139, 82, 59, 39, 20, 30, 216, 67, 131, 75, 215, 225, 7, 133, 242, 46, 67, 26, 244, 135, 66, 154, 55, 7, 69, 151, 61, 255, 18, 209, 186, 108, 153, 209, 173, 141, 206, 121, 169, 250, 165, 240, 80, 165, 33, 101], managed: false, account: Free { id: AccountId(38d2eb15-31ea-470f-92a9-3c26b5351c42), license: "94e5DJS8-Wqh617X9-78cjuC59" }, alternate_networks: None })
2025-07-28T09:27:40.552Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="handle_ipc_request" elapsed=381.977µs
2025-07-28T09:27:40.552Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc request: 4967ea2a-e29a-47f2-b424-10b97e25aa5b; SetQlog(setting=Disabled)
2025-07-28T09:27:40.552Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="handle_ipc_request"
2025-07-28T09:27:40.552Z DEBUG run: warp_settings::manager: LayerManager update: UserOverrides(<mutator>)
2025-07-28T09:27:40.552Z DEBUG run: warp_settings::manager: UserOverrides updated: UserOverrides { version: Some(Current), always_on: Some(true), operation_mode: None, disable_for_wifi: None, disable_for_ethernet: None, disable_for_networks: None, families: None, dns_log_until: FutureSystemTime(None), gateway_id: None, onboarding: None, organization: None, split_config: None, fallback_domains: None, proxy_port: None, disable_connectivity_checks: None, override_api_endpoint: None, override_doh_endpoint: None, override_warp_endpoint: None, override_tunnel_mtu: None, qlog_log_until: FutureSystemTime(None), masque_settings: None, compliance_environment: None }
2025-07-28T09:27:40.553Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc response: 4967ea2a-e29a-47f2-b424-10b97e25aa5b; Success
2025-07-28T09:27:40.553Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="handle_ipc_request" elapsed=740.98µs
2025-07-28T09:27:40.553Z  INFO warp::warp_service::ipc_loop: IPC connection ended
2025-07-28T09:27:41.065Z DEBUG captive_portal::detections::http_redirect: 200 response for HTTP test. No captive portal detected
2025-07-28T09:27:41.065Z DEBUG run{detection_type=third-party}: captive_portal::detections::third_party: Third party detections complete detections=[("connectivitycheck.gstatic.com", Ok(NoCaptivePortalDetected)), ("captive.apple.com", Ok(NoCaptivePortalDetected)), ("www.msftconnecttest.com", Ok(NoCaptivePortalDetected))]
2025-07-28T09:27:41.065Z DEBUG run{detection_type=third-party}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T09:27:41.065Z DEBUG captive_portal: Captive portal detection completed results=[("os-detected", Ok(NoCaptivePortalDetected)), ("no-dns-redirect[cloudflareportal.com]", Ok(NoCaptivePortalDetected)), ("expected-content", Ok(NoCaptivePortalDetected)), ("http-redirect[cloudflareportal.com]", Ok(NoCaptivePortalDetected)), ("legacy", Ok(NoCaptivePortalDetected)), ("third-party", Ok(NoCaptivePortalDetected))]
2025-07-28T09:27:41.066Z DEBUG firewall::change: Firewall disallow captive portal detection
2025-07-28T09:27:41.072Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T09:27:41.073Z DEBUG firewall::captive_portal: Firewall captive portal detection expired
2025-07-28T09:27:42.688Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="main_loop_compat_rx"
2025-07-28T09:27:42.688Z DEBUG main_loop:handle_update{update=NetworkInfoChanged}: warp::warp_service::actor_compat: Handling network status change old_info=IPv4: [eth0; **********; Ethernet; Gateway: Some(**********)], DNS servers:,  127.0.0.11:53
 new_info=IPv4: [eth0; **********; Ethernet; Gateway: Some(**********)], DNS servers:,  *********:53,  *********:53

2025-07-28T09:27:42.688Z DEBUG main_loop:handle_update{update=NetworkInfoChanged}: warp::warp::proxying_dns: Reapplying DNS settings when the list of DNS servers contains ourselves new_info.dns_servers=[*********:53, *********:53] ADVERTISED_DNS_TARGETS=[*********, *********, ::ffff:*********, ::ffff:*********]
2025-07-28T09:27:42.688Z  INFO main_loop:handle_update{update=NetworkInfoChanged}: warp::warp::proxying_dns: Reinforcing old DNS settings old_info.dns_servers=[127.0.0.11:53] new_info.dns_servers=[*********:53, *********:53]
2025-07-28T09:27:42.689Z DEBUG main_loop:handle_update{update=NetworkInfoChanged}: warp::warp::dns_recovery::unix: Reverting DNS settings old_dns=RestoreDNS { action: OverwriteResolv([35, 32, 71, 101, 110, 101, 114, 97, 116, 101, 100, 32, 98, 121, 32, 68, 111, 99, 107, 101, 114, 32, 69, 110, 103, 105, 110, 101, 46, 10, 35, 32, 84, 104, 105, 115, 32, 102, 105, 108, 101, 32, 99, 97, 110, 32, 98, 101, 32, 101, 100, 105, 116, 101, 100, 59, 32, 68, 111, 99, 107, 101, 114, 32, 69, 110, 103, 105, 110, 101, 32, 119, 105, 108, 108, 32, 110, 111, 116, 32, 109, 97, 107, 101, 32, 102, 117, 114, 116, 104, 101, 114, 32, 99, 104, 97, 110, 103, 101, 115, 32, 111, 110, 99, 101, 32, 105, 116, 10, 35, 32, 104, 97, 115, 32, 98, 101, 101, 110, 32, 109, 111, 100, 105, 102, 105, 101, 100, 46, 10, 10, 110, 97, 109, 101, 115, 101, 114, 118, 101, 114, 32, 49, 50, 55, 46, 48, 46, 48, 46, 49, 49, 10, 111, 112, 116, 105, 111, 110, 115, 32, 110, 100, 111, 116, 115, 58, 48, 10, 10, 35, 32, 66, 97, 115, 101, 100, 32, 111, 110, 32, 104, 111, 115, 116, 32, 102, 105, 108, 101, 58, 32, 39, 47, 101, 116, 99, 47, 114, 101, 115, 111, 108, 118, 46, 99, 111, 110, 102, 39, 32, 40, 105, 110, 116, 101, 114, 110, 97, 108, 32, 114, 101, 115, 111, 108, 118, 101, 114, 41, 10, 35, 32, 69, 120, 116, 83, 101, 114, 118, 101, 114, 115, 58, 32, 91, 104, 111, 115, 116, 40, 52, 54, 46, 51, 56, 46, 50, 50, 53, 46, 50, 51, 48, 41, 32, 104, 111, 115, 116, 40, 52, 54, 46, 51, 56, 46, 50, 53, 50, 46, 50, 51, 48, 41, 93, 10, 35, 32, 79, 118, 101, 114, 114, 105, 100, 101, 115, 58, 32, 91, 93, 10, 35, 32, 79, 112, 116, 105, 111, 110, 32, 110, 100, 111, 116, 115, 32, 102, 114, 111, 109, 58, 32, 105, 110, 116, 101, 114, 110, 97, 108, 10]), resolv_conf_owner: Unknown, os_config_owner: File, previous_nameservers: Some([127.0.0.11]), warp_nameservers: [*********, *********, ::ffff:*********, ::ffff:*********] }
2025-07-28T09:27:42.690Z  WARN main_loop:handle_update{update=NetworkInfoChanged}: network_info::linux::dns_manager: Failed to notify system DNS manager of change. DNS may be broken if systemd-resolved is used owner=Unknown os_config_owner=File err=Failed to restart systemd-resolved

Caused by:
    org.freedesktop.DBus.Error.ServiceUnknown: The name org.freedesktop.systemd1 was not provided by any .service files
2025-07-28T09:27:42.690Z DEBUG main_loop:handle_update{update=NetworkInfoChanged}: warp::warp::dns_recovery::unix: DNS settings reverted (<resolv.conf restored>)
2025-07-28T09:27:42.691Z DEBUG main_loop:handle_update{update=NetworkInfoChanged}: warp::warp::dns_recovery::unix: Applying DNS settings [*********, *********, ::ffff:*********, ::ffff:*********]
2025-07-28T09:27:42.692Z DEBUG main_loop:handle_update{update=NetworkInfoChanged}: network_info::linux::dns_manager: detected system dns manager file_owner=Unknown os_configuration_owner=File
2025-07-28T09:27:42.710Z DEBUG main_loop:handle_update{update=NetworkInfoChanged}: warp::warp::proxying_dns: Skip notifying DNS changes as servers was empty or only contained our own advertised IPs. servers=[*********:53, *********:53] ADVERTISED_DNS_TARGETS=[*********, *********, ::ffff:*********, ::ffff:*********]
2025-07-28T09:27:42.711Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="main_loop_compat_rx" elapsed=22.339566ms
2025-07-28T09:27:45.906Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:27:48.664Z  INFO actor_emergency_disconnect::handler: Polling API for latest emergency disconnect state
2025-07-28T09:27:50.976Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:27:53.665Z DEBUG actor_root_ca::handler: Starting root-ca actor loop
2025-07-28T09:27:53.666Z DEBUG handle_registration_changed: root_ca::store: Done installing certificate(s) total=0
2025-07-28T09:27:53.666Z DEBUG handle_registration_changed: root_ca::store: Done installing certificate(s) total=0
2025-07-28T09:27:54.182Z DEBUG slog: got a response for stream_id=8, headers=[":status: 200", "cf-team: 28f1f542450000dc6e33a2c400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:28:09.182Z DEBUG slog: got a response for stream_id=12, headers=[":status: 200", "cf-team: 28f1f57cdd0000dc6e33c2f400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:28:13.312Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:28:18.880Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:28:24.182Z DEBUG slog: got a response for stream_id=16, headers=[":status: 200", "cf-team: 28f1f5b7750000dc6e33e0b400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:28:39.181Z DEBUG slog: got a response for stream_id=20, headers=[":status: 200", "cf-team: 28f1f5f20c0000dc6e33f28400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:28:52.420Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:28:54.181Z DEBUG slog: got a response for stream_id=24, headers=[":status: 200", "cf-team: 28f1f62ca40000dc6e340d5400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:28:59.332Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:29:09.181Z DEBUG slog: got a response for stream_id=28, headers=[":status: 200", "cf-team: 28f1f6673c0000dc6e34415400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:29:24.182Z DEBUG slog: got a response for stream_id=32, headers=[":status: 200", "cf-team: 28f1f6a1d40000dc6e345ca400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:29:36.708Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:29:38.663Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 23 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 1199 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 143.0, count: 84 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 195.0, count: 10 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.04763676700000001, count: 1199 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.001568502 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-07-28T09:29:38.664Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 23 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 1199 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 143.0, count: 84 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 195.0, count: 10 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.04763676700000001, count: 1199 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.001568502 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-07-28T09:29:39.182Z DEBUG slog: got a response for stream_id=36, headers=[":status: 200", "cf-team: 28f1f6dc6d0000dc6e3480d400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:29:39.246Z DEBUG tunnel_loop{protocol="masque" con_id="32a76b55bcf96cbf19dc0d297b20f87cc3db7621"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 5, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-07-28T09:29:43.360Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:29:54.181Z DEBUG slog: got a response for stream_id=40, headers=[":status: 200", "cf-team: 28f1f717040000dc6e34a07400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:30:09.183Z DEBUG slog: got a response for stream_id=44, headers=[":status: 200", "cf-team: 28f1f7519d0000dc6e34b38400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:30:22.532Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:30:24.182Z DEBUG slog: got a response for stream_id=48, headers=[":status: 200", "cf-team: 28f1f78c350000dc6e34c84400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:30:27.648Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:30:39.182Z DEBUG slog: got a response for stream_id=52, headers=[":status: 200", "cf-team: 28f1f7c6cd0000dc6e34dd2400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:30:54.182Z DEBUG slog: got a response for stream_id=56, headers=[":status: 200", "cf-team: 28f1f801650000dc6e34f92400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:31:05.536Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:31:09.182Z DEBUG slog: got a response for stream_id=60, headers=[":status: 200", "cf-team: 28f1f83bfd0000dc6e350f0400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:31:10.656Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:31:24.181Z DEBUG slog: got a response for stream_id=64, headers=[":status: 200", "cf-team: 28f1f876940000dc6e352aa400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:31:38.663Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 2399 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 53 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.09576414300000016, count: 2399 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 195.0, count: 10 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 226.0, count: 234 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.999833221 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-07-28T09:31:38.663Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 2399 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 53 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.09576414300000016, count: 2399 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 195.0, count: 10 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 226.0, count: 234 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.999833221 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-07-28T09:31:39.182Z DEBUG slog: got a response for stream_id=68, headers=[":status: 200", "cf-team: 28f1f8b12d0000dc6e35443400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:31:39.246Z DEBUG tunnel_loop{protocol="masque" con_id="32a76b55bcf96cbf19dc0d297b20f87cc3db7621"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 6, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-07-28T09:31:48.548Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:31:53.664Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:31:54.181Z DEBUG slog: got a response for stream_id=72, headers=[":status: 200", "cf-team: 28f1f8ebc40000dc6e3569a400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:32:09.181Z DEBUG slog: got a response for stream_id=76, headers=[":status: 200", "cf-team: 28f1f9265d0000dc6e3586f400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:32:24.182Z DEBUG slog: got a response for stream_id=80, headers=[":status: 200", "cf-team: 28f1f960f50000dc6e35a0a400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:32:31.556Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:32:36.672Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:32:39.182Z DEBUG slog: got a response for stream_id=84, headers=[":status: 200", "cf-team: 28f1f99b8d0000dc6e35bbc400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:32:54.182Z DEBUG slog: got a response for stream_id=88, headers=[":status: 200", "cf-team: 28f1f9d6250000dc6e35e60400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:33:09.182Z DEBUG slog: got a response for stream_id=92, headers=[":status: 200", "cf-team: 28f1fa10bd0000dc6e35fd1400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:33:14.560Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:33:19.936Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:33:24.181Z DEBUG slog: got a response for stream_id=96, headers=[":status: 200", "cf-team: 28f1fa4b540000dc6e361c0400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:33:38.664Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 78 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 3599 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 322.0, count: 388 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.14372945499999995, count: 3599 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 195.0, count: 10 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.999294545 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-07-28T09:33:38.664Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 78 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 3599 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 322.0, count: 388 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.14372945499999995, count: 3599 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 195.0, count: 10 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.999294545 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-07-28T09:33:39.181Z DEBUG slog: got a response for stream_id=100, headers=[":status: 200", "cf-team: 28f1fa85ec0000dc6e363c2400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:33:39.246Z DEBUG tunnel_loop{protocol="masque" con_id="32a76b55bcf96cbf19dc0d297b20f87cc3db7621"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 7, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-07-28T09:33:54.182Z DEBUG slog: got a response for stream_id=104, headers=[":status: 200", "cf-team: 28f1fac0850000dc6e3662a400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:33:57.568Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:34:03.456Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:34:09.182Z DEBUG slog: got a response for stream_id=108, headers=[":status: 200", "cf-team: 28f1fafb1d0000dc6e36786400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:34:24.182Z DEBUG slog: got a response for stream_id=112, headers=[":status: 200", "cf-team: 28f1fb35b50000dc6e3697b400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:34:30.336Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:34:35.456Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:34:39.181Z DEBUG slog: got a response for stream_id=116, headers=[":status: 200", "cf-team: 28f1fb704c0000dc6e36afa400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:34:54.181Z DEBUG slog: got a response for stream_id=120, headers=[":status: 200", "cf-team: 28f1fbaae40000dc6e36cd5400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:35:03.104Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:35:09.182Z DEBUG slog: got a response for stream_id=124, headers=[":status: 200", "cf-team: 28f1fbe57d0000dc6e36ef2400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:35:09.504Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:35:24.182Z DEBUG slog: got a response for stream_id=128, headers=[":status: 200", "cf-team: 28f1fc20150000dc6e3710c400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:35:35.872Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:35:38.663Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 4799 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 108 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 195.0, count: 10 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.19269850700000016, count: 4799 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 424.0, count: 526 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.000943516 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-07-28T09:35:38.663Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 4799 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 108 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 195.0, count: 10 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.19269850700000016, count: 4799 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 424.0, count: 526 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.000943516 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-07-28T09:35:39.181Z DEBUG slog: got a response for stream_id=132, headers=[":status: 200", "cf-team: 28f1fc5aad0000dc6e373a2400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:35:39.246Z DEBUG tunnel_loop{protocol="masque" con_id="32a76b55bcf96cbf19dc0d297b20f87cc3db7621"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 7, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-07-28T09:35:41.760Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:35:41.762Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:35:54.181Z DEBUG slog: got a response for stream_id=136, headers=[":status: 200", "cf-team: 28f1fc95440000dc6e37701400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:36:08.640Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:36:09.181Z DEBUG slog: got a response for stream_id=140, headers=[":status: 200", "cf-team: 28f1fccfdd0000dc6e378ab400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:36:14.272Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:36:24.182Z DEBUG slog: got a response for stream_id=144, headers=[":status: 200", "cf-team: 28f1fd0a750000dc6e37b19400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:36:39.182Z DEBUG slog: got a response for stream_id=148, headers=[":status: 200", "cf-team: 28f1fd450d0000dc6e37cfd400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:36:41.408Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:36:47.552Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:36:54.182Z DEBUG slog: got a response for stream_id=152, headers=[":status: 200", "cf-team: 28f1fd7fa50000dc6e37f50400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:37:09.182Z DEBUG slog: got a response for stream_id=156, headers=[":status: 200", "cf-team: 28f1fdba3d0000dc6e3810d400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:37:14.176Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:37:19.808Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:37:24.182Z DEBUG slog: got a response for stream_id=160, headers=[":status: 200", "cf-team: 28f1fdf4d50000dc6e382a0400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:37:38.663Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 140 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 5998 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.23745634099999988, count: 5998 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 195.0, count: 10 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 510.0, count: 666 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.993356192 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-07-28T09:37:38.664Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 140 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 5998 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.23745634099999988, count: 5998 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 195.0, count: 10 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 510.0, count: 666 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.993356192 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-07-28T09:37:39.182Z DEBUG slog: got a response for stream_id=164, headers=[":status: 200", "cf-team: 28f1fe2f6d0000dc6e384e8400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:37:39.246Z DEBUG tunnel_loop{protocol="masque" con_id="32a76b55bcf96cbf19dc0d297b20f87cc3db7621"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 8, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-07-28T09:37:46.944Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:37:52.320Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:37:54.182Z DEBUG slog: got a response for stream_id=168, headers=[":status: 200", "cf-team: 28f1fe6a050000dc6e38741400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:38:09.182Z DEBUG slog: got a response for stream_id=172, headers=[":status: 200", "cf-team: 28f1fea49d0000dc6e388c3400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:38:19.716Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:38:22.835Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:38:24.182Z DEBUG slog: got a response for stream_id=176, headers=[":status: 200", "cf-team: 28f1fedf340000dc6e38a04400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:38:24.836Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:38:28.672Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:38:39.181Z DEBUG slog: got a response for stream_id=180, headers=[":status: 200", "cf-team: 28f1ff19cd0000dc6e38c1b400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:38:52.480Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:38:54.181Z DEBUG slog: got a response for stream_id=184, headers=[":status: 200", "cf-team: 28f1ff54640000dc6e38dbc400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:38:59.392Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:39:09.182Z DEBUG slog: got a response for stream_id=188, headers=[":status: 200", "cf-team: 28f1ff8efd0000dc6e38f9b400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:39:24.182Z DEBUG slog: got a response for stream_id=192, headers=[":status: 200", "cf-team: 28f1ffc9950000dc6e39140400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:39:38.663Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 7198 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 166 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.2864482720000003, count: 7198 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 596.0, count: 758 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 195.0, count: 10 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.992654797 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-07-28T09:39:38.663Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 7198 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 166 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.2864482720000003, count: 7198 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 596.0, count: 758 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 195.0, count: 10 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.992654797 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-07-28T09:39:39.182Z DEBUG slog: got a response for stream_id=196, headers=[":status: 200", "cf-team: 28f200042d0000dc6e3933b400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:39:39.247Z DEBUG tunnel_loop{protocol="masque" con_id="32a76b55bcf96cbf19dc0d297b20f87cc3db7621"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 8, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-07-28T09:39:45.728Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:39:52.128Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:39:54.182Z DEBUG slog: got a response for stream_id=200, headers=[":status: 200", "cf-team: 28f2003ec50000dc6e3953a400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:40:09.182Z DEBUG slog: got a response for stream_id=204, headers=[":status: 200", "cf-team: 28f200795d0000dc6e3970a400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
