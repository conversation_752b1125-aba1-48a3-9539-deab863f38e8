2025-07-28T09:47:58.036Z  INFO warp::warp_service: Starting WarpService pid=20
2025-07-28T09:47:58.036Z  INFO warp::warp_service: Version: 2025.5.943.0
2025-07-28T09:47:58.044Z DEBUG warp_settings::raw_settings: Loading settings from file "/var/lib/cloudflare-warp/settings.json"
2025-07-28T09:47:58.044Z  INFO warp_settings::manager: User preferences not loaded e=No such file or directory (os error 2)
2025-07-28T09:47:58.044Z DEBUG warp_settings::raw_settings: Loading consumer settings from file "/var/lib/cloudflare-warp/consumer-settings.json"
2025-07-28T09:47:58.044Z  INFO warp_settings::manager: Consumer preferences not loaded e=No such file or directory (os error 2)
2025-07-28T09:47:58.044Z DEBUG warp_settings::manager::final_overrides: Loading final overrides settings from file "/var/lib/cloudflare-warp/final-overrides-settings.json"
2025-07-28T09:47:58.044Z  INFO warp_settings::manager: Final override settings not loaded e=No such file or directory (os error 2)
2025-07-28T09:47:58.045Z DEBUG warp_settings::manager: Starting local policy file watch parent_path="/var/lib/cloudflare-warp"
2025-07-28T09:47:58.115Z DEBUG warp_api_client::addresses: Initializing base API endpoint settings consumer=ConsumerEndpointConfig { ips: [*************, ************, 2606:4700::6810:1854, 2606:4700::6810:c052], api_sni: ApiSni("api.cloudflareclient.com.") } zt=ZeroTrustEndpointConfig { ips: [***************, ***************, 2606:4700:7::a29f:8969, 2606:4700:7::a29f:8a69], api_sni: ApiSni("zero-trust-client.cloudflareclient.com."), notifications_sni: NotificationsSni("notifications.cloudflareclient.com") } compliance_environment=Normal
2025-07-28T09:47:58.116Z  WARN network_info::linux::dns_manager::dns_owner: Could not determine resolv.conf file owner: No comment line contained an owner slug
2025-07-28T09:47:58.123Z DEBUG warp::warp_service: Main loop refactor feature enabled
2025-07-28T09:47:58.123Z DEBUG watchdog: warp::watchdog: Kicking off watchdog
2025-07-28T09:47:58.123Z DEBUG actor_watchdog: Initializing watchdog actor
2025-07-28T09:47:58.124Z DEBUG actor_alternate_network: Initializing alternate network actor
2025-07-28T09:47:58.124Z DEBUG actor_main_loop_compat: Initializing main loop compat actor
2025-07-28T09:47:58.124Z DEBUG actor_statistics: Initializing statistics actor
2025-07-28T09:47:58.124Z DEBUG actor_connectivity: Initializing connectivity actor
2025-07-28T09:47:58.124Z DEBUG actor_remote_dex_command: Initializing remote DEX command actor
2025-07-28T09:47:58.125Z DEBUG actor_device_state: Initializing device state actor
2025-07-28T09:47:58.125Z DEBUG actor_emergency_disconnect: Initializing emergency disconnect actor
2025-07-28T09:47:58.125Z DEBUG actor_registration_verifier: Initializing registration verifier actor
2025-07-28T09:47:58.125Z DEBUG actor_root_ca: Initializing root-ca actor
2025-07-28T09:47:58.125Z DEBUG actor_root_ca: Sleeping for 15s to allow main loop boot-up
2025-07-28T09:47:58.126Z  INFO main_loop: warp_net::ipc: Bound ipc socket name="cloudflare-warp/warp_service" path="/run/cloudflare-warp/warp_service"
2025-07-28T09:47:58.126Z  INFO main_loop: warp::warp_service: Warp IPC listening on "cloudflare-warp/warp_service"
2025-07-28T09:47:58.127Z  INFO firewall::engine: Firewall engine running
2025-07-28T09:47:58.130Z  WARN main_loop: warp::warp_service: Failed to load Registration error=OsError(Os { code: 2, kind: NotFound, message: "No such file or directory" })
2025-07-28T09:47:58.130Z DEBUG network_info::linux::power_notifier: Initializing dbus connection
2025-07-28T09:47:58.130Z DEBUG warp_api_client::client::ops: Sending API request GET api.cloudflareclient.com./v0/client_config
2025-07-28T09:47:58.131Z DEBUG warp_api_client::client::ops: Sending API request GET zero-trust-client.cloudflareclient.com./v0/client_config
2025-07-28T09:47:58.189Z DEBUG run: warp_settings::manager: LayerManager update: NetworkDefaults(NetworkDefaults { split_config: Some(Exclude { ips: [(10.0.0.0/8, None), (**********/10, None), (***********/16, None), (**********/12, None), (*********/24, None), (***********/16, None), (*********/24, None), (240.0.0.0/4, None), (***************/32, None), (***************/32, None), (fe80::/10, None), (fd00::/8, None), (ff01::/16, None), (ff02::/16, None), (ff03::/16, None), (ff04::/16, None), (ff05::/16, None), (fc00::/7, None), (**********/16, None), (**********/16, None), (***********/22, None), (************/18, None), (***********/23, None), (2620:149:a44::/48, None), (2403:300:a42::/48, None), (2403:300:a51::/48, None), (2a01:b740:a42::/48, None)], hosts: [] }) })
2025-07-28T09:47:58.189Z DEBUG main_loop: warp::warp_service: Checking for registration vs settings account mismatch reg_is_teams=false settings_is_teams=false reg_org=None settings_org=None
2025-07-28T09:47:58.189Z  INFO firewall::engine: Firewall unloaded
2025-07-28T09:47:58.189Z  INFO main_loop: warp::warp_service: New User Settings
2025-07-28T09:47:58.190Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC update: SettingsUpdated
2025-07-28T09:47:58.190Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseUpdate: SettingsUpdated
2025-07-28T09:47:58.190Z DEBUG main_loop: warp::warp_service: update_settings: no restart required
2025-07-28T09:47:58.190Z  INFO main_loop: warp::warp_service: WARP status: Unable(RegistrationMissing(ManualDeletion))
2025-07-28T09:47:58.190Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Unable(RegistrationMissing(ManualDeletion))
2025-07-28T09:47:58.190Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Unable(RegistrationMissing(ManualDeletion))
2025-07-28T09:47:58.190Z DEBUG main_loop: warp::warp_service: Checking for registration vs settings account mismatch reg_is_teams=false settings_is_teams=false reg_org=None settings_org=None
2025-07-28T09:47:58.190Z  INFO warp::warp_service: Spawning registration changed IPC loop
2025-07-28T09:47:58.190Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T09:47:58.190Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=148.051µs
2025-07-28T09:47:58.190Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="settings_changed"
2025-07-28T09:47:58.190Z  INFO firewall::engine: Firewall unloaded
2025-07-28T09:47:58.190Z  INFO main_loop: warp::warp_service: User Settings Changed: {}
2025-07-28T09:47:58.190Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC update: SettingsUpdated
2025-07-28T09:47:58.190Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseUpdate: SettingsUpdated
2025-07-28T09:47:58.190Z DEBUG main_loop: warp::warp_service: update_settings: no restart required
2025-07-28T09:47:58.190Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="settings_changed" elapsed=566.226µs
2025-07-28T09:48:00.009Z  WARN warp_net::ipc::unix: Failed to get path for client error="return code = -1, errno = 13, message = 'Permission denied'" pid=50
2025-07-28T09:48:00.009Z  WARN warp_net::ipc::unix: Failed to get path for client error="return code = -1, errno = 13, message = 'Permission denied'" pid=50
2025-07-28T09:48:00.009Z  INFO warp::warp_service::ipc_loop: IPC: new connection exec_context=Regular process_name="<Unknown>" pid=50
2025-07-28T09:48:00.010Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc request: de0aff50-9df6-4401-b8d8-37636f9dc5a7; GetAppSettings
2025-07-28T09:48:00.010Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="handle_ipc_request"
2025-07-28T09:48:00.010Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="handle_ipc_request" elapsed=355.927µs
2025-07-28T09:48:00.011Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc request: af5290be-ffa7-4d85-a19d-32473546c9d0; GetRegistrationInfo
2025-07-28T09:48:00.011Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="handle_ipc_request"
2025-07-28T09:48:00.011Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc response: af5290be-ffa7-4d85-a19d-32473546c9d0; RegistrationInfo: None
2025-07-28T09:48:00.011Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="handle_ipc_request" elapsed=75.343µs
2025-07-28T09:48:00.011Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc request: 3be6be48-dbeb-4634-9c29-5927bddd33a2; NewRegistration
2025-07-28T09:48:00.011Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="handle_ipc_request"
2025-07-28T09:48:00.011Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC update: RegistrationInProgress
2025-07-28T09:48:00.011Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseUpdate: RegistrationInProgress
2025-07-28T09:48:00.012Z DEBUG warp::warp_service::ipc_handlers: Sending IPC update: ConfigurationUpdated
2025-07-28T09:48:00.012Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="handle_ipc_request" elapsed=342.152µs
2025-07-28T09:48:00.012Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "root-ca.updates_recv", labels: {}, count: 0 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "device-state.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "remote-dex-command.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 2 }, CounterPayload { name: "watchdog.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "root-ca.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "main-loop-compat.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "statistics.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "root-ca.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "connectivity.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "alternate-network.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "connectivity.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "statistics.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "emergency-disconnect.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 0 }, CounterPayload { name: "remote-dex-command.updates_recv", labels: {}, count: 0 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "statistics.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 0 }, CounterPayload { name: "remote-dex-command.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "watchdog.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "emergency-disconnect.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "emergency-disconnect.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "registration-verifier.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "alternate-network.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "connectivity.updates_recv", labels: {}, count: 0 }, CounterPayload { name: "connectivity.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "remote-dex-command.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "device-state.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "emergency-disconnect.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "main-loop-compat.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "registration-verifier.updates_recv", labels: {}, count: 0 }, CounterPayload { name: "message_bus.bus_driver_request_timeouts", labels: {}, count: 0 }, CounterPayload { name: "watchdog.updates_recv", labels: {}, count: 0 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 19 }, CounterPayload { name: "root-ca.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "alternate-network.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "registration-verifier.requests_recv", labels: {}, count: 0 }], summaries: [SummaryPayload { name: "root-ca.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "emergency-disconnect.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "remote-dex-command.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "connectivity.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "alternate-network.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "registration-verifier.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "main-loop-compat.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "device-state.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "statistics.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.003663194, count: 19 }, SummaryPayload { name: "watchdog.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 0.000272267 }, GaugesPayload { name: "message_bus.bus_driver_current_actors", labels: {}, value: 10.0 }] } context="registration change"}: actor_statistics::handler: Starting to upload stats
2025-07-28T09:48:00.012Z DEBUG warp::warp_service::ipc_handlers: Ipc Broadcast ResponseUpdate: ConfigurationUpdated
2025-07-28T09:48:00.012Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "root-ca.updates_recv", labels: {}, count: 0 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "device-state.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "remote-dex-command.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 2 }, CounterPayload { name: "watchdog.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "root-ca.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "main-loop-compat.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "statistics.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "root-ca.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "connectivity.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "alternate-network.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "connectivity.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "statistics.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "emergency-disconnect.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 0 }, CounterPayload { name: "remote-dex-command.updates_recv", labels: {}, count: 0 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "statistics.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 0 }, CounterPayload { name: "remote-dex-command.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "watchdog.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "emergency-disconnect.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "emergency-disconnect.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "registration-verifier.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "alternate-network.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "connectivity.updates_recv", labels: {}, count: 0 }, CounterPayload { name: "connectivity.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "remote-dex-command.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "device-state.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "emergency-disconnect.requests_recv", labels: {}, count: 0 }, CounterPayload { name: "main-loop-compat.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "registration-verifier.updates_recv", labels: {}, count: 0 }, CounterPayload { name: "message_bus.bus_driver_request_timeouts", labels: {}, count: 0 }, CounterPayload { name: "watchdog.updates_recv", labels: {}, count: 0 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 19 }, CounterPayload { name: "root-ca.commands_recv", labels: {}, count: 0 }, CounterPayload { name: "alternate-network.reinitializations", labels: {}, count: 0 }, CounterPayload { name: "registration-verifier.requests_recv", labels: {}, count: 0 }], summaries: [SummaryPayload { name: "root-ca.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "emergency-disconnect.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "remote-dex-command.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "connectivity.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "alternate-network.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "registration-verifier.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "main-loop-compat.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "device-state.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "statistics.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.003663194, count: 19 }, SummaryPayload { name: "watchdog.lifetime_sec_nanosec", labels: {}, sum: -0.0, count: 0 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 0.000272267 }, GaugesPayload { name: "message_bus.bus_driver_current_actors", labels: {}, value: 10.0 }] } context="registration change"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-07-28T09:48:00.012Z  INFO actor_statistics::handler: Switched to new registration for stats. All stats have been reset. new_registration_id=None
2025-07-28T09:48:00.012Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="registration_change"
2025-07-28T09:48:00.012Z  INFO main_loop:handle_update{update=AlternateNetworkChanged(None)}: warp::warp_service::actor_compat: Detected alternate network name=None
2025-07-28T09:48:00.013Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="registration_change" elapsed=998.981µs
2025-07-28T09:48:00.013Z ERROR utilities::device_info::linux: Failed to load SMBiosData error=Os { code: 2, kind: NotFound, message: "No such file or directory" }
2025-07-28T09:48:00.013Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="main_loop_compat_rx"
2025-07-28T09:48:00.013Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="main_loop_compat_rx" elapsed=118.495µs
2025-07-28T09:48:00.013Z DEBUG warp_api_client::apis::registration: New registration auth_method=Consumer
2025-07-28T09:48:00.013Z DEBUG warp_api_client::client: Sending API request POST https://api.cloudflareclient.com.//v0/reg
2025-07-28T09:48:01.118Z DEBUG warp_api_endpoints::payloads: Warp endpoint override ports ports=[2408, 500, 1701, 4500]
2025-07-28T09:48:01.119Z DEBUG main_loop: warp_api_client::executor: Returning API response for NewRegistration request_id=3be6be48-dbeb-4634-9c29-5927bddd33a2
2025-07-28T09:48:01.119Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="api_responses"
2025-07-28T09:48:01.119Z DEBUG run: warp_settings::manager: LayerManager update: NetworkPolicy(Some(NetworkPolicySettings { onboarding: None, operation_mode: None, disable_auto_fallback: None, fallback_domains: None, proxy_port: None, split_config: None, gateway_id: None, support_url: None, allow_mode_switch: None, switch_locked: None, auto_connect: None, captive_portal: None, organization: None, allow_updates: None, allowed_to_leave: None, profile_id: None, lan_allow_settings: Some(LanAllowSettings { minutes: None, subnet_size: SubnetSize(24) }), warp_tunnel_protocol: Some(Masque), masque_settings: None, register_interface_ip_with_dns: None, anycast_split_ips: Some(AnycastSplitIps { always_exclude: [*************, 2606:4700:102::3], always_include: [*************, 2606:4700:102::4] }), firewall_scope: None, sccm_vpn_boundary_support: None, enable_post_quantum: None }))
2025-07-28T09:48:01.119Z DEBUG main_loop: warp::warp_service::api_handlers: Registration updated current_registration=NewInProgress disposition=UpdateOnly
2025-07-28T09:48:01.119Z DEBUG main_loop: warp::warp_service::api_handlers: Invalidating existing posture check scheduler on registration update
2025-07-28T09:48:01.119Z DEBUG main_loop: warp::warp_service::api_handlers: Registration updated 0.00 hours old, refresh at 2025-07-29 9:48:01.118683783 +00:00:00
Registration: ecd6f5b1-04e9-4394-92f6-015127a8c2e8
Device Identifiers: System User
Auth Method: Consumer
Public key: bc5dbfdca1ffa36675809072391d372689cd33b1a06c00cd33a1c29227e49552
Interface: WarpEndpoint { v4: **********, v6: 2606:4700:110:868e:49c:c89e:1283:86e }
Endpoints: [WarpSocketAddrPair { v4: *************:2408, v6: [2606:4700:d0::a29f:c005]:2408 }, WarpSocketAddrPair { v4: *************:500, v6: [2606:4700:d0::a29f:c005]:500 }, WarpSocketAddrPair { v4: *************:1701, v6: [2606:4700:d0::a29f:c005]:1701 }, WarpSocketAddrPair { v4: *************:4500, v6: [2606:4700:d0::a29f:c005]:4500 }]
Subnet CIDRS: None

Account: Free { id: AccountId(5ab76f72-c06d-4187-893e-a6d6f0d71cba), license: "Ub56i2C0-S51U7Bi4-580Sk7Iq" }

2025-07-28T09:48:01.119Z  INFO main_loop: warp_storage::registration::registration_storage: Saving registration to active cache config_name=None username="WARPSecret"
2025-07-28T09:48:01.120Z DEBUG main_loop: warp::warp::controller: Stopping WarpConnection state=Disconnected
2025-07-28T09:48:01.120Z DEBUG warp::warp_service::ipc_handlers: Sending IPC update: ConfigurationUpdated
2025-07-28T09:48:01.120Z  INFO firewall::engine: Firewall unloaded
2025-07-28T09:48:01.120Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 11 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "emergency-disconnect.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 2 }, CounterPayload { name: "statistics.updates_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.00043767000000000003, count: 11 }], gauges: [] } context="registration change"}: actor_statistics::handler: Starting to upload stats
2025-07-28T09:48:01.121Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 11 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "emergency-disconnect.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 2 }, CounterPayload { name: "statistics.updates_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.00043767000000000003, count: 11 }], gauges: [] } context="registration change"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-07-28T09:48:01.121Z  INFO actor_statistics::handler: Switched to new registration for stats. All stats have been reset. new_registration_id=None
2025-07-28T09:48:01.121Z DEBUG warp::warp_service::ipc_handlers: Ipc Broadcast ResponseUpdate: ConfigurationUpdated
2025-07-28T09:48:01.121Z DEBUG main_loop: warp::warp_service: Determining disconnected reason from connectivity state net_info=IPv4: [eth0; **********; Ethernet; Gateway: Some(**********)], DNS servers:,  127.0.0.11:53
 power_state=None disconnect_reason=None
2025-07-28T09:48:01.121Z  WARN main_loop: warp::warp_service: Disconnecting, but reason is unknown
2025-07-28T09:48:01.121Z DEBUG main_loop: warp::warp_service: Checking for registration vs settings account mismatch reg_is_teams=false settings_is_teams=false reg_org=None settings_org=None
2025-07-28T09:48:01.121Z  INFO firewall::engine: Firewall unloaded
2025-07-28T09:48:01.122Z  INFO main_loop: warp::warp_service: User Settings Changed: {warp_tunnel_protocol: "wireguard"->"masque", anycast_split_ips.always_exclude: Added: [{"ip":"*************"},{"ip":"2606:4700:102::3"}] , anycast_split_ips.always_include: Added: [{"ip":"*************"},{"ip":"2606:4700:102::4"}] , }
2025-07-28T09:48:01.122Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC update: SettingsUpdated
2025-07-28T09:48:01.122Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseUpdate: SettingsUpdated
2025-07-28T09:48:01.122Z  INFO main_loop: warp::warp_service: New user settings modify the tunnel protocol, rotating keys new_settings.warp_tunnel_protocol=Masque new_key_data=RegistrationTunnelKeys { key_type: NistP256, tunnel_type: Masque }
2025-07-28T09:48:01.122Z DEBUG main_loop: warp_api_client::executor: Spawning API request for RotateKeys request_id=3b132640-7569-4b11-8711-aee60e027cbd
2025-07-28T09:48:01.122Z DEBUG main_loop: warp::warp_service: Determining disconnected reason from connectivity state net_info=IPv4: [eth0; **********; Ethernet; Gateway: Some(**********)], DNS servers:,  127.0.0.11:53
 power_state=None disconnect_reason=Some(SettingsChanged)
2025-07-28T09:48:01.122Z  INFO main_loop: warp::warp_service: Disconnecting due to settings change
2025-07-28T09:48:01.122Z  INFO main_loop: warp::warp_service: Disconnecting after settings change disconnected_reason=Disconnected(SettingsChanged)
2025-07-28T09:48:01.122Z DEBUG main_loop: warp::warp::controller: Stopping WarpConnection state=Disconnected
2025-07-28T09:48:01.122Z DEBUG warp_api_client::client: Sending API request PATCH https://api.cloudflareclient.com.//v0/reg/ecd6f5b1-04e9-4394-92f6-015127a8c2e8
2025-07-28T09:48:01.123Z  INFO firewall::engine: Firewall unloaded
2025-07-28T09:48:01.123Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc response: 3be6be48-dbeb-4634-9c29-5927bddd33a2; Success
2025-07-28T09:48:01.123Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="api_responses" elapsed=4.248225ms
2025-07-28T09:48:01.123Z  INFO main_loop: warp::warp_service: WARP status: Disconnected(SettingsChanged)
2025-07-28T09:48:01.123Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T09:48:01.123Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Disconnected(SettingsChanged)
2025-07-28T09:48:01.123Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Disconnected(SettingsChanged)
2025-07-28T09:48:01.123Z  INFO warp::warp_service::ipc_loop: IPC connection ended
2025-07-28T09:48:01.123Z DEBUG main_loop: warp::warp_service: Checking for registration vs settings account mismatch reg_is_teams=false settings_is_teams=false reg_org=None settings_org=None
2025-07-28T09:48:01.123Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=461.197µs
2025-07-28T09:48:01.123Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="settings_changed"
2025-07-28T09:48:01.123Z  INFO firewall::engine: Firewall unloaded
2025-07-28T09:48:01.124Z  INFO main_loop: warp::warp_service: User Settings Changed: {}
2025-07-28T09:48:01.124Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC update: SettingsUpdated
2025-07-28T09:48:01.124Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseUpdate: SettingsUpdated
2025-07-28T09:48:01.124Z  INFO main_loop: warp::warp_service: Settings and Registration determined that a key rotation was needed but there exists a pending API call that satisfies this constraint. Skipping for now.
2025-07-28T09:48:01.125Z DEBUG main_loop: warp::warp_service: update_settings: no restart required
2025-07-28T09:48:01.125Z DEBUG main_loop: warp::warp_service: Determining disconnected reason from connectivity state net_info=IPv4: [eth0; **********; Ethernet; Gateway: Some(**********)], DNS servers:,  127.0.0.11:53
 power_state=None disconnect_reason=None
2025-07-28T09:48:01.125Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="settings_changed" elapsed=1.71326ms
2025-07-28T09:48:01.125Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="registration_change"
2025-07-28T09:48:01.125Z  WARN main_loop: warp::warp_service: Disconnecting, but reason is unknown
2025-07-28T09:48:01.126Z  INFO main_loop: warp::warp_service: WARP status: Disconnected(Manual)
2025-07-28T09:48:01.126Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="registration_change" elapsed=644.416µs
2025-07-28T09:48:01.126Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Disconnected(Manual)
2025-07-28T09:48:01.126Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Disconnected(Manual)
2025-07-28T09:48:01.126Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T09:48:01.126Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=448.634µs
2025-07-28T09:48:01.131Z  WARN warp_net::ipc::unix: Failed to get path for client error="return code = -1, errno = 13, message = 'Permission denied'" pid=51
2025-07-28T09:48:01.131Z  WARN warp_net::ipc::unix: Failed to get path for client error="return code = -1, errno = 13, message = 'Permission denied'" pid=51
2025-07-28T09:48:01.131Z  INFO warp::warp_service::ipc_loop: IPC: new connection exec_context=Regular process_name="<Unknown>" pid=51
2025-07-28T09:48:01.132Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc request: 832e1105-df0a-456d-80ec-ffd74064fc9f; GetAppSettings
2025-07-28T09:48:01.132Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="handle_ipc_request"
2025-07-28T09:48:01.132Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="handle_ipc_request" elapsed=311.383µs
2025-07-28T09:48:01.133Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc request: 46c3e28d-1a66-4ac4-99ee-c87d1de42197; GetRegistrationInfo
2025-07-28T09:48:01.133Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="handle_ipc_request"
2025-07-28T09:48:01.133Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc response: 46c3e28d-1a66-4ac4-99ee-c87d1de42197; RegistrationInfo: Some(RegistrationInfo { id: Client(ConsumerRegistrationId(ecd6f5b1-04e9-4394-92f6-015127a8c2e8)), device_id: PhysicalDeviceId(ecd6f5b1-04e9-4394-92f6-015127a8c2e8), public_key: [188, 93, 191, 220, 161, 255, 163, 102, 117, 128, 144, 114, 57, 29, 55, 38, 137, 205, 51, 177, 160, 108, 0, 205, 51, 161, 194, 146, 39, 228, 149, 82], managed: false, account: Free { id: AccountId(5ab76f72-c06d-4187-893e-a6d6f0d71cba), license: "Ub56i2C0-S51U7Bi4-580Sk7Iq" }, alternate_networks: None })
2025-07-28T09:48:01.133Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="handle_ipc_request" elapsed=466.388µs
2025-07-28T09:48:01.133Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="handle_ipc_request"
2025-07-28T09:48:01.133Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc request: 6d023c19-c442-487e-9651-6bb0a7a64470; SetAlwaysOn(true)
2025-07-28T09:48:01.134Z  INFO main_loop: structlog: event="UserToggledV0" toggle="on" warp_colo="none" warp_metal="none" account_name="" account_id="5ab76f72c06d4187893ea6d6f0d71cba" hostname="a2bf1074869f" serial_number=""
2025-07-28T09:48:01.135Z DEBUG run: warp_settings::manager: LayerManager update: UserOverrides(<mutator>)
2025-07-28T09:48:01.135Z DEBUG run: warp_settings::manager: UserOverrides updated: UserOverrides { version: None, always_on: Some(true), operation_mode: None, disable_for_wifi: None, disable_for_ethernet: None, disable_for_networks: None, families: None, dns_log_until: FutureSystemTime(None), gateway_id: None, onboarding: None, organization: None, split_config: None, fallback_domains: None, proxy_port: None, disable_connectivity_checks: None, override_api_endpoint: None, override_doh_endpoint: None, override_warp_endpoint: None, override_tunnel_mtu: None, qlog_log_until: FutureSystemTime(None), masque_settings: None, compliance_environment: None }
2025-07-28T09:48:01.135Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc response: 6d023c19-c442-487e-9651-6bb0a7a64470; Success
2025-07-28T09:48:01.135Z DEBUG main_loop: warp::warp_service: Checking for registration vs settings account mismatch reg_is_teams=false settings_is_teams=false reg_org=None settings_org=None
2025-07-28T09:48:01.135Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="handle_ipc_request" elapsed=2.271592ms
2025-07-28T09:48:01.136Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="settings_changed"
2025-07-28T09:48:01.136Z DEBUG firewall::change: Firewall allow API ips api_ips=[*************, ************, 2606:4700::6810:1854, 2606:4700::6810:c052]
2025-07-28T09:48:01.136Z DEBUG firewall::change: Changing catch all policy Deny->Deny
2025-07-28T09:48:01.136Z DEBUG firewall::change: Firewall allow managed network endpoints managed_network_endpoints=[]
2025-07-28T09:48:01.136Z DEBUG firewall::change: Firewall allow anycast ips: [*************, 2606:4700:102::3]
2025-07-28T09:48:01.136Z  INFO firewall::engine: Firewall starting
2025-07-28T09:48:01.136Z  INFO warp::warp_service::ipc_loop: IPC connection ended
2025-07-28T09:48:01.143Z  WARN warp_net::ipc::unix: Failed to get path for client error="return code = -1, errno = 13, message = 'Permission denied'" pid=59
2025-07-28T09:48:01.143Z  WARN warp_net::ipc::unix: Failed to get path for client error="return code = -1, errno = 13, message = 'Permission denied'" pid=59
2025-07-28T09:48:01.143Z  INFO warp::warp_service::ipc_loop: IPC: new connection exec_context=Regular process_name="<Unknown>" pid=59
2025-07-28T09:48:01.144Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T09:48:01.144Z  INFO main_loop: warp::warp_service: User Settings Changed: {always_on: false->true, }
2025-07-28T09:48:01.144Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC update: SettingsUpdated
2025-07-28T09:48:01.144Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseUpdate: SettingsUpdated
2025-07-28T09:48:01.144Z  INFO main_loop: warp::warp_service: Settings and Registration determined that a key rotation was needed but there exists a pending API call that satisfies this constraint. Skipping for now.
2025-07-28T09:48:01.144Z DEBUG main_loop: warp::warp_service: update_settings: no restart required
2025-07-28T09:48:01.145Z DEBUG main_loop: warp::warp::controller: Stopping WarpConnection state=Disconnected
2025-07-28T09:48:01.145Z DEBUG firewall::change: Firewall allow API ips api_ips=[*************, ************, 2606:4700::6810:1854, 2606:4700::6810:c052]
2025-07-28T09:48:01.145Z DEBUG firewall::change: Changing catch all policy Deny->Deny
2025-07-28T09:48:01.145Z DEBUG firewall::change: Firewall allow managed network endpoints managed_network_endpoints=[]
2025-07-28T09:48:01.145Z DEBUG firewall::change: Firewall allow anycast ips: [*************, 2606:4700:102::3]
2025-07-28T09:48:01.189Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T09:48:01.189Z DEBUG main_loop: warp::warp_service: Determining disconnected reason from connectivity state net_info=IPv4: [eth0; **********; Ethernet; Gateway: Some(**********)], DNS servers:,  127.0.0.11:53
 power_state=None disconnect_reason=None
2025-07-28T09:48:01.189Z  WARN main_loop: warp::warp_service: Disconnecting, but reason is unknown
2025-07-28T09:48:01.190Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc request: 63d49f41-483f-4768-b4ae-db747f1e1657; GetAppSettings
2025-07-28T09:48:01.190Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="settings_changed" elapsed=54.093154ms
2025-07-28T09:48:01.190Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="handle_ipc_request"
2025-07-28T09:48:01.190Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="handle_ipc_request" elapsed=765.517µs
2025-07-28T09:48:01.190Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="main_loop_compat_rx"
2025-07-28T09:48:01.191Z DEBUG main_loop:handle_command: warp::warp::controller: Stopping WarpConnection state=Disconnected
2025-07-28T09:48:01.192Z DEBUG firewall::change: Firewall allow API ips api_ips=[*************, ************, 2606:4700::6810:1854, 2606:4700::6810:c052]
2025-07-28T09:48:01.192Z DEBUG firewall::change: Changing catch all policy Deny->Deny
2025-07-28T09:48:01.192Z DEBUG firewall::change: Firewall allow managed network endpoints managed_network_endpoints=[]
2025-07-28T09:48:01.192Z DEBUG firewall::change: Firewall allow anycast ips: [*************, 2606:4700:102::3]
2025-07-28T09:48:01.199Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T09:48:01.199Z  INFO main_loop:handle_command: warp::warp_service: captive_portal_fw_until: Indefinitely
2025-07-28T09:48:01.199Z DEBUG main_loop:handle_command: warp::warp::proxying_dns: Using auto fallback: true
2025-07-28T09:48:01.199Z  WARN main_loop:handle_command: warp::warp::tunnel: Settings and Registration mismatch on tunnel protocol. Falling back to registration protocol. settings_proto=Masque reg_proto=Wireguard
2025-07-28T09:48:01.199Z DEBUG main_loop:handle_command: warp::warp::controller: Setting WarpConnection current_state=Disconnected
2025-07-28T09:48:01.199Z DEBUG main_loop:handle_command: warp::warp::controller: Stopping WarpConnection state=Disconnected
2025-07-28T09:48:01.199Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="main_loop_compat_rx" elapsed=9.230685ms
2025-07-28T09:48:01.199Z DEBUG main_loop: warp::warp::warp_connection: Current Network: IPv4: [eth0; **********; Ethernet; Gateway: Some(**********)], DNS servers:,  127.0.0.11:53

2025-07-28T09:48:01.199Z  INFO main_loop: warp::warp::warp_connection: Starting Warp Connection operation_mode=Warp dns_mode=DNS Proxy tunnel_mode=Exclude-only Tunnel posture_only=disabled forward_proxy=disabled
2025-07-28T09:48:01.200Z  INFO main_loop: warp::warp::tunnel: Initiate WARP connection protocol=Wireguard self.masque_settings=MasqueProtocolSettings { http_version: H3WithH2Fallback }
2025-07-28T09:48:01.200Z  INFO main_loop: warp::warp_service: WARP status: Connecting(CheckingNetwork)
2025-07-28T09:48:01.200Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Connecting(CheckingNetwork)
2025-07-28T09:48:01.200Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Connecting(CheckingNetwork)
2025-07-28T09:48:01.200Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc request: e38b0570-4548-43be-b47b-d472259b78e6; GetRegistrationInfo
2025-07-28T09:48:01.200Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc response: e38b0570-4548-43be-b47b-d472259b78e6; RegistrationInfo: Some(RegistrationInfo { id: Client(ConsumerRegistrationId(ecd6f5b1-04e9-4394-92f6-015127a8c2e8)), device_id: PhysicalDeviceId(ecd6f5b1-04e9-4394-92f6-015127a8c2e8), public_key: [188, 93, 191, 220, 161, 255, 163, 102, 117, 128, 144, 114, 57, 29, 55, 38, 137, 205, 51, 177, 160, 108, 0, 205, 51, 161, 194, 146, 39, 228, 149, 82], managed: false, account: Free { id: AccountId(5ab76f72-c06d-4187-893e-a6d6f0d71cba), license: "Ub56i2C0-S51U7Bi4-580Sk7Iq" }, alternate_networks: None })
2025-07-28T09:48:01.200Z DEBUG firewall::change: Firewall allow tunnel UDP ipv4=[*************] ipv6=[2606:4700:d0::a29f:c005]
2025-07-28T09:48:01.200Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T09:48:01.200Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=433.073µs
2025-07-28T09:48:01.200Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="handle_ipc_request"
2025-07-28T09:48:01.200Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="handle_ipc_request" elapsed=411.623µs
2025-07-28T09:48:01.201Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc request: bdd4047e-d188-4ed1-98d3-a56802053986; SetQlog(setting=Disabled)
2025-07-28T09:48:01.201Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="handle_ipc_request"
2025-07-28T09:48:01.201Z DEBUG run: warp_settings::manager: LayerManager update: UserOverrides(<mutator>)
2025-07-28T09:48:01.201Z DEBUG run: warp_settings::manager: UserOverrides updated: UserOverrides { version: None, always_on: Some(true), operation_mode: None, disable_for_wifi: None, disable_for_ethernet: None, disable_for_networks: None, families: None, dns_log_until: FutureSystemTime(None), gateway_id: None, onboarding: None, organization: None, split_config: None, fallback_domains: None, proxy_port: None, disable_connectivity_checks: None, override_api_endpoint: None, override_doh_endpoint: None, override_warp_endpoint: None, override_tunnel_mtu: None, qlog_log_until: FutureSystemTime(None), masque_settings: None, compliance_environment: None }
2025-07-28T09:48:01.201Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc response: bdd4047e-d188-4ed1-98d3-a56802053986; Success
2025-07-28T09:48:01.201Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="handle_ipc_request" elapsed=731.462µs
2025-07-28T09:48:01.202Z  INFO warp::warp_service::ipc_loop: IPC connection ended
2025-07-28T09:48:01.241Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T09:48:01.241Z DEBUG firewall::change: Firewall allow captive portal detection
2025-07-28T09:48:01.246Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T09:48:01.246Z DEBUG captive_portal: Running captive portal detections [legacy,os-detected,http-redirect[cloudflareportal.com],no-dns-redirect[cloudflareportal.com],expected-content,third-party]
2025-07-28T09:48:01.247Z  INFO captive_portal::detections::http_redirect: Checking for captive portal with HTTP-Redirect detection host="connectivitycheck.gstatic.com" ips=None interface=None bind_addr=None
2025-07-28T09:48:01.247Z  INFO run{detection_type=legacy}: captive_portal::detections::legacy: Checking for captive portal with legacy detection urls=["cloudflareportal.com", "cloudflareok.com", "cloudflarecp.com"]
2025-07-28T09:48:01.247Z DEBUG run{detection_type=os-detected}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T09:48:01.247Z  INFO captive_portal::detections::http_redirect: Checking for captive portal with HTTP-Redirect detection host="captive.apple.com" ips=None interface=None bind_addr=None
2025-07-28T09:48:01.247Z  INFO captive_portal::detections::http_redirect: Checking for captive portal with HTTP-Redirect detection host="www.msftconnecttest.com" ips=None interface=None bind_addr=None
2025-07-28T09:48:01.247Z DEBUG main_loop:connect_single_protocol{protocol="wireguard"}:happy_eyeballs_with_retry{protocol="wireguard"}: warp_edge::happy_eyeballs: Attempting happy eyeballs with retries retries=0 retry_delay=0ns stage=FirstRun
2025-07-28T09:48:01.247Z  INFO run{detection_type=http-redirect[cloudflareportal.com]}: captive_portal::detections::http_redirect: Checking for captive portal with HTTP-Redirect detection host="cloudflareportal.com" ips=None interface=None bind_addr=None
2025-07-28T09:48:01.247Z DEBUG main_loop:connect_single_protocol{protocol="wireguard"}:happy_eyeballs_with_retry{protocol="wireguard"}: warp_edge::happy_eyeballs: Attempting Happy Eyeballs to *************:2408 / [2606:4700:d0::a29f:c005]:2408
2025-07-28T09:48:01.248Z DEBUG main_loop:connect_single_protocol{protocol="wireguard"}:happy_eyeballs_with_retry{protocol="wireguard"}: warp_edge::happy_eyeballs: Happy Eyeballs check failed v6=[2606:4700:d0::a29f:c005]:2408 err=Connect(Os { code: 101, kind: NetworkUnreachable, message: "Network is unreachable" })
2025-07-28T09:48:01.248Z DEBUG main_loop:connect_single_protocol{protocol="wireguard"}:happy_eyeballs_with_retry{protocol="wireguard"}: warp_edge::happy_eyeballs: Start racer **********:50501 ---> *************:2408
2025-07-28T09:48:01.248Z  INFO run{detection_type=no-dns-redirect[cloudflareportal.com]}: captive_portal::detections::http_redirect: Checking for captive portal with HTTP-Redirect detection host="cloudflareportal.com" ips=Some([**********, **************, **********, 2606:4700:7::1, 2606:4700::1]) interface=None bind_addr=None
2025-07-28T09:48:01.249Z DEBUG main_loop:connect_single_protocol{protocol="wireguard"}:happy_eyeballs_with_retry{protocol="wireguard"}: warp_edge::wireguard_tun: Handshaking wireguard with session tag affinity_tag=0
2025-07-28T09:48:01.250Z DEBUG main_loop:connect_single_protocol{protocol="wireguard"}:happy_eyeballs_with_retry{protocol="wireguard"}: warp_edge::wireguard_tun: Sent handshake initiation to *************:2408
2025-07-28T09:48:01.250Z  INFO main_loop: warp::warp_service: WARP status: Connecting(PerformingHappyEyeballs((*************:2408, [2606:4700:d0::a29f:c005]:2408)))
2025-07-28T09:48:01.250Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T09:48:01.250Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Connecting(PerformingHappyEyeballs((*************:2408, [2606:4700:d0::a29f:c005]:2408)))
2025-07-28T09:48:01.250Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Connecting(PerformingHappyEyeballs((*************:2408, [2606:4700:d0::a29f:c005]:2408)))
2025-07-28T09:48:01.250Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=164.072µs
2025-07-28T09:48:01.256Z  INFO run{detection_type=legacy}: captive_portal::detections::legacy: Successfully resolved DNS queries
2025-07-28T09:48:01.258Z DEBUG captive_portal::detections::http_redirect: 204 response for HTTP test. No captive portal detected
2025-07-28T09:48:01.260Z DEBUG captive_portal::detections::http_redirect: 200 response for HTTP test. No captive portal detected
2025-07-28T09:48:01.261Z DEBUG captive_portal::detections::http_redirect: 200 response for HTTP test. No captive portal detected
2025-07-28T09:48:01.261Z DEBUG run{detection_type=third-party}: captive_portal::detections::third_party: Third party detections complete detections=[("connectivitycheck.gstatic.com", Ok(NoCaptivePortalDetected)), ("captive.apple.com", Ok(NoCaptivePortalDetected)), ("www.msftconnecttest.com", Ok(NoCaptivePortalDetected))]
2025-07-28T09:48:01.261Z DEBUG run{detection_type=third-party}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T09:48:01.275Z DEBUG run{detection_type=no-dns-redirect[cloudflareportal.com]}: captive_portal::detections::http_redirect: 204 response for HTTP test. No captive portal detected
2025-07-28T09:48:01.275Z DEBUG run{detection_type=no-dns-redirect[cloudflareportal.com]}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T09:48:01.293Z DEBUG run{detection_type=http-redirect[cloudflareportal.com]}: captive_portal::detections::http_redirect: 204 response for HTTP test. No captive portal detected
2025-07-28T09:48:01.294Z DEBUG run{detection_type=http-redirect[cloudflareportal.com]}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T09:48:01.295Z  INFO run{detection_type=legacy}: captive_portal::detections::legacy: Successfully retrieved HTTPs page
2025-07-28T09:48:01.295Z DEBUG run{detection_type=legacy}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T09:48:01.309Z DEBUG run{detection_type=expected-content}: captive_portal::detections::expected_content: 200 response and correct content for HTTP Content test. No captive portal detected
2025-07-28T09:48:01.309Z DEBUG run{detection_type=expected-content}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T09:48:01.310Z DEBUG captive_portal: Captive portal detection completed results=[("os-detected", Ok(NoCaptivePortalDetected)), ("third-party", Ok(NoCaptivePortalDetected)), ("no-dns-redirect[cloudflareportal.com]", Ok(NoCaptivePortalDetected)), ("http-redirect[cloudflareportal.com]", Ok(NoCaptivePortalDetected)), ("legacy", Ok(NoCaptivePortalDetected)), ("expected-content", Ok(NoCaptivePortalDetected))]
2025-07-28T09:48:01.310Z DEBUG firewall::change: Firewall disallow captive portal detection
2025-07-28T09:48:01.317Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T09:48:01.317Z DEBUG firewall::captive_portal: Firewall captive portal detection expired
2025-07-28T09:48:02.138Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="main_loop_compat_rx"
2025-07-28T09:48:02.138Z DEBUG main_loop:handle_update{update=NetworkInfoChanged}: warp::warp_service::actor_compat: Handling network status change old_info=IPv4: [eth0; **********; Ethernet; Gateway: Some(**********)], DNS servers:,  127.0.0.11:53
 new_info=IPv4: [eth0; **********; Ethernet; Gateway: Some(**********)], DNS servers:,  127.0.0.11:53

2025-07-28T09:48:02.138Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="main_loop_compat_rx" elapsed=293.459µs
2025-07-28T09:48:02.155Z DEBUG warp_api_endpoints::payloads: Warp endpoint override ports ports=[443, 500, 1701, 4500, 4443, 8443, 8095]
2025-07-28T09:48:02.155Z DEBUG main_loop: warp_api_client::executor: Returning API response for RotateKeys request_id=3b132640-7569-4b11-8711-aee60e027cbd
2025-07-28T09:48:02.155Z DEBUG main_loop: warp::warp_service::api_handlers: Registration updated current_registration=Registered(reg=ecd6f5b1-04e9-4394-92f6-015127a8c2e8) disposition=CheckAndUpdate
2025-07-28T09:48:02.155Z DEBUG main_loop: warp::warp_service::api_handlers: Invalidating existing posture check scheduler on registration update
2025-07-28T09:48:02.155Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="api_responses"
2025-07-28T09:48:02.155Z DEBUG main_loop: warp::warp_service::api_handlers: Registration updated 0.00 hours old, refresh at 2025-07-29 9:48:02.155162 +00:00:00
Registration: ecd6f5b1-04e9-4394-92f6-015127a8c2e8
Device Identifiers: System User
Auth Method: Consumer
Public key: 3059301306072a8648ce3d020106082a8648ce3d0301070342000499b56ceaaefc7ce8cd73367566360d1851dc5caaa264437b5129d4cf5e803cd43a9aebdd5178951fce8a711135388244fbf955ca81f56f181eee431745324208
Interface: WarpEndpoint { v4: **********, v6: 2606:4700:110:858c:888:a5bc:a05a:360d }
Endpoints: [WarpSocketAddrPair { v4: *************:443, v6: [2606:4700:103::2]:443 }, WarpSocketAddrPair { v4: *************:500, v6: [2606:4700:103::2]:500 }, WarpSocketAddrPair { v4: *************:1701, v6: [2606:4700:103::2]:1701 }, WarpSocketAddrPair { v4: *************:4500, v6: [2606:4700:103::2]:4500 }, WarpSocketAddrPair { v4: *************:4443, v6: [2606:4700:103::2]:4443 }, WarpSocketAddrPair { v4: *************:8443, v6: [2606:4700:103::2]:8443 }, WarpSocketAddrPair { v4: *************:8095, v6: [2606:4700:103::2]:8095 }]
Subnet CIDRS: None

Account: Free { id: AccountId(5ab76f72-c06d-4187-893e-a6d6f0d71cba), license: "Ub56i2C0-S51U7Bi4-580Sk7Iq" }

2025-07-28T09:48:02.155Z  INFO main_loop: warp_storage::registration::registration_storage: Saving registration to active cache config_name=None username="WARPSecret"
2025-07-28T09:48:02.156Z DEBUG main_loop: warp::warp::controller: Stopping WarpConnection state=Connecting
2025-07-28T09:48:02.156Z DEBUG firewall::change: Firewall reset to defaults
2025-07-28T09:48:02.157Z DEBUG warp::warp_service::ipc_handlers: Sending IPC update: ConfigurationUpdated
2025-07-28T09:48:02.157Z DEBUG warp::warp_service::ipc_handlers: Ipc Broadcast ResponseUpdate: ConfigurationUpdated
2025-07-28T09:48:02.157Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "emergency-disconnect.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "statistics.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "root-ca.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "registration-verifier.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 11 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 3 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 3 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.0004934780000000001, count: 11 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 107.0, count: 6 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="registration change"}: actor_statistics::handler: Starting to upload stats
2025-07-28T09:48:02.157Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "emergency-disconnect.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "statistics.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "root-ca.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 2 }, CounterPayload { name: "registration-verifier.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 11 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 3 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 3 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.0004934780000000001, count: 11 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 107.0, count: 6 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="registration change"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-07-28T09:48:02.157Z  INFO actor_statistics::handler: Switched to new registration for stats. All stats have been reset. new_registration_id=None
2025-07-28T09:48:02.162Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T09:48:02.163Z DEBUG firewall::change: Firewall allow API ips api_ips=[*************, ************, 2606:4700::6810:1854, 2606:4700::6810:c052]
2025-07-28T09:48:02.163Z DEBUG firewall::change: Changing catch all policy Deny->Deny
2025-07-28T09:48:02.163Z DEBUG firewall::change: Firewall allow managed network endpoints managed_network_endpoints=[]
2025-07-28T09:48:02.163Z DEBUG firewall::change: Firewall allow anycast ips: [*************, 2606:4700:102::3]
2025-07-28T09:48:02.201Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T09:48:02.201Z DEBUG main_loop: warp::warp_service: Determining disconnected reason from connectivity state net_info=IPv4: [eth0; **********; Ethernet; Gateway: Some(**********)], DNS servers:,  127.0.0.11:53
 power_state=None disconnect_reason=None
2025-07-28T09:48:02.201Z  WARN main_loop: warp::warp_service: Disconnecting, but reason is unknown
2025-07-28T09:48:02.201Z DEBUG main_loop: warp::warp_service: Checking for registration vs settings account mismatch reg_is_teams=false settings_is_teams=false reg_org=None settings_org=None
2025-07-28T09:48:02.202Z DEBUG firewall::change: Firewall allow API ips api_ips=[*************, ************, 2606:4700::6810:1854, 2606:4700::6810:c052]
2025-07-28T09:48:02.202Z DEBUG firewall::change: Changing catch all policy Deny->Deny
2025-07-28T09:48:02.202Z DEBUG firewall::change: Firewall allow managed network endpoints managed_network_endpoints=[]
2025-07-28T09:48:02.202Z DEBUG firewall::change: Firewall allow anycast ips: [*************, 2606:4700:102::3]
2025-07-28T09:48:02.208Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T09:48:02.209Z  INFO main_loop: warp::warp_service: User Settings Changed: {}
2025-07-28T09:48:02.209Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC update: SettingsUpdated
2025-07-28T09:48:02.209Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseUpdate: SettingsUpdated
2025-07-28T09:48:02.209Z DEBUG main_loop: warp::warp_service: update_settings: no restart required
2025-07-28T09:48:02.209Z DEBUG main_loop: warp::warp::controller: Stopping WarpConnection state=Disconnected
2025-07-28T09:48:02.210Z DEBUG firewall::change: Firewall allow API ips api_ips=[*************, ************, 2606:4700::6810:1854, 2606:4700::6810:c052]
2025-07-28T09:48:02.210Z DEBUG firewall::change: Changing catch all policy Deny->Deny
2025-07-28T09:48:02.210Z DEBUG firewall::change: Firewall allow managed network endpoints managed_network_endpoints=[]
2025-07-28T09:48:02.210Z DEBUG firewall::change: Firewall allow anycast ips: [*************, 2606:4700:102::3]
2025-07-28T09:48:02.245Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T09:48:02.246Z DEBUG main_loop: warp::warp_service: Determining disconnected reason from connectivity state net_info=IPv4: [eth0; **********; Ethernet; Gateway: Some(**********)], DNS servers:,  127.0.0.11:53
 power_state=None disconnect_reason=None
2025-07-28T09:48:02.246Z  WARN main_loop: warp::warp_service: Disconnecting, but reason is unknown
2025-07-28T09:48:02.246Z DEBUG run: warp_settings::manager: LayerManager update: NetworkPolicy(Some(NetworkPolicySettings { onboarding: None, operation_mode: None, disable_auto_fallback: None, fallback_domains: None, proxy_port: None, split_config: None, gateway_id: None, support_url: None, allow_mode_switch: None, switch_locked: None, auto_connect: None, captive_portal: None, organization: None, allow_updates: None, allowed_to_leave: None, profile_id: None, lan_allow_settings: Some(LanAllowSettings { minutes: None, subnet_size: SubnetSize(24) }), warp_tunnel_protocol: Some(Masque), masque_settings: None, register_interface_ip_with_dns: None, anycast_split_ips: Some(AnycastSplitIps { always_exclude: [*************, 2606:4700:102::3], always_include: [*************, 2606:4700:102::4] }), firewall_scope: None, sccm_vpn_boundary_support: None, enable_post_quantum: None }))
2025-07-28T09:48:02.247Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="api_responses" elapsed=91.747214ms
2025-07-28T09:48:02.247Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="main_loop_compat_rx"
2025-07-28T09:48:02.248Z DEBUG main_loop:handle_command: warp::warp::controller: Stopping WarpConnection state=Disconnected
2025-07-28T09:48:02.249Z DEBUG firewall::change: Firewall allow API ips api_ips=[*************, ************, 2606:4700::6810:1854, 2606:4700::6810:c052]
2025-07-28T09:48:02.249Z DEBUG firewall::change: Changing catch all policy Deny->Deny
2025-07-28T09:48:02.249Z DEBUG firewall::change: Firewall allow managed network endpoints managed_network_endpoints=[]
2025-07-28T09:48:02.249Z DEBUG firewall::change: Firewall allow anycast ips: [*************, 2606:4700:102::3]
2025-07-28T09:48:02.255Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T09:48:02.255Z  INFO main_loop:handle_command: warp::warp_service: captive_portal_fw_until: Indefinitely
2025-07-28T09:48:02.256Z DEBUG main_loop:handle_command: warp::warp::proxying_dns: Using auto fallback: true
2025-07-28T09:48:02.256Z DEBUG main_loop:handle_command: warp::warp::controller: Setting WarpConnection current_state=Disconnected
2025-07-28T09:48:02.256Z DEBUG main_loop:handle_command: warp::warp::controller: Stopping WarpConnection state=Disconnected
2025-07-28T09:48:02.257Z DEBUG main_loop: warp::warp::warp_connection: Current Network: IPv4: [eth0; **********; Ethernet; Gateway: Some(**********)], DNS servers:,  127.0.0.11:53

2025-07-28T09:48:02.257Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="main_loop_compat_rx" elapsed=9.993376ms
2025-07-28T09:48:02.257Z  INFO main_loop: warp::warp::warp_connection: Starting Warp Connection operation_mode=Warp dns_mode=DNS Proxy tunnel_mode=Exclude-only Tunnel posture_only=disabled forward_proxy=disabled
2025-07-28T09:48:02.257Z  INFO main_loop: warp::warp::tunnel: Initiate WARP connection protocol=Masque self.masque_settings=MasqueProtocolSettings { http_version: H3WithH2Fallback }
2025-07-28T09:48:02.257Z  INFO main_loop: warp::warp_service: WARP status: Connecting(CheckingNetwork)
2025-07-28T09:48:02.257Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Connecting(CheckingNetwork)
2025-07-28T09:48:02.257Z DEBUG firewall::change: Firewall allow tunnel UDP ipv4=[*************] ipv6=[2606:4700:103::2]
2025-07-28T09:48:02.257Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Connecting(CheckingNetwork)
2025-07-28T09:48:02.257Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T09:48:02.257Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=86.555µs
2025-07-28T09:48:02.257Z  INFO main_loop: warp::warp_service: WARP status: Connecting(InitializingSettings)
2025-07-28T09:48:02.257Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="registration_change"
2025-07-28T09:48:02.257Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="registration_change" elapsed=54.914µs
2025-07-28T09:48:02.257Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Connecting(InitializingSettings)
2025-07-28T09:48:02.257Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Connecting(InitializingSettings)
2025-07-28T09:48:02.257Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T09:48:02.258Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=380.424µs
2025-07-28T09:48:02.301Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T09:48:02.301Z DEBUG firewall::change: Firewall allow tunnel TCP addresses=[*************:443, *************:500, *************:1701, *************:4443, *************:4500, *************:8095, *************:8443, [2606:4700:103::2]:443, [2606:4700:103::2]:500, [2606:4700:103::2]:1701, [2606:4700:103::2]:4443, [2606:4700:103::2]:4500, [2606:4700:103::2]:8095, [2606:4700:103::2]:8443]
2025-07-28T09:48:02.313Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T09:48:02.314Z DEBUG firewall::change: Firewall allow captive portal detection
2025-07-28T09:48:02.357Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T09:48:02.358Z DEBUG main_loop:connect_with_fallback{primary="masque" fallback="H2"}:happy_eyeballs_with_retry{protocol="masque"}: warp_edge::happy_eyeballs: Attempting happy eyeballs with retries retries=0 retry_delay=0ns stage=FirstRun
2025-07-28T09:48:02.358Z DEBUG captive_portal: Running captive portal detections [legacy,os-detected,http-redirect[cloudflareportal.com],no-dns-redirect[cloudflareportal.com],expected-content,third-party]
2025-07-28T09:48:02.358Z DEBUG main_loop:connect_with_fallback{primary="masque" fallback="H2"}:happy_eyeballs_with_retry{protocol="masque"}: warp_edge::happy_eyeballs: Attempting Happy Eyeballs to *************:443 / [2606:4700:103::2]:443
2025-07-28T09:48:02.358Z  INFO captive_portal::detections::http_redirect: Checking for captive portal with HTTP-Redirect detection host="connectivitycheck.gstatic.com" ips=None interface=None bind_addr=None
2025-07-28T09:48:02.358Z  INFO run{detection_type=http-redirect[cloudflareportal.com]}: captive_portal::detections::http_redirect: Checking for captive portal with HTTP-Redirect detection host="cloudflareportal.com" ips=None interface=None bind_addr=None
2025-07-28T09:48:02.358Z  INFO run{detection_type=legacy}: captive_portal::detections::legacy: Checking for captive portal with legacy detection urls=["cloudflareportal.com", "cloudflareok.com", "cloudflarecp.com"]
2025-07-28T09:48:02.358Z DEBUG run{detection_type=os-detected}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T09:48:02.358Z  INFO run{detection_type=no-dns-redirect[cloudflareportal.com]}: captive_portal::detections::http_redirect: Checking for captive portal with HTTP-Redirect detection host="cloudflareportal.com" ips=Some([**********, **************, **********, 2606:4700:7::1, 2606:4700::1]) interface=None bind_addr=None
2025-07-28T09:48:02.358Z  INFO captive_portal::detections::http_redirect: Checking for captive portal with HTTP-Redirect detection host="www.msftconnecttest.com" ips=None interface=None bind_addr=None
2025-07-28T09:48:02.358Z  INFO captive_portal::detections::http_redirect: Checking for captive portal with HTTP-Redirect detection host="captive.apple.com" ips=None interface=None bind_addr=None
2025-07-28T09:48:02.359Z DEBUG main_loop:connect_with_fallback{primary="masque" fallback="H2"}:happy_eyeballs_with_retry{protocol="masque"}: warp_edge::happy_eyeballs: Start racer **********:55181 ---> *************:443
2025-07-28T09:48:02.360Z DEBUG main_loop:connect_with_fallback{primary="masque" fallback="H2"}:happy_eyeballs_with_retry{protocol="masque"}: warp_edge::h3_tun: Creating QUIC Client Connection Parmaeters qlog_setting=Disabled idle_timeout=IdleTimeout { idle_duration: IdleDuration(5s), retries: 0 }
2025-07-28T09:48:02.361Z  INFO run{detection_type=legacy}: captive_portal::detections::legacy: Successfully resolved DNS queries
2025-07-28T09:48:02.362Z DEBUG main_loop:connect_with_fallback{primary="masque" fallback="H2"}:happy_eyeballs_with_retry{protocol="masque"}: warp_edge::h3_tun: Sending handshake initiation to *************:443
2025-07-28T09:48:02.363Z  INFO slog: created unestablished quiche::Connection slog.target="tokio_quiche::quic" slog.module_path="tokio_quiche::quic" slog.file="/home/<USER>/.cargo/git/checkouts/quiche-4103ffa15137cf39/bfd737b/tokio-quiche/src/quic/mod.rs" slog.line=235 slog.column=5 slog.kv="provided_dcid=None,scid=f6ae5963007bd58af823c5a95b63b4e784cc2068"
2025-07-28T09:48:02.363Z DEBUG main_loop:connect_with_fallback{primary="masque" fallback="H2"}:happy_eyeballs_with_retry{protocol="masque"}: warp_edge::happy_eyeballs: Happy Eyeballs check failed v6=[2606:4700:103::2]:443 err=Connect(Os { code: 101, kind: NetworkUnreachable, message: "Network is unreachable" })
2025-07-28T09:48:02.363Z  INFO main_loop: warp::warp_service: WARP status: Connecting(PerformingHappyEyeballs((*************:443, [2606:4700:103::2]:443)))
2025-07-28T09:48:02.363Z DEBUG slog: sending client Initials to peer slog.target="tokio_quiche::quic::router::connector" slog.module_path="tokio_quiche::quic::router::connector" slog.file="/home/<USER>/.cargo/git/checkouts/quiche-4103ffa15137cf39/bfd737b/tokio-quiche/src/quic/router/connector.rs" slog.line=266 slog.column=5 slog.kv="scid=f6ae5963007bd58af823c5a95b63b4e784cc2068"
2025-07-28T09:48:02.363Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Connecting(PerformingHappyEyeballs((*************:443, [2606:4700:103::2]:443)))
2025-07-28T09:48:02.363Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Connecting(PerformingHappyEyeballs((*************:443, [2606:4700:103::2]:443)))
2025-07-28T09:48:02.363Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T09:48:02.363Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=161.718µs
2025-07-28T09:48:02.368Z DEBUG captive_portal::detections::http_redirect: 204 response for HTTP test. No captive portal detected
2025-07-28T09:48:02.368Z DEBUG slog: sending client Initials to peer slog.target="tokio_quiche::quic::router::connector" slog.module_path="tokio_quiche::quic::router::connector" slog.file="/home/<USER>/.cargo/git/checkouts/quiche-4103ffa15137cf39/bfd737b/tokio-quiche/src/quic/router/connector.rs" slog.line=266 slog.column=5 slog.kv="scid=f6ae5963007bd58af823c5a95b63b4e784cc2068"
2025-07-28T09:48:02.369Z DEBUG captive_portal::detections::http_redirect: 200 response for HTTP test. No captive portal detected
2025-07-28T09:48:02.369Z DEBUG captive_portal::detections::http_redirect: 200 response for HTTP test. No captive portal detected
2025-07-28T09:48:02.370Z DEBUG run{detection_type=third-party}: captive_portal::detections::third_party: Third party detections complete detections=[("connectivitycheck.gstatic.com", Ok(NoCaptivePortalDetected)), ("www.msftconnecttest.com", Ok(NoCaptivePortalDetected)), ("captive.apple.com", Ok(NoCaptivePortalDetected))]
2025-07-28T09:48:02.370Z DEBUG run{detection_type=third-party}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T09:48:02.375Z DEBUG slog: sending client Initials to peer slog.target="tokio_quiche::quic::router::connector" slog.module_path="tokio_quiche::quic::router::connector" slog.file="/home/<USER>/.cargo/git/checkouts/quiche-4103ffa15137cf39/bfd737b/tokio-quiche/src/quic/router/connector.rs" slog.line=266 slog.column=5 slog.kv="scid=f6ae5963007bd58af823c5a95b63b4e784cc2068"
2025-07-28T09:48:02.380Z DEBUG slog: QUIC connection established slog.target="tokio_quiche::quic::router::connector" slog.module_path="tokio_quiche::quic::router::connector" slog.file="/home/<USER>/.cargo/git/checkouts/quiche-4103ffa15137cf39/bfd737b/tokio-quiche/src/quic/router/connector.rs" slog.line=176 slog.column=13 slog.kv="scid=f6ae5963007bd58af823c5a95b63b4e784cc2068"
2025-07-28T09:48:02.380Z DEBUG main_loop:connect_with_fallback{primary="masque" fallback="H2"}:happy_eyeballs_with_retry{protocol="masque"}: warp_edge::h3_tun: Established QUIC connection with *************:443 scid=f6ae5963007bd58af823c5a95b63b4e784cc2068
2025-07-28T09:48:02.380Z DEBUG main_loop:connect_with_fallback{primary="masque" fallback="H2"}:happy_eyeballs_with_retry{protocol="masque"}: warp_edge::h3_tun: Establishing MASQUE tunnel on existing QUIC connection scid=f6ae5963007bd58af823c5a95b63b4e784cc2068 endpoint=*************:443
2025-07-28T09:48:02.380Z  INFO main_loop:connect_with_fallback{primary="masque" fallback="H2"}:happy_eyeballs_with_retry{protocol="masque"}:proxy_task{scid=f6ae5963007bd58af823c5a95b63b4e784cc2068}: warp_edge::h3_tun: Starting MASQUE proxy task idle_timeout=IdleTimeout { idle_duration: IdleDuration(5s), retries: 0 } keepalive_interval=4s
2025-07-28T09:48:02.380Z  INFO main_loop: warp::warp_service: WARP status: Connecting(EstablishingConnection(*************:443))
2025-07-28T09:48:02.380Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Connecting(EstablishingConnection(*************:443))
2025-07-28T09:48:02.380Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Connecting(EstablishingConnection(*************:443))
2025-07-28T09:48:02.380Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T09:48:02.381Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=305.651µs
2025-07-28T09:48:02.381Z  INFO slog: creating new flow for MASQUE request slog.target="tokio_quiche::http3::driver::client" slog.module_path="tokio_quiche::http3::driver::client" slog.file="/home/<USER>/.cargo/git/checkouts/quiche-4103ffa15137cf39/bfd737b/tokio-quiche/src/http3/driver/client.rs" slog.line=168 slog.column=13 slog.kv="flow_id=0,stream_id=0"
2025-07-28T09:48:02.382Z DEBUG run{detection_type=http-redirect[cloudflareportal.com]}: captive_portal::detections::http_redirect: 204 response for HTTP test. No captive portal detected
2025-07-28T09:48:02.382Z DEBUG run{detection_type=http-redirect[cloudflareportal.com]}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T09:48:02.383Z DEBUG run{detection_type=no-dns-redirect[cloudflareportal.com]}: captive_portal::detections::http_redirect: 204 response for HTTP test. No captive portal detected
2025-07-28T09:48:02.383Z DEBUG run{detection_type=no-dns-redirect[cloudflareportal.com]}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T09:48:02.390Z DEBUG run{detection_type=expected-content}: captive_portal::detections::expected_content: 200 response and correct content for HTTP Content test. No captive portal detected
2025-07-28T09:48:02.390Z DEBUG run{detection_type=expected-content}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T09:48:02.392Z  INFO run{detection_type=legacy}: captive_portal::detections::legacy: Successfully retrieved HTTPs page
2025-07-28T09:48:02.392Z DEBUG run{detection_type=legacy}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T09:48:02.392Z DEBUG captive_portal: Captive portal detection completed results=[("os-detected", Ok(NoCaptivePortalDetected)), ("third-party", Ok(NoCaptivePortalDetected)), ("http-redirect[cloudflareportal.com]", Ok(NoCaptivePortalDetected)), ("no-dns-redirect[cloudflareportal.com]", Ok(NoCaptivePortalDetected)), ("expected-content", Ok(NoCaptivePortalDetected)), ("legacy", Ok(NoCaptivePortalDetected))]
2025-07-28T09:48:02.393Z DEBUG firewall::change: Firewall disallow captive portal detection
2025-07-28T09:48:02.407Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T09:48:02.407Z DEBUG firewall::captive_portal: Firewall captive portal detection expired
2025-07-28T09:48:03.734Z DEBUG slog: got a response for stream_id=0, headers=[":status: 200", "cf-team: 28f207b7160000d665f13f9400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:48:03.734Z DEBUG slog: upgraded H3Body: flow_id=0, mode=WithContextId slog.target="httx::body" slog.module_path="httx::body" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/body/mod.rs" slog.line=678 slog.column=5
2025-07-28T09:48:03.735Z DEBUG main_loop:connect_with_fallback{primary="masque" fallback="H2"}: warp::warp::tunnel::fallback: Connecting with primary connection protocol, reverting fallback firewall config
2025-07-28T09:48:03.735Z DEBUG firewall::change: Firewall allow tunnel TCP addresses=[]
2025-07-28T09:48:03.741Z DEBUG slog: got a response for stream_id=4, headers=[":status: 200", "cf-team: 28f207b71c0000d665f13fa400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:48:03.741Z DEBUG main_loop:connect_with_fallback{primary="masque" fallback="H2"}:tunnel_stats_reporting_task{scid=f6ae5963007bd58af823c5a95b63b4e784cc2068}: warp_edge::h3_tun::tunnel_stats: Updating tunnel stats reporting interval from=120s to=15s
2025-07-28T09:48:03.744Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T09:48:03.744Z DEBUG main_loop:start_tunnel_processing{protocol="masque"}: warp::warp::tunnel: Connected to *************:443
2025-07-28T09:48:03.744Z  INFO main_loop:start_tunnel_processing{protocol="masque"}: warp::warp::tunnel: DNS resolver connection is being made inside of the tunnel
2025-07-28T09:48:03.745Z DEBUG main_loop:start_tunnel_processing{protocol="masque"}: warp_primitives::daemon_status: Connection stage 'Establishing connection to *************:443' took 1364ms which is longer than threshold of 500ms
2025-07-28T09:48:03.746Z  INFO main_loop: warp::warp_service: WARP status: Connecting(InitializingTunnelInterface)
2025-07-28T09:48:03.746Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Connecting(InitializingTunnelInterface)
2025-07-28T09:48:03.746Z DEBUG firewall::change: Firewall allow interface CloudflareWARP
2025-07-28T09:48:03.746Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Connecting(InitializingTunnelInterface)
2025-07-28T09:48:03.746Z  INFO main_loop: warp::warp_service: WARP status: Connecting(ConfiguringInitialFirewall)
2025-07-28T09:48:03.746Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Connecting(ConfiguringInitialFirewall)
2025-07-28T09:48:03.746Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Connecting(ConfiguringInitialFirewall)
2025-07-28T09:48:03.746Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T09:48:03.746Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=289.06µs
2025-07-28T09:48:03.746Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T09:48:03.746Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=213.747µs
2025-07-28T09:48:03.773Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T09:48:03.791Z DEBUG firewall::change: Firewall allowing private IPs
2025-07-28T09:48:03.791Z  INFO main_loop: warp::warp_service: WARP status: Connecting(ConfiguringFirewallRules)
2025-07-28T09:48:03.791Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Connecting(ConfiguringFirewallRules)
2025-07-28T09:48:03.791Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Connecting(ConfiguringFirewallRules)
2025-07-28T09:48:03.791Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T09:48:03.791Z  INFO main_loop: warp::warp_service: WARP status: Connecting(ConfiguringFirewallRules)
2025-07-28T09:48:03.791Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Connecting(ConfiguringFirewallRules)
2025-07-28T09:48:03.791Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=208.295µs
2025-07-28T09:48:03.791Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Connecting(ConfiguringFirewallRules)
2025-07-28T09:48:03.791Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T09:48:03.791Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=62.139µs
2025-07-28T09:48:03.800Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T09:48:03.800Z  INFO main_loop: warp::warp::proxying_dns: Initiate DNS connection
2025-07-28T09:48:03.800Z DEBUG main_loop: warp::warp::proxying_dns: Racing DNS IPs ips=[************, ************, 2606:4700:4700::1111, 2606:4700:4700::1001]
2025-07-28T09:48:03.802Z DEBUG tunnel_loop{protocol="masque" con_id="f6ae5963007bd58af823c5a95b63b4e784cc2068"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 1, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-07-28T09:48:03.808Z DEBUG main_loop: warp::warp::proxying_dns: DNS TCP connection to Gateway DoH resolver was successful raced_ip=************
2025-07-28T09:48:03.809Z DEBUG main_loop: dns_proxy::proxy: Creating DoH resolver config=ResolverConfig { domain: None, search: [], name_servers: NameServerConfigGroup([NameServerConfig { socket_addr: ************:443, protocol: Https, tls_dns_name: Some("cloudflare-dns.com"), trust_negative_responses: true, tls_config: None, bind_addr: None }, NameServerConfig { socket_addr: ************:443, protocol: Https, tls_dns_name: Some("cloudflare-dns.com"), trust_negative_responses: true, tls_config: None, bind_addr: None }, NameServerConfig { socket_addr: [2606:4700:4700::1111]:443, protocol: Https, tls_dns_name: Some("cloudflare-dns.com"), trust_negative_responses: true, tls_config: None, bind_addr: None }, NameServerConfig { socket_addr: [2606:4700:4700::1001]:443, protocol: Https, tls_dns_name: Some("cloudflare-dns.com"), trust_negative_responses: true, tls_config: None, bind_addr: None }], None) } options=ResolverOpts { ndots: 1, timeout: 4s, attempts: 0, rotate: false, check_names: true, edns0: true, validate: false, ip_strategy: Ipv4thenIpv6, cache_size: 32, use_hosts_file: true, positive_min_ttl: None, negative_min_ttl: None, positive_max_ttl: None, negative_max_ttl: Some(10s), num_concurrent_reqs: 1, preserve_intermediates: true, try_tcp_on_error: false, server_ordering_strategy: QueryStatistics, recursion_desired: true, authentic_data: false, shuffle_dns_servers: false }
2025-07-28T09:48:03.809Z  INFO main_loop: warp::warp_service: WARP status: Connecting(CheckingForRouteToDnsEndpoint)
2025-07-28T09:48:03.809Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Connecting(CheckingForRouteToDnsEndpoint)
2025-07-28T09:48:03.809Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T09:48:03.809Z DEBUG firewall::change: Firewall allow captive portal detection
2025-07-28T09:48:03.809Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Connecting(CheckingForRouteToDnsEndpoint)
2025-07-28T09:48:03.809Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=314.218µs
2025-07-28T09:48:03.845Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T09:48:03.846Z DEBUG captive_portal: Running captive portal detections [legacy,os-detected,http-redirect[cloudflareportal.com],no-dns-redirect[cloudflareportal.com],expected-content,third-party]
2025-07-28T09:48:03.847Z  INFO captive_portal::detections::http_redirect: Checking for captive portal with HTTP-Redirect detection host="connectivitycheck.gstatic.com" ips=None interface=Some("eth0") bind_addr=Some(**********)
2025-07-28T09:48:03.847Z  INFO run{detection_type=http-redirect[cloudflareportal.com]}: captive_portal::detections::http_redirect: Checking for captive portal with HTTP-Redirect detection host="cloudflareportal.com" ips=None interface=Some("eth0") bind_addr=Some(**********)
2025-07-28T09:48:03.847Z DEBUG run{detection_type=os-detected}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T09:48:03.847Z  INFO run{detection_type=legacy}: captive_portal::detections::legacy: Checking for captive portal with legacy detection urls=["cloudflareportal.com", "cloudflareok.com", "cloudflarecp.com"]
2025-07-28T09:48:03.847Z  INFO run{detection_type=no-dns-redirect[cloudflareportal.com]}: captive_portal::detections::http_redirect: Checking for captive portal with HTTP-Redirect detection host="cloudflareportal.com" ips=Some([**********, **************, **********, 2606:4700:7::1, 2606:4700::1]) interface=Some("eth0") bind_addr=Some(**********)
2025-07-28T09:48:03.848Z  INFO captive_portal::detections::http_redirect: Checking for captive portal with HTTP-Redirect detection host="captive.apple.com" ips=None interface=Some("eth0") bind_addr=Some(**********)
2025-07-28T09:48:03.848Z  INFO captive_portal::detections::http_redirect: Checking for captive portal with HTTP-Redirect detection host="www.msftconnecttest.com" ips=None interface=Some("eth0") bind_addr=Some(**********)
2025-07-28T09:48:03.849Z  INFO run{detection_type=legacy}: captive_portal::detections::legacy: Successfully resolved DNS queries
2025-07-28T09:48:03.857Z DEBUG captive_portal::detections::http_redirect: 200 response for HTTP test. No captive portal detected
2025-07-28T09:48:03.857Z DEBUG captive_portal::detections::http_redirect: 204 response for HTTP test. No captive portal detected
2025-07-28T09:48:03.858Z DEBUG captive_portal::detections::http_redirect: 200 response for HTTP test. No captive portal detected
2025-07-28T09:48:03.858Z DEBUG run{detection_type=third-party}: captive_portal::detections::third_party: Third party detections complete detections=[("captive.apple.com", Ok(NoCaptivePortalDetected)), ("connectivitycheck.gstatic.com", Ok(NoCaptivePortalDetected)), ("www.msftconnecttest.com", Ok(NoCaptivePortalDetected))]
2025-07-28T09:48:03.859Z DEBUG run{detection_type=third-party}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T09:48:03.867Z DEBUG run{detection_type=no-dns-redirect[cloudflareportal.com]}: captive_portal::detections::http_redirect: 204 response for HTTP test. No captive portal detected
2025-07-28T09:48:03.867Z DEBUG run{detection_type=no-dns-redirect[cloudflareportal.com]}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T09:48:03.867Z DEBUG run{detection_type=http-redirect[cloudflareportal.com]}: captive_portal::detections::http_redirect: 204 response for HTTP test. No captive portal detected
2025-07-28T09:48:03.867Z DEBUG run{detection_type=http-redirect[cloudflareportal.com]}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T09:48:03.874Z DEBUG main_loop: warp::warp::proxying_dns: Connectivity Check resolved query for connectivity.cloudflareclient.com resolved_ips=[**************, **************]
2025-07-28T09:48:03.874Z DEBUG main_loop: dns_proxy::resolver: Ending DoH connection stats=establish_time:2025-07-28 9:48:03.846173444 +00:00:00, ip:2606:4700:4700::1111, attempted_queries:1, successful_queries:1, certificate_errors:0, timeout_errors:0, no_connection_errors:0, proto_errors:0, io_errors:0, msg_errors:0
2025-07-28T09:48:03.874Z DEBUG main_loop: warp::warp::proxying_dns: Acquiring bind semaphore available_permits=1
2025-07-28T09:48:03.874Z DEBUG main_loop: warp::warp::proxying_dns: Binding UDP and TCP sockets dns_servers=[*********, *********]
2025-07-28T09:48:03.875Z DEBUG main_loop: warp::warp::proxying_dns: Successfully bound DNS listen sockets udp_sockets=2 tcp_sockets=2
2025-07-28T09:48:03.876Z DEBUG run{detection_type=expected-content}: captive_portal::detections::expected_content: 200 response and correct content for HTTP Content test. No captive portal detected
2025-07-28T09:48:03.877Z DEBUG run{detection_type=expected-content}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T09:48:03.877Z DEBUG main_loop: warp::warp::proxying_dns: Read system DNS configuration initial_dns_config=(ResolverConfig { domain: None, search: [], name_servers: NameServerConfigGroup([NameServerConfig { socket_addr: 127.0.0.11:53, protocol: Udp, tls_dns_name: None, trust_negative_responses: false, tls_config: None, bind_addr: None }, NameServerConfig { socket_addr: 127.0.0.11:53, protocol: Tcp, tls_dns_name: None, trust_negative_responses: false, tls_config: None, bind_addr: None }], None) }, ResolverOpts { ndots: 0, timeout: 5s, attempts: 2, rotate: false, check_names: true, edns0: false, validate: false, ip_strategy: Ipv4thenIpv6, cache_size: 32, use_hosts_file: true, positive_min_ttl: None, negative_min_ttl: None, positive_max_ttl: None, negative_max_ttl: None, num_concurrent_reqs: 2, preserve_intermediates: true, try_tcp_on_error: false, server_ordering_strategy: QueryStatistics, recursion_desired: true, authentic_data: false, shuffle_dns_servers: false })
2025-07-28T09:48:03.877Z DEBUG main_loop: warp::warp::proxying_dns: Initializing DNS restore settings net.v4_ifaces=[eth0; **********; Ethernet; Gateway: Some(**********)] net.v6_ifaces=[]
2025-07-28T09:48:03.877Z DEBUG main_loop: warp::warp::dns_recovery::unix: Applying DNS settings [*********, *********, ::ffff:*********, ::ffff:*********]
2025-07-28T09:48:03.877Z DEBUG warp_dns: Starting DnsOverWarp task tun_address=**********:0
2025-07-28T09:48:03.878Z  INFO dns_proxy::proxy: Default fallbacks configured default_fallback_ips=[127.0.0.11:53] config=ResolverConfig { domain: None, search: [], name_servers: NameServerConfigGroup([NameServerConfig { socket_addr: 127.0.0.11:53, protocol: Udp, tls_dns_name: None, trust_negative_responses: true, tls_config: None, bind_addr: None }, NameServerConfig { socket_addr: 127.0.0.11:53, protocol: Tcp, tls_dns_name: None, trust_negative_responses: true, tls_config: None, bind_addr: None }], None) } sys_options=ResolverOpts { ndots: 0, timeout: 11s, attempts: 0, rotate: false, check_names: true, edns0: true, validate: false, ip_strategy: Ipv4thenIpv6, cache_size: 32, use_hosts_file: true, positive_min_ttl: None, negative_min_ttl: None, positive_max_ttl: None, negative_max_ttl: None, num_concurrent_reqs: 8, preserve_intermediates: true, try_tcp_on_error: false, server_ordering_strategy: QueryStatistics, recursion_desired: true, authentic_data: false, shuffle_dns_servers: false }
2025-07-28T09:48:03.879Z DEBUG main_loop: network_info::linux::dns_manager: detected system dns manager file_owner=Unknown os_configuration_owner=File
2025-07-28T09:48:03.886Z  INFO main_loop: warp::warp_service: WARP status: Connecting(ApplyingDnsSettings)
2025-07-28T09:48:03.886Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Connecting(ApplyingDnsSettings)
2025-07-28T09:48:03.886Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Connecting(ApplyingDnsSettings)
2025-07-28T09:48:03.887Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T09:48:03.887Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=1.194834ms
2025-07-28T09:48:03.887Z  INFO run{detection_type=legacy}: captive_portal::detections::legacy: Successfully retrieved HTTPs page
2025-07-28T09:48:03.887Z DEBUG run{detection_type=legacy}: captive_portal::detections: return=Ok(NoCaptivePortalDetected)
2025-07-28T09:48:03.887Z DEBUG captive_portal: Captive portal detection completed results=[("os-detected", Ok(NoCaptivePortalDetected)), ("third-party", Ok(NoCaptivePortalDetected)), ("no-dns-redirect[cloudflareportal.com]", Ok(NoCaptivePortalDetected)), ("http-redirect[cloudflareportal.com]", Ok(NoCaptivePortalDetected)), ("expected-content", Ok(NoCaptivePortalDetected)), ("legacy", Ok(NoCaptivePortalDetected))]
2025-07-28T09:48:03.887Z DEBUG firewall::change: Firewall disallow captive portal detection
2025-07-28T09:48:03.888Z  INFO main_loop: warp::warp_service: WARP status: Connecting(PerformingConnectivityChecks)
2025-07-28T09:48:03.888Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Connecting(PerformingConnectivityChecks)
2025-07-28T09:48:03.888Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Connecting(PerformingConnectivityChecks)
2025-07-28T09:48:03.888Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T09:48:03.888Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=70.694µs
2025-07-28T09:48:03.889Z DEBUG connectivity_check: Resolved connectivity-check.warp-svc. to [*********, *********, ::ffff:*********, ::ffff:*********]
2025-07-28T09:48:03.896Z DEBUG firewall::linux: Firewall rules loaded
2025-07-28T09:48:03.896Z DEBUG firewall::captive_portal: Firewall captive portal detection expired
2025-07-28T09:48:03.908Z DEBUG do_trace_inner{host="engage.cloudflareclient.com" host_with_protocol="https://engage.cloudflareclient.com" timeout=4s sockets=[*************:443, [2606:4700:102::3]:443] con_check_meta=ConnectivityCheckMetadata { proxy_local_addr: None }}: connectivity_check: fl=696f40
h=engage.cloudflareclient.com
ip=***************
ts=1753696083.906
visit_scheme=https
uag=WARP for Linux
colo=FRA
sliver=none
http=http/1.1
loc=DE
tls=TLSv1.3
sni=plaintext
warp=off
gateway=off
rbi=off
kex=X25519

2025-07-28T09:48:03.966Z DEBUG do_trace_inner{host="connectivity.cloudflareclient.com" host_with_protocol="https://connectivity.cloudflareclient.com" timeout=4s sockets=[*************:443, [2606:4700:102::4]:443] con_check_meta=ConnectivityCheckMetadata { proxy_local_addr: None }}: connectivity_check: fl=71f1389
h=connectivity.cloudflareclient.com
ip=**************
ts=1753696083.915
visit_scheme=https
uag=WARP for Linux
colo=FRA
sliver=none
http=http/1.1
loc=DE
tls=TLSv1.3
sni=plaintext
warp=off
gateway=off
rbi=off
kex=X25519

2025-07-28T09:48:03.967Z DEBUG main_loop: warp::warp::connectivity_check: Trace status: Ok(TraceResult { metal_id: "71f1389", colo: "FRA", ip: ************** })
2025-07-28T09:48:03.967Z  INFO main_loop: warp::warp_service: WARP status: Connecting(ValidatingDnsConfiguration)
2025-07-28T09:48:03.967Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Connecting(ValidatingDnsConfiguration)
2025-07-28T09:48:03.967Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Connecting(ValidatingDnsConfiguration)
2025-07-28T09:48:03.967Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T09:48:03.967Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=357.671µs
2025-07-28T09:48:03.975Z DEBUG main_loop: connectivity_check: Resolved connectivity.cloudflareclient.com to [2606:4700:7::a29f:8941, 2606:4700:7::a29f:8a41, **************, **************]
2025-07-28T09:48:03.975Z DEBUG main_loop: warp::warp::warp_connection: Connect finished
2025-07-28T09:48:03.975Z DEBUG main_loop: warp::warp::warp_connection: start_status=Ok(())
2025-07-28T09:48:03.975Z DEBUG main_loop: warp::warp::controller: self.warp future resolved
2025-07-28T09:48:03.975Z  INFO main_loop: warp::warp_service: WARP status: Connected
2025-07-28T09:48:03.976Z DEBUG actor_statistics::handler: Recording gauge into histogram context=RecordIntoHistogramInfo { gauge_key: KeyName("conn_attempts_current"), histogram_key: KeyName("conn_attempts_all"), component: "happy_eyeballs" }
2025-07-28T09:48:03.976Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="tunnel_taskset_errors_fut"
2025-07-28T09:48:03.976Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="tunnel_taskset_errors_fut" elapsed=213.456µs
2025-07-28T09:48:03.976Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="status_change"
2025-07-28T09:48:03.976Z DEBUG main_loop: warp::warp_service::ipc_handlers: Sending IPC status update: Connected
2025-07-28T09:48:03.976Z DEBUG main_loop: warp::warp_service::ipc_handlers: Ipc Broadcast ResponseStatus: Connected
2025-07-28T09:48:03.976Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="status_change" elapsed=467.679µs
2025-07-28T09:48:06.140Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="main_loop_compat_rx"
2025-07-28T09:48:06.140Z DEBUG main_loop:handle_update{update=NetworkInfoChanged}: warp::warp_service::actor_compat: Handling network status change old_info=IPv4: [eth0; **********; Ethernet; Gateway: Some(**********)], DNS servers:,  127.0.0.11:53
 new_info=IPv4: [eth0; **********; Ethernet; Gateway: Some(**********)], DNS servers:,  *********:53,  *********:53

2025-07-28T09:48:06.140Z DEBUG main_loop:handle_update{update=NetworkInfoChanged}: warp::warp::proxying_dns: Reapplying DNS settings when the list of DNS servers contains ourselves new_info.dns_servers=[*********:53, *********:53] ADVERTISED_DNS_TARGETS=[*********, *********, ::ffff:*********, ::ffff:*********]
2025-07-28T09:48:06.140Z  INFO main_loop:handle_update{update=NetworkInfoChanged}: warp::warp::proxying_dns: Reinforcing old DNS settings old_info.dns_servers=[127.0.0.11:53] new_info.dns_servers=[*********:53, *********:53]
2025-07-28T09:48:06.140Z DEBUG main_loop:handle_update{update=NetworkInfoChanged}: warp::warp::dns_recovery::unix: Reverting DNS settings old_dns=RestoreDNS { action: OverwriteResolv([35, 32, 71, 101, 110, 101, 114, 97, 116, 101, 100, 32, 98, 121, 32, 68, 111, 99, 107, 101, 114, 32, 69, 110, 103, 105, 110, 101, 46, 10, 35, 32, 84, 104, 105, 115, 32, 102, 105, 108, 101, 32, 99, 97, 110, 32, 98, 101, 32, 101, 100, 105, 116, 101, 100, 59, 32, 68, 111, 99, 107, 101, 114, 32, 69, 110, 103, 105, 110, 101, 32, 119, 105, 108, 108, 32, 110, 111, 116, 32, 109, 97, 107, 101, 32, 102, 117, 114, 116, 104, 101, 114, 32, 99, 104, 97, 110, 103, 101, 115, 32, 111, 110, 99, 101, 32, 105, 116, 10, 35, 32, 104, 97, 115, 32, 98, 101, 101, 110, 32, 109, 111, 100, 105, 102, 105, 101, 100, 46, 10, 10, 110, 97, 109, 101, 115, 101, 114, 118, 101, 114, 32, 49, 50, 55, 46, 48, 46, 48, 46, 49, 49, 10, 111, 112, 116, 105, 111, 110, 115, 32, 110, 100, 111, 116, 115, 58, 48, 10, 10, 35, 32, 66, 97, 115, 101, 100, 32, 111, 110, 32, 104, 111, 115, 116, 32, 102, 105, 108, 101, 58, 32, 39, 47, 101, 116, 99, 47, 114, 101, 115, 111, 108, 118, 46, 99, 111, 110, 102, 39, 32, 40, 105, 110, 116, 101, 114, 110, 97, 108, 32, 114, 101, 115, 111, 108, 118, 101, 114, 41, 10, 35, 32, 69, 120, 116, 83, 101, 114, 118, 101, 114, 115, 58, 32, 91, 104, 111, 115, 116, 40, 52, 54, 46, 51, 56, 46, 50, 50, 53, 46, 50, 51, 48, 41, 32, 104, 111, 115, 116, 40, 52, 54, 46, 51, 56, 46, 50, 53, 50, 46, 50, 51, 48, 41, 93, 10, 35, 32, 79, 118, 101, 114, 114, 105, 100, 101, 115, 58, 32, 91, 93, 10, 35, 32, 79, 112, 116, 105, 111, 110, 32, 110, 100, 111, 116, 115, 32, 102, 114, 111, 109, 58, 32, 105, 110, 116, 101, 114, 110, 97, 108, 10]), resolv_conf_owner: Unknown, os_config_owner: File, previous_nameservers: Some([127.0.0.11]), warp_nameservers: [*********, *********, ::ffff:*********, ::ffff:*********] }
2025-07-28T09:48:06.142Z  WARN main_loop:handle_update{update=NetworkInfoChanged}: network_info::linux::dns_manager: Failed to notify system DNS manager of change. DNS may be broken if systemd-resolved is used owner=Unknown os_config_owner=File err=Failed to restart systemd-resolved

Caused by:
    org.freedesktop.DBus.Error.ServiceUnknown: The name org.freedesktop.systemd1 was not provided by any .service files
2025-07-28T09:48:06.142Z DEBUG main_loop:handle_update{update=NetworkInfoChanged}: warp::warp::dns_recovery::unix: DNS settings reverted (<resolv.conf restored>)
2025-07-28T09:48:06.142Z DEBUG main_loop:handle_update{update=NetworkInfoChanged}: warp::warp::dns_recovery::unix: Applying DNS settings [*********, *********, ::ffff:*********, ::ffff:*********]
2025-07-28T09:48:06.143Z DEBUG main_loop:handle_update{update=NetworkInfoChanged}: network_info::linux::dns_manager: detected system dns manager file_owner=Unknown os_configuration_owner=File
2025-07-28T09:48:06.149Z DEBUG main_loop:handle_update{update=NetworkInfoChanged}: warp::warp::proxying_dns: Skip notifying DNS changes as servers was empty or only contained our own advertised IPs. servers=[*********:53, *********:53] ADVERTISED_DNS_TARGETS=[*********, *********, ::ffff:*********, ::ffff:*********]
2025-07-28T09:48:06.149Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="main_loop_compat_rx" elapsed=8.903582ms
2025-07-28T09:48:06.340Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:48:07.248Z DEBUG main_loop:handle_command: warp::warp_service::actor_compat: Force-fetching registration config id=ecd6f5b1-04e9-4394-92f6-015127a8c2e8
2025-07-28T09:48:07.248Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="main_loop_compat_rx"
2025-07-28T09:48:07.248Z DEBUG main_loop:handle_command: warp_api_client::executor: Spawning API request for GetRegistrationConfig request_id=fe09a31a-f5a5-4bc3-9763-e663353d0284
2025-07-28T09:48:07.248Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="main_loop_compat_rx" elapsed=403.477µs
2025-07-28T09:48:07.248Z DEBUG warp_api_client::apis::registration: Getting registration registration_id=Client(ConsumerRegistrationId(ecd6f5b1-04e9-4394-92f6-015127a8c2e8))
2025-07-28T09:48:07.248Z DEBUG warp_api_client::client: Sending API request GET https://api.cloudflareclient.com.//v0/reg/ecd6f5b1-04e9-4394-92f6-015127a8c2e8
2025-07-28T09:48:07.299Z DEBUG warp_api_endpoints::payloads: Warp endpoint override ports ports=[443, 500, 1701, 4500, 4443, 8443, 8095]
2025-07-28T09:48:07.299Z DEBUG main_loop: warp_api_client::executor: Returning API response for GetRegistrationConfig request_id=fe09a31a-f5a5-4bc3-9763-e663353d0284
2025-07-28T09:48:07.300Z DEBUG run: warp_settings::manager: LayerManager update: NetworkPolicy(Some(NetworkPolicySettings { onboarding: None, operation_mode: None, disable_auto_fallback: None, fallback_domains: None, proxy_port: None, split_config: None, gateway_id: None, support_url: None, allow_mode_switch: None, switch_locked: None, auto_connect: None, captive_portal: None, organization: None, allow_updates: None, allowed_to_leave: None, profile_id: None, lan_allow_settings: Some(LanAllowSettings { minutes: None, subnet_size: SubnetSize(24) }), warp_tunnel_protocol: Some(Masque), masque_settings: None, register_interface_ip_with_dns: None, anycast_split_ips: Some(AnycastSplitIps { always_exclude: [*************, 2606:4700:102::3], always_include: [*************, 2606:4700:102::4] }), firewall_scope: None, sccm_vpn_boundary_support: None, enable_post_quantum: None }))
2025-07-28T09:48:07.300Z DEBUG utilities::tokio::select_watcher: Entering: [main loop] arm="api_responses"
2025-07-28T09:48:07.300Z DEBUG main_loop: warp::warp_service::api_handlers: Registration updated current_registration=Registered(reg=ecd6f5b1-04e9-4394-92f6-015127a8c2e8) disposition=CheckAndUpdate
2025-07-28T09:48:07.300Z DEBUG main_loop: warp::warp_service::api_handlers: Registration is up to date, updating refresh time
2025-07-28T09:48:07.300Z DEBUG main_loop: warp::warp_service::api_handlers: Registration refresh time updated 0.00 hours old, refresh at 2025-07-29 9:48:07.299431001 +00:00:00
Registration: ecd6f5b1-04e9-4394-92f6-015127a8c2e8
Device Identifiers: System User
Auth Method: Consumer
Public key: 3059301306072a8648ce3d020106082a8648ce3d0301070342000499b56ceaaefc7ce8cd73367566360d1851dc5caaa264437b5129d4cf5e803cd43a9aebdd5178951fce8a711135388244fbf955ca81f56f181eee431745324208
Interface: WarpEndpoint { v4: **********, v6: 2606:4700:110:858c:888:a5bc:a05a:360d }
Endpoints: [WarpSocketAddrPair { v4: *************:443, v6: [2606:4700:103::2]:443 }, WarpSocketAddrPair { v4: *************:500, v6: [2606:4700:103::2]:500 }, WarpSocketAddrPair { v4: *************:1701, v6: [2606:4700:103::2]:1701 }, WarpSocketAddrPair { v4: *************:4500, v6: [2606:4700:103::2]:4500 }, WarpSocketAddrPair { v4: *************:4443, v6: [2606:4700:103::2]:4443 }, WarpSocketAddrPair { v4: *************:8443, v6: [2606:4700:103::2]:8443 }, WarpSocketAddrPair { v4: *************:8095, v6: [2606:4700:103::2]:8095 }]
Subnet CIDRS: None

Account: Free { id: AccountId(5ab76f72-c06d-4187-893e-a6d6f0d71cba), license: "Ub56i2C0-S51U7Bi4-580Sk7Iq" }

2025-07-28T09:48:07.300Z  INFO main_loop: warp_storage::registration::registration_storage: Saving registration to active cache config_name=None username="WARPSecret"
2025-07-28T09:48:07.301Z DEBUG utilities::tokio::select_watcher: Exiting: [main loop] arm="api_responses" elapsed=1.525393ms
2025-07-28T09:48:08.127Z  INFO actor_emergency_disconnect::handler: Polling API for latest emergency disconnect state
2025-07-28T09:48:11.584Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:48:13.127Z DEBUG actor_root_ca::handler: Starting root-ca actor loop
2025-07-28T09:48:13.128Z DEBUG handle_registration_changed: root_ca::store: Done installing certificate(s) total=0
2025-07-28T09:48:13.128Z DEBUG handle_registration_changed: root_ca::store: Done installing certificate(s) total=0
2025-07-28T09:48:18.749Z DEBUG slog: got a response for stream_id=8, headers=[":status: 200", "cf-team: 28f207f1bc0000d665f153c400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:48:33.600Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:48:33.748Z DEBUG slog: got a response for stream_id=12, headers=[":status: 200", "cf-team: 28f2082c530000d665f1639400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:48:38.976Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:48:48.748Z DEBUG slog: got a response for stream_id=16, headers=[":status: 200", "cf-team: 28f20866eb0000d665f170a400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:49:03.748Z DEBUG slog: got a response for stream_id=20, headers=[":status: 200", "cf-team: 28f208a1830000d665f1862400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:49:06.880Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:49:13.024Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:49:18.748Z DEBUG slog: got a response for stream_id=24, headers=[":status: 200", "cf-team: 28f208dc1b0000d665f1940400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:49:28.640Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:49:33.749Z DEBUG slog: got a response for stream_id=28, headers=[":status: 200", "cf-team: 28f20916b40000d665f1a2e400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:49:33.760Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:49:48.748Z DEBUG slog: got a response for stream_id=32, headers=[":status: 200", "cf-team: 28f209514b0000d665f1bd1400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:49:49.376Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:49:55.264Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:49:58.126Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 1160 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 30 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.04422462000000004, count: 1160 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 289.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 139.0, count: 130 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.********* }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-07-28T09:49:58.126Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 1160 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 30 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.04422462000000004, count: 1160 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 289.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 139.0, count: 130 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.********* }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-07-28T09:49:58.190Z DEBUG warp_api_client::client: Method: POST, Path: v0/ecd6f5b1-04e9-4394-92f6-015127a8c2e8/observe_events, StructuredLogsEndpoint { device_id: "ecd6f5b1-04e9-4394-92f6-015127a8c2e8", logs: Array [Object {"event": String("UserToggledV0"), "toggle": String("on"), "warp_colo": String("none"), "warp_metal": String("none"), "account_name": String("\"\""), "account_id": String("\"5ab76f72c06d4187893ea6d6f0d71cba\""), "hostname": String("\"a2bf1074869f\""), "serial_number": String("\"\""), "version": Number(0), "client_sample_rate": Number(1), "app_version": String("2025.5.943.0"), "os": String("Linux"), "os_version": String("6.1.81"), "timestamp": Number(****************), "session_id": String("986306b1-c6d9-4402-a1d4-0e3c1fe2dc29")}] }
2025-07-28T09:49:58.584Z ERROR warp_api_client::client: Failed API Request endpoint=StructuredLogsEndpoint { device_id: "ecd6f5b1-04e9-4394-92f6-015127a8c2e8", logs: Array [Object {"event": String("UserToggledV0"), "toggle": String("on"), "warp_colo": String("none"), "warp_metal": String("none"), "account_name": String("\"\""), "account_id": String("\"5ab76f72c06d4187893ea6d6f0d71cba\""), "hostname": String("\"a2bf1074869f\""), "serial_number": String("\"\""), "version": Number(0), "client_sample_rate": Number(1), "app_version": String("2025.5.943.0"), "os": String("Linux"), "os_version": String("6.1.81"), "timestamp": Number(****************), "session_id": String("986306b1-c6d9-4402-a1d4-0e3c1fe2dc29")}] } err=ApiFailure(500, ApiErrors { errors: [], other: {"messages": Array [], "success": Bool(false), "result": Null} })
2025-07-28T09:49:58.585Z ERROR warp_api_client::client: API request error ApiFailure(500, ApiErrors { errors: [], other: {"messages": Array [], "success": Bool(false), "result": Null} }), retrying in 1.034657679s
2025-07-28T09:49:59.983Z ERROR warp_api_client::client: Failed API Request endpoint=StructuredLogsEndpoint { device_id: "ecd6f5b1-04e9-4394-92f6-015127a8c2e8", logs: Array [Object {"event": String("UserToggledV0"), "toggle": String("on"), "warp_colo": String("none"), "warp_metal": String("none"), "account_name": String("\"\""), "account_id": String("\"5ab76f72c06d4187893ea6d6f0d71cba\""), "hostname": String("\"a2bf1074869f\""), "serial_number": String("\"\""), "version": Number(0), "client_sample_rate": Number(1), "app_version": String("2025.5.943.0"), "os": String("Linux"), "os_version": String("6.1.81"), "timestamp": Number(****************), "session_id": String("986306b1-c6d9-4402-a1d4-0e3c1fe2dc29")}] } err=ApiFailure(500, ApiErrors { errors: [], other: {"result": Null, "messages": Array [], "success": Bool(false)} })
2025-07-28T09:49:59.983Z ERROR warp_api_client::client: API request error ApiFailure(500, ApiErrors { errors: [], other: {"result": Null, "messages": Array [], "success": Bool(false)} }), retrying in 2.128237069s
2025-07-28T09:50:02.433Z ERROR warp_api_client::client: Failed API Request endpoint=StructuredLogsEndpoint { device_id: "ecd6f5b1-04e9-4394-92f6-015127a8c2e8", logs: Array [Object {"event": String("UserToggledV0"), "toggle": String("on"), "warp_colo": String("none"), "warp_metal": String("none"), "account_name": String("\"\""), "account_id": String("\"5ab76f72c06d4187893ea6d6f0d71cba\""), "hostname": String("\"a2bf1074869f\""), "serial_number": String("\"\""), "version": Number(0), "client_sample_rate": Number(1), "app_version": String("2025.5.943.0"), "os": String("Linux"), "os_version": String("6.1.81"), "timestamp": Number(****************), "session_id": String("986306b1-c6d9-4402-a1d4-0e3c1fe2dc29")}] } err=ApiFailure(500, ApiErrors { errors: [], other: {"success": Bool(false), "result": Null, "messages": Array []} })
2025-07-28T09:50:02.433Z ERROR warp_api_client::client: API request error ApiFailure(500, ApiErrors { errors: [], other: {"success": Bool(false), "result": Null, "messages": Array []} }), retrying in 4.222774997s
2025-07-28T09:50:03.748Z DEBUG slog: got a response for stream_id=36, headers=[":status: 200", "cf-team: 28f2098be30000d665f1df2400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:50:03.802Z DEBUG tunnel_loop{protocol="masque" con_id="f6ae5963007bd58af823c5a95b63b4e784cc2068"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 5, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-07-28T09:50:06.974Z ERROR warp_api_client::client: Failed API Request endpoint=StructuredLogsEndpoint { device_id: "ecd6f5b1-04e9-4394-92f6-015127a8c2e8", logs: Array [Object {"event": String("UserToggledV0"), "toggle": String("on"), "warp_colo": String("none"), "warp_metal": String("none"), "account_name": String("\"\""), "account_id": String("\"5ab76f72c06d4187893ea6d6f0d71cba\""), "hostname": String("\"a2bf1074869f\""), "serial_number": String("\"\""), "version": Number(0), "client_sample_rate": Number(1), "app_version": String("2025.5.943.0"), "os": String("Linux"), "os_version": String("6.1.81"), "timestamp": Number(****************), "session_id": String("986306b1-c6d9-4402-a1d4-0e3c1fe2dc29")}] } err=ApiFailure(500, ApiErrors { errors: [], other: {"result": Null, "messages": Array [], "success": Bool(false)} })
2025-07-28T09:50:18.748Z DEBUG slog: got a response for stream_id=40, headers=[":status: 200", "cf-team: 28f209c67c0000d665f1f74400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:50:22.400Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:50:28.800Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:50:33.749Z DEBUG slog: got a response for stream_id=44, headers=[":status: 200", "cf-team: 28f20a01140000d665f20a2400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:50:44.416Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:50:48.748Z DEBUG slog: got a response for stream_id=48, headers=[":status: 200", "cf-team: 28f20a3bab0000d665f21b4400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:50:50.304Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:51:03.748Z DEBUG slog: got a response for stream_id=52, headers=[":status: 200", "cf-team: 28f20a76430000d665f2362400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:51:04.896Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:51:10.016Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:51:18.748Z DEBUG slog: got a response for stream_id=56, headers=[":status: 200", "cf-team: 28f20ab0db0000d665f24b2400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:51:25.632Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:51:31.264Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:51:33.748Z DEBUG slog: got a response for stream_id=60, headers=[":status: 200", "cf-team: 28f20aeb730000d665f2621400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:51:46.880Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:51:48.748Z DEBUG slog: got a response for stream_id=64, headers=[":status: 200", "cf-team: 28f20b260b0000d665f2727400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:51:53.280Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:51:58.126Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 55 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 2360 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.09136271999999998, count: 2360 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 241.0, count: 280 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 289.0, count: 14 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.006921655 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-07-28T09:51:58.126Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 55 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 2360 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.09136271999999998, count: 2360 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 241.0, count: 280 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 289.0, count: 14 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.006921655 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-07-28T09:52:03.748Z DEBUG slog: got a response for stream_id=68, headers=[":status: 200", "cf-team: 28f20b60a30000d665f28a1400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:52:03.802Z DEBUG tunnel_loop{protocol="masque" con_id="f6ae5963007bd58af823c5a95b63b4e784cc2068"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 6, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-07-28T09:52:13.760Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:52:18.748Z DEBUG slog: got a response for stream_id=72, headers=[":status: 200", "cf-team: 28f20b9b3b0000d665f2a55400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:52:18.880Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:52:33.748Z DEBUG slog: got a response for stream_id=76, headers=[":status: 200", "cf-team: 28f20bd5d30000d665f2ba8400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:52:34.496Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:52:40.640Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:52:48.748Z DEBUG slog: got a response for stream_id=80, headers=[":status: 200", "cf-team: 28f20c106b0000d665f2cc1400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:52:56.260Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:53:02.656Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:53:03.748Z DEBUG slog: got a response for stream_id=84, headers=[":status: 200", "cf-team: 28f20c4b040000d665f2e67400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:53:18.272Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:53:18.748Z DEBUG slog: got a response for stream_id=88, headers=[":status: 200", "cf-team: 28f20c859b0000d665f2ffe400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:53:23.392Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:53:33.748Z DEBUG slog: got a response for stream_id=92, headers=[":status: 200", "cf-team: 28f20cc0330000d665f3204400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:53:39.008Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:53:45.152Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:53:48.749Z DEBUG slog: got a response for stream_id=96, headers=[":status: 200", "cf-team: 28f20cfacc0000d665f35af400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:53:58.126Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 3560 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 81 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 351.0, count: 412 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 289.0, count: 14 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.1379030690000002, count: 3560 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.999191652 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-07-28T09:53:58.126Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 3560 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 81 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 351.0, count: 412 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 289.0, count: 14 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.1379030690000002, count: 3560 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.999191652 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-07-28T09:54:00.768Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:54:03.748Z DEBUG slog: got a response for stream_id=100, headers=[":status: 200", "cf-team: 28f20d35640000d665f3b52400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:54:03.802Z DEBUG tunnel_loop{protocol="masque" con_id="f6ae5963007bd58af823c5a95b63b4e784cc2068"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 7, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-07-28T09:54:06.912Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:54:18.748Z DEBUG slog: got a response for stream_id=104, headers=[":status: 200", "cf-team: 28f20d6ffb0000d665f4209400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:54:33.748Z DEBUG slog: got a response for stream_id=108, headers=[":status: 200", "cf-team: 28f20daa940000d665f4ba0400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:54:34.304Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:54:39.424Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:54:48.748Z DEBUG slog: got a response for stream_id=112, headers=[":status: 200", "cf-team: 28f20de52b0000d665f567c400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:55:03.749Z DEBUG slog: got a response for stream_id=116, headers=[":status: 200", "cf-team: 28f20e1fc40000d665f5a21400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:55:07.328Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:55:13.216Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:55:18.762Z DEBUG slog: got a response for stream_id=120, headers=[":status: 200", "cf-team: 28f20e5a690000d665f5c36400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:55:33.749Z DEBUG slog: got a response for stream_id=124, headers=[":status: 200", "cf-team: 28f20e94f40000d665f5f31400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:55:42.144Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:55:47.520Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:55:48.747Z DEBUG slog: got a response for stream_id=128, headers=[":status: 200", "cf-team: 28f20ecf8b0000d665f651f400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:55:58.125Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 4759 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 107 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 289.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 449.0, count: 544 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.18522593099999965, count: 4759 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.006183271 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-07-28T09:55:58.126Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 4759 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 107 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 289.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 449.0, count: 544 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.18522593099999965, count: 4759 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.006183271 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-07-28T09:56:03.748Z DEBUG slog: got a response for stream_id=132, headers=[":status: 200", "cf-team: 28f20f0a230000d665f6a56400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:56:03.802Z DEBUG tunnel_loop{protocol="masque" con_id="f6ae5963007bd58af823c5a95b63b4e784cc2068"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 7, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-07-28T09:56:14.912Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:56:18.749Z DEBUG slog: got a response for stream_id=136, headers=[":status: 200", "cf-team: 28f20f44bc0000d665f6fbb400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:56:21.312Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:56:33.748Z DEBUG slog: got a response for stream_id=140, headers=[":status: 200", "cf-team: 28f20f7f530000d665f7100400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:56:48.748Z DEBUG slog: got a response for stream_id=144, headers=[":status: 200", "cf-team: 28f20fb9eb0000d665f7553400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:56:49.728Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:56:55.616Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:57:03.748Z DEBUG slog: got a response for stream_id=148, headers=[":status: 200", "cf-team: 28f20ff4830000d665f777a400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:57:18.748Z DEBUG slog: got a response for stream_id=152, headers=[":status: 200", "cf-team: 28f2102f1c0000d665f79d8400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:57:24.544Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:57:31.456Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:57:33.747Z DEBUG slog: got a response for stream_id=156, headers=[":status: 200", "cf-team: 28f21069b30000d665f7b52400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:57:48.748Z DEBUG slog: got a response for stream_id=160, headers=[":status: 200", "cf-team: 28f210a44b0000d665f7ea2400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:57:58.125Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 5959 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 134 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 289.0, count: 14 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.23363121199999975, count: 5959 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 565.0, count: 690 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.006526843 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-07-28T09:57:58.125Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 5959 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 134 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 289.0, count: 14 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.23363121199999975, count: 5959 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 565.0, count: 690 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.006526843 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-07-28T09:57:59.360Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:58:03.748Z DEBUG slog: got a response for stream_id=164, headers=[":status: 200", "cf-team: 28f210dee30000d665f7ffd400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:58:03.802Z DEBUG tunnel_loop{protocol="masque" con_id="f6ae5963007bd58af823c5a95b63b4e784cc2068"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 8, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-07-28T09:58:04.736Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:58:18.748Z DEBUG slog: got a response for stream_id=168, headers=[":status: 200", "cf-team: 28f211197b0000d665f80f5400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:58:32.128Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:58:33.748Z DEBUG slog: got a response for stream_id=172, headers=[":status: 200", "cf-team: 28f21154140000d665f8289400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:58:38.784Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:58:48.747Z DEBUG slog: got a response for stream_id=176, headers=[":status: 200", "cf-team: 28f2118eab0000d665f83a0400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:59:03.748Z DEBUG slog: got a response for stream_id=180, headers=[":status: 200", "cf-team: 28f211c9440000d665f8501400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:59:06.944Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:59:12.064Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:59:18.749Z DEBUG slog: got a response for stream_id=184, headers=[":status: 200", "cf-team: 28f21203dc0000d665f87f2400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:59:33.748Z DEBUG slog: got a response for stream_id=188, headers=[":status: 200", "cf-team: 28f2123e730000d665f8a7f400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:59:39.712Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T09:59:47.392Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T09:59:48.748Z DEBUG slog: got a response for stream_id=192, headers=[":status: 200", "cf-team: 28f212790c0000d665f8c9a400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T09:59:58.126Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 7159 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 156 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 672.0, count: 850 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.28100731499999926, count: 7159 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 289.0, count: 14 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.993326164 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-07-28T09:59:58.126Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 7159 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 156 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 672.0, count: 850 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.28100731499999926, count: 7159 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 289.0, count: 14 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.993326164 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-07-28T10:00:03.748Z DEBUG slog: got a response for stream_id=196, headers=[":status: 200", "cf-team: 28f212b3a30000d665f8e54400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:00:03.802Z DEBUG tunnel_loop{protocol="masque" con_id="f6ae5963007bd58af823c5a95b63b4e784cc2068"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 8, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-07-28T10:00:10.432Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:00:16.320Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T10:00:18.748Z DEBUG slog: got a response for stream_id=200, headers=[":status: 200", "cf-team: 28f212ee3b0000d665f8f91400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:00:33.748Z DEBUG slog: got a response for stream_id=204, headers=[":status: 200", "cf-team: 28f21328d40000d665f90d7400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:00:39.104Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:00:44.480Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T10:00:48.748Z DEBUG slog: got a response for stream_id=208, headers=[":status: 200", "cf-team: 28f213636c0000d665f923f400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:01:03.748Z DEBUG slog: got a response for stream_id=212, headers=[":status: 200", "cf-team: 28f2139e030000d665f93bf400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:01:07.776Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:01:13.408Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T10:01:18.748Z DEBUG slog: got a response for stream_id=216, headers=[":status: 200", "cf-team: 28f213d89b0000d665f94fa400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:01:33.748Z DEBUG slog: got a response for stream_id=220, headers=[":status: 200", "cf-team: 28f21413330000d665f9660400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:01:36.448Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:01:41.568Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T10:01:48.748Z DEBUG slog: got a response for stream_id=224, headers=[":status: 200", "cf-team: 28f2144dcc0000d665f97ca400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:01:58.127Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 8359 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 186 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 289.0, count: 14 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.3294406090000014, count: 8359 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 809.0, count: 988 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.993733069 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-07-28T10:01:58.127Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 8359 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 186 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 289.0, count: 14 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.3294406090000014, count: 8359 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 809.0, count: 988 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.993733069 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-07-28T10:02:03.076Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:02:03.748Z DEBUG slog: got a response for stream_id=228, headers=[":status: 200", "cf-team: 28f21488640000d665f98c2400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:02:03.802Z DEBUG tunnel_loop{protocol="masque" con_id="f6ae5963007bd58af823c5a95b63b4e784cc2068"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 8, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-07-28T10:02:08.960Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T10:02:18.748Z DEBUG slog: got a response for stream_id=232, headers=[":status: 200", "cf-team: 28f214c2fb0000d665f9a27400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:02:31.744Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:02:33.748Z DEBUG slog: got a response for stream_id=236, headers=[":status: 200", "cf-team: 28f214fd930000d665f9b74400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:02:37.376Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T10:02:48.748Z DEBUG slog: got a response for stream_id=240, headers=[":status: 200", "cf-team: 28f215382b0000d665f9ce7400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:03:00.416Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:03:03.748Z DEBUG slog: got a response for stream_id=244, headers=[":status: 200", "cf-team: 28f21572c30000d665f9df1400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:03:07.584Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T10:03:18.747Z DEBUG slog: got a response for stream_id=248, headers=[":status: 200", "cf-team: 28f215ad5b0000d665f9f90400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:03:29.088Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:03:33.748Z DEBUG slog: got a response for stream_id=252, headers=[":status: 200", "cf-team: 28f215e7f30000d665fa159400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:03:35.236Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T10:03:48.748Z DEBUG slog: got a response for stream_id=256, headers=[":status: 200", "cf-team: 28f216228b0000d665fa277400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:03:57.760Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:03:58.126Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 9559 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 214 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.37626158800000137, count: 9559 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 899.0, count: 1124 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 289.0, count: 14 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.000454781 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-07-28T10:03:58.126Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 9559 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 214 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.37626158800000137, count: 9559 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 899.0, count: 1124 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 289.0, count: 14 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.000454781 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-07-28T10:04:02.880Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T10:04:03.747Z DEBUG slog: got a response for stream_id=260, headers=[":status: 200", "cf-team: 28f2165d230000d665fa3db400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:04:03.803Z DEBUG tunnel_loop{protocol="masque" con_id="f6ae5963007bd58af823c5a95b63b4e784cc2068"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 8, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-07-28T10:04:18.748Z DEBUG slog: got a response for stream_id=264, headers=[":status: 200", "cf-team: 28f21697bc0000d665fa4ff400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:04:33.748Z DEBUG slog: got a response for stream_id=268, headers=[":status: 200", "cf-team: 28f216d2540000d665fa5e4400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:04:36.928Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:04:42.816Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T10:04:48.748Z DEBUG slog: got a response for stream_id=272, headers=[":status: 200", "cf-team: 28f2170ceb0000d665fa709400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:05:03.748Z DEBUG slog: got a response for stream_id=276, headers=[":status: 200", "cf-team: 28f21747830000d665fa894400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:05:17.632Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:05:18.747Z DEBUG slog: got a response for stream_id=280, headers=[":status: 200", "cf-team: 28f217821b0000d665fa9d1400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:05:23.008Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T10:05:33.748Z DEBUG slog: got a response for stream_id=284, headers=[":status: 200", "cf-team: 28f217bcb40000d665faaf7400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:05:48.748Z DEBUG slog: got a response for stream_id=288, headers=[":status: 200", "cf-team: 28f217f74c0000d665fabfd400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:05:58.126Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 246 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 10759 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 289.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 994.0, count: 1264 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.42355121499999954, count: 10759 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.999496644 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-07-28T10:05:58.126Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 246 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 10759 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 289.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 994.0, count: 1264 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.42355121499999954, count: 10759 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.999496644 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-07-28T10:05:58.592Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:06:03.712Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T10:06:03.748Z DEBUG slog: got a response for stream_id=292, headers=[":status: 200", "cf-team: 28f21831e30000d665fade7400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:06:03.801Z DEBUG tunnel_loop{protocol="masque" con_id="f6ae5963007bd58af823c5a95b63b4e784cc2068"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 9, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-07-28T10:06:18.748Z DEBUG slog: got a response for stream_id=296, headers=[":status: 200", "cf-team: 28f2186c7b0000d665fafa9400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:06:33.749Z DEBUG slog: got a response for stream_id=300, headers=[":status: 200", "cf-team: 28f218a7140000d665fb251400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:06:39.552Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:06:45.188Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T10:06:48.748Z DEBUG slog: got a response for stream_id=304, headers=[":status: 200", "cf-team: 28f218e1ac0000d665fb461400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:07:03.748Z DEBUG slog: got a response for stream_id=308, headers=[":status: 200", "cf-team: 28f2191c430000d665fb5e2400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:07:18.748Z DEBUG slog: got a response for stream_id=312, headers=[":status: 200", "cf-team: 28f21956db0000d665fb71d400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:07:20.512Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:07:25.888Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T10:07:33.748Z DEBUG slog: got a response for stream_id=316, headers=[":status: 200", "cf-team: 28f21991740000d665fb833400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:07:48.748Z DEBUG slog: got a response for stream_id=320, headers=[":status: 200", "cf-team: 28f219cc0b0000d665fb96a400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:07:58.126Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 11959 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 277 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 289.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 1131.0, count: 1404 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.46927065699999954, count: 11959 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.006734245 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-07-28T10:07:58.127Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 11959 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 277 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 289.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 1131.0, count: 1404 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.46927065699999954, count: 11959 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.006734245 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-07-28T10:08:01.472Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:08:03.748Z DEBUG slog: got a response for stream_id=324, headers=[":status: 200", "cf-team: 28f21a06a30000d665fbc78400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:08:03.802Z DEBUG tunnel_loop{protocol="masque" con_id="f6ae5963007bd58af823c5a95b63b4e784cc2068"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 9, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-07-28T10:08:06.592Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T10:08:18.748Z DEBUG slog: got a response for stream_id=328, headers=[":status: 200", "cf-team: 28f21a413b0000d665fbebf400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:08:33.748Z DEBUG slog: got a response for stream_id=332, headers=[":status: 200", "cf-team: 28f21a7bd30000d665fc130400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:08:42.432Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:08:47.808Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T10:08:48.748Z DEBUG slog: got a response for stream_id=336, headers=[":status: 200", "cf-team: 28f21ab66b0000d665fc4b5400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:09:03.748Z DEBUG slog: got a response for stream_id=340, headers=[":status: 200", "cf-team: 28f21af1030000d665fc761400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:09:18.748Z DEBUG slog: got a response for stream_id=344, headers=[":status: 200", "cf-team: 28f21b2b9c0000d665fc985400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:09:23.392Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:09:29.280Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T10:09:33.748Z DEBUG slog: got a response for stream_id=348, headers=[":status: 200", "cf-team: 28f21b66330000d665fcbb4400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:09:48.748Z DEBUG slog: got a response for stream_id=352, headers=[":status: 200", "cf-team: 28f21ba0cb0000d665fcd42400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:09:50.016Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:09:55.392Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T10:09:58.125Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 300 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 13159 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }], summaries: [SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 1240.0, count: 1558 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 289.0, count: 14 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.5159002929999986, count: 13159 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.993311403999996 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-07-28T10:09:58.126Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 300 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 13159 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }], summaries: [SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 1240.0, count: 1558 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 289.0, count: 14 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.5159002929999986, count: 13159 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.993311403999996 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-07-28T10:10:03.748Z DEBUG slog: got a response for stream_id=356, headers=[":status: 200", "cf-team: 28f21bdb630000d665fce91400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:10:03.802Z DEBUG tunnel_loop{protocol="masque" con_id="f6ae5963007bd58af823c5a95b63b4e784cc2068"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 9, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-07-28T10:10:14.592Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:10:18.748Z DEBUG slog: got a response for stream_id=360, headers=[":status: 200", "cf-team: 28f21c15fb0000d665fd1dd400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:10:22.020Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T10:10:33.748Z DEBUG slog: got a response for stream_id=364, headers=[":status: 200", "cf-team: 28f21c50940000d665fd3e1400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:10:41.216Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:10:47.616Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T10:10:48.748Z DEBUG slog: got a response for stream_id=368, headers=[":status: 200", "cf-team: 28f21c8b2b0000d665fd5cd400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:11:03.748Z DEBUG slog: got a response for stream_id=372, headers=[":status: 200", "cf-team: 28f21cc5c30000d665fd74a400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:11:07.840Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:11:13.476Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T10:11:18.748Z DEBUG slog: got a response for stream_id=376, headers=[":status: 200", "cf-team: 28f21d005b0000d665fd956400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:11:32.416Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:11:33.749Z DEBUG slog: got a response for stream_id=380, headers=[":status: 200", "cf-team: 28f21d3af40000d665fdb5c400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:11:38.304Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T10:11:48.748Z DEBUG slog: got a response for stream_id=384, headers=[":status: 200", "cf-team: 28f21d758b0000d665fdd48400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:11:58.126Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 14359 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 330 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.5629394909999966, count: 14359 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 1341.0, count: 1690 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 289.0, count: 14 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.007034413 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-07-28T10:11:58.126Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 14359 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 330 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.5629394909999966, count: 14359 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 1341.0, count: 1690 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 289.0, count: 14 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.007034413 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-07-28T10:11:59.040Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:12:03.748Z DEBUG slog: got a response for stream_id=388, headers=[":status: 200", "cf-team: 28f21db0230000d665fdf80400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:12:03.802Z DEBUG tunnel_loop{protocol="masque" con_id="f6ae5963007bd58af823c5a95b63b4e784cc2068"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 9, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-07-28T10:12:04.416Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T10:12:18.748Z DEBUG slog: got a response for stream_id=392, headers=[":status: 200", "cf-team: 28f21deabb0000d665fe2c6400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:12:23.616Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:12:30.016Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T10:12:33.748Z DEBUG slog: got a response for stream_id=396, headers=[":status: 200", "cf-team: 28f21e25540000d665fe49c400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:12:48.748Z DEBUG slog: got a response for stream_id=400, headers=[":status: 200", "cf-team: 28f21e5feb0000d665fe647400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:12:50.240Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:12:55.616Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T10:13:03.750Z DEBUG slog: got a response for stream_id=404, headers=[":status: 200", "cf-team: 28f21e9a840000d665fe7c5400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:13:14.816Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:13:18.748Z DEBUG slog: got a response for stream_id=408, headers=[":status: 200", "cf-team: 28f21ed51c0000d665fe932400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:13:21.472Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T10:13:33.748Z DEBUG slog: got a response for stream_id=412, headers=[":status: 200", "cf-team: 28f21f0fb30000d665fea86400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:13:41.440Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:13:46.560Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T10:13:48.748Z DEBUG slog: got a response for stream_id=416, headers=[":status: 200", "cf-team: 28f21f4a4b0000d665fec5f400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:13:58.126Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 359 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 15559 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.6107315359999965, count: 15559 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 1453.0, count: 1826 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 289.0, count: 14 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.007837229 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-07-28T10:13:58.126Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 359 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 15559 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.6107315359999965, count: 15559 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 1453.0, count: 1826 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 289.0, count: 14 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.007837229 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-07-28T10:14:03.753Z DEBUG slog: got a response for stream_id=420, headers=[":status: 200", "cf-team: 28f21f84e80000d665fee25400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:14:03.802Z DEBUG tunnel_loop{protocol="masque" con_id="f6ae5963007bd58af823c5a95b63b4e784cc2068"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 9, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-07-28T10:14:06.016Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:14:11.396Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T10:14:18.749Z DEBUG slog: got a response for stream_id=424, headers=[":status: 200", "cf-team: 28f21fbf7c0000d665fefc2400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:14:30.592Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:14:33.748Z DEBUG slog: got a response for stream_id=428, headers=[":status: 200", "cf-team: 28f21ffa130000d665ff159400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:14:36.224Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T10:14:48.748Z DEBUG slog: got a response for stream_id=432, headers=[":status: 200", "cf-team: 28f22034ac0000d665ff2a5400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:14:55.172Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:15:01.824Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T10:15:03.748Z DEBUG slog: got a response for stream_id=436, headers=[":status: 200", "cf-team: 28f2206f430000d665ff419400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:15:18.748Z DEBUG slog: got a response for stream_id=440, headers=[":status: 200", "cf-team: 28f220a9db0000d665ff56a400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:15:21.792Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:15:27.424Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T10:15:33.748Z DEBUG slog: got a response for stream_id=444, headers=[":status: 200", "cf-team: 28f220e4730000d665ff6d6400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:15:46.368Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:15:48.748Z DEBUG slog: got a response for stream_id=448, headers=[":status: 200", "cf-team: 28f2211f0b0000d665ff81a400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:15:51.488Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T10:15:58.126Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 16759 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 388 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.6603093209999966, count: 16759 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 1559.0, count: 1962 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 289.0, count: 14 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.995006832 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-07-28T10:15:58.126Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 16759 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 388 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.6603093209999966, count: 16759 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 1559.0, count: 1962 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 289.0, count: 14 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.995006832 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-07-28T10:16:03.748Z DEBUG slog: got a response for stream_id=452, headers=[":status: 200", "cf-team: 28f22159a30000d665ff976400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:16:03.802Z DEBUG tunnel_loop{protocol="masque" con_id="f6ae5963007bd58af823c5a95b63b4e784cc2068"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 9, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-07-28T10:16:10.944Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:16:18.624Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T10:16:18.747Z DEBUG slog: got a response for stream_id=456, headers=[":status: 200", "cf-team: 28f221943b0000d665ffabf400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:16:33.748Z DEBUG slog: got a response for stream_id=460, headers=[":status: 200", "cf-team: 28f221ced40000d665ffc7a400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:16:37.568Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:16:43.460Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T10:16:48.748Z DEBUG slog: got a response for stream_id=464, headers=[":status: 200", "cf-team: 28f222096b0000d665ffe68400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:17:02.144Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:17:03.748Z DEBUG slog: got a response for stream_id=468, headers=[":status: 200", "cf-team: 28f22244030000d6650003a400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:17:07.520Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T10:17:18.747Z DEBUG slog: got a response for stream_id=472, headers=[":status: 200", "cf-team: 28f2227e9b0000d66500142400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:17:26.720Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:17:33.376Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T10:17:33.748Z DEBUG slog: got a response for stream_id=476, headers=[":status: 200", "cf-team: 28f222b9330000d66500280400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:17:48.748Z DEBUG slog: got a response for stream_id=480, headers=[":status: 200", "cf-team: 28f222f3cb0000d66500521400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:17:53.348Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:17:58.125Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 17959 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 419 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }], summaries: [SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 1654.0, count: 2096 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 289.0, count: 14 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.707781594999997, count: 17959 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.005983899 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-07-28T10:17:58.125Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 17959 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 419 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }], summaries: [SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 1654.0, count: 2096 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 289.0, count: 14 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.707781594999997, count: 17959 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.005983899 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-07-28T10:17:58.976Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T10:18:03.748Z DEBUG slog: got a response for stream_id=484, headers=[":status: 200", "cf-team: 28f2232e630000d66500803400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:18:03.801Z DEBUG tunnel_loop{protocol="masque" con_id="f6ae5963007bd58af823c5a95b63b4e784cc2068"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 9, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-07-28T10:18:17.920Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:18:18.748Z DEBUG slog: got a response for stream_id=488, headers=[":status: 200", "cf-team: 28f22368fb0000d66500a43400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:18:23.552Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T10:18:33.748Z DEBUG slog: got a response for stream_id=492, headers=[":status: 200", "cf-team: 28f223a3930000d66500b9f400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:18:42.496Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:18:48.128Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T10:18:48.748Z DEBUG slog: got a response for stream_id=496, headers=[":status: 200", "cf-team: 28f223de2b0000d66500d1d400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:19:03.748Z DEBUG slog: got a response for stream_id=500, headers=[":status: 200", "cf-team: 28f22418c30000d66500e74400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:19:07.072Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:19:14.500Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:19:14.502Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:19:18.748Z DEBUG slog: got a response for stream_id=504, headers=[":status: 200", "cf-team: 28f224535b0000d66500fdd400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:19:33.748Z DEBUG slog: got a response for stream_id=508, headers=[":status: 200", "cf-team: 28f2248df30000d665011af400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:19:44.192Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:19:48.748Z DEBUG slog: got a response for stream_id=512, headers=[":status: 200", "cf-team: 28f224c88b0000d665012fb400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:19:51.104Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T10:19:58.126Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 449 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 19159 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.7538802189999956, count: 19159 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 289.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 1748.0, count: 2232 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.993616403 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-07-28T10:19:58.126Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 449 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 19159 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.7538802189999956, count: 19159 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 289.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 1748.0, count: 2232 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 59.993616403 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-07-28T10:20:03.748Z DEBUG slog: got a response for stream_id=516, headers=[":status: 200", "cf-team: 28f22503230000d6650143b400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:20:03.802Z DEBUG tunnel_loop{protocol="masque" con_id="f6ae5963007bd58af823c5a95b63b4e784cc2068"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 9, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-07-28T10:20:18.748Z DEBUG slog: got a response for stream_id=520, headers=[":status: 200", "cf-team: 28f2253dbb0000d6650159d400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:20:20.800Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:20:26.944Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T10:20:33.748Z DEBUG slog: got a response for stream_id=524, headers=[":status: 200", "cf-team: 28f22578530000d66501729400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:20:48.748Z DEBUG slog: got a response for stream_id=528, headers=[":status: 200", "cf-team: 28f225b2eb0000d66501868400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:20:51.520Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:20:57.664Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:21:02.784Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T10:21:03.748Z DEBUG slog: got a response for stream_id=532, headers=[":status: 200", "cf-team: 28f225ed830000d6650198a400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:21:18.748Z DEBUG slog: got a response for stream_id=536, headers=[":status: 200", "cf-team: 28f226281b0000d66501a40400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:21:32.480Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:21:33.748Z DEBUG slog: got a response for stream_id=540, headers=[":status: 200", "cf-team: 28f22662b30000d66501bb6400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:21:38.880Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T10:21:48.748Z DEBUG slog: got a response for stream_id=544, headers=[":status: 200", "cf-team: 28f2269d4b0000d66501c75400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:21:58.126Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 20359 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 450 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.8009168319999946, count: 20359 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 1785.0, count: 2248 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 289.0, count: 14 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.000341607 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-07-28T10:21:58.126Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 20359 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 450 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }], summaries: [SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.8009168319999946, count: 20359 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 1785.0, count: 2248 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 289.0, count: 14 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.000341607 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-07-28T10:22:03.748Z DEBUG slog: got a response for stream_id=548, headers=[":status: 200", "cf-team: 28f226d7e30000d66501d9e400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:22:03.802Z DEBUG tunnel_loop{protocol="masque" con_id="f6ae5963007bd58af823c5a95b63b4e784cc2068"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 10, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-07-28T10:22:09.344Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:22:15.232Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T10:22:18.748Z DEBUG slog: got a response for stream_id=552, headers=[":status: 200", "cf-team: 28f227127b0000d66501ef7400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:22:30.848Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T10:22:33.748Z DEBUG slog: got a response for stream_id=556, headers=[":status: 200", "cf-team: 28f2274d130000d66502056400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:22:46.208Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:22:48.748Z DEBUG slog: got a response for stream_id=560, headers=[":status: 200", "cf-team: 28f22787ab0000d66502182400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:22:51.584Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T10:23:00.544Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:23:03.748Z DEBUG slog: got a response for stream_id=564, headers=[":status: 200", "cf-team: 28f227c2430000d6650226d400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:23:16.416Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:23:18.748Z DEBUG slog: got a response for stream_id=568, headers=[":status: 200", "cf-team: 28f227fcdb0000d6650255c400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:23:23.072Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:23:28.960Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T10:23:33.747Z DEBUG slog: got a response for stream_id=572, headers=[":status: 200", "cf-team: 28f22837730000d6650273e400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:23:48.748Z DEBUG slog: got a response for stream_id=576, headers=[":status: 200", "cf-team: 28f228720b0000d66502847400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:23:58.126Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 450 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 21559 }], summaries: [SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 289.0, count: 14 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.8465031849999954, count: 21559 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 1792.0, count: 2260 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.006102178 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-07-28T10:23:58.126Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 450 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 21559 }], summaries: [SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 289.0, count: 14 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.8465031849999954, count: 21559 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 1792.0, count: 2260 }], gauges: [GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }, GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.006102178 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-07-28T10:23:59.936Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:24:03.748Z DEBUG slog: got a response for stream_id=580, headers=[":status: 200", "cf-team: 28f228aca30000d6650297e400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:24:03.802Z DEBUG tunnel_loop{protocol="masque" con_id="f6ae5963007bd58af823c5a95b63b4e784cc2068"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 10, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-07-28T10:24:06.340Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T10:24:18.748Z DEBUG slog: got a response for stream_id=584, headers=[":status: 200", "cf-team: 28f228e73b0000d66502a8c400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:24:33.748Z DEBUG slog: got a response for stream_id=588, headers=[":status: 200", "cf-team: 28f22921d30000d66502b85400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:24:36.800Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T10:24:43.456Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T10:24:48.748Z DEBUG slog: got a response for stream_id=592, headers=[":status: 200", "cf-team: 28f2295c6b0000d66502c5c400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:25:03.748Z DEBUG slog: got a response for stream_id=596, headers=[":status: 200", "cf-team: 28f22997030000d66502dea400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:25:07.520Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:25:15.456Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T10:25:18.747Z DEBUG slog: got a response for stream_id=600, headers=[":status: 200", "cf-team: 28f229d19a0000d66502f36400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:25:33.748Z DEBUG slog: got a response for stream_id=604, headers=[":status: 200", "cf-team: 28f22a0c330000d6650304e400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:25:40.288Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:25:47.456Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T10:25:48.748Z DEBUG slog: got a response for stream_id=608, headers=[":status: 200", "cf-team: 28f22a46cb0000d66503173400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:25:58.126Z DEBUG upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 22759 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 450 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 289.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 1806.0, count: 2272 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.8919806809999974, count: 22759 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.007895068 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Starting to upload stats
2025-07-28T10:25:58.126Z  WARN upload_stats{stats=StatsPayload { counters: [CounterPayload { name: "device-state.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "watchdog.requests_recv", labels: {}, count: 22759 }, CounterPayload { name: "dns_proxy.doh_err_other", labels: {}, count: 450 }, CounterPayload { name: "statistics.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "registration-verifier.commands_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "message_bus.bus_driver_updates_sent", labels: {}, count: 1 }, CounterPayload { name: "alternate-network.updates_recv", labels: {}, count: 1 }, CounterPayload { name: "main-loop-compat.commands_recv", labels: {}, count: 2 }, CounterPayload { name: "message_bus.bus_driver_commands_sent", labels: {}, count: 4 }], summaries: [SummaryPayload { name: "firewall.fw_avg_ms", labels: {}, sum: 289.0, count: 14 }, SummaryPayload { name: "dns_proxy.doh_avg_ms", labels: {}, sum: 1806.0, count: 2272 }, SummaryPayload { name: "happy_eyeballs.conn_attempts_all", labels: {}, sum: 1.0, count: 1 }, SummaryPayload { name: "message_bus.bus_driver_request_duration_sec_nano_sec", labels: {}, sum: 0.8919806809999974, count: 22759 }], gauges: [GaugesPayload { name: "ntp-clock.min_since_ntp_sync", labels: {}, value: 60.007895068 }, GaugesPayload { name: "happy_eyeballs.conn_attempts_current", labels: {}, value: 1.0 }] } context="timer"}: actor_statistics::handler: Skipped uploading aggregate stats because registration was none!
2025-07-28T10:26:03.748Z DEBUG slog: got a response for stream_id=612, headers=[":status: 200", "cf-team: 28f22a81630000d6650329a400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:26:03.802Z DEBUG tunnel_loop{protocol="masque" con_id="f6ae5963007bd58af823c5a95b63b4e784cc2068"}: warp_edge::tunnel_loop: Tunnel loop statistics stats=TunnelLoopStats { tun_packet_too_large: 0, edge_packet_too_large: 0, tun_packet_too_small: 0, edge_packet_too_small: 0, tun_packet_martian: 0, tun_packet_unhandled: 10, dropped_edge_packet: 0, dropped_tun_packet: 0, dropped_dns_packet: 0, successfully_retried_tun_packet: 0 }
2025-07-28T10:26:11.008Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:26:18.748Z DEBUG slog: got a response for stream_id=616, headers=[":status: 200", "cf-team: 28f22abbfb0000d66503375400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:26:19.456Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T10:26:33.747Z DEBUG slog: got a response for stream_id=620, headers=[":status: 200", "cf-team: 28f22af6920000d665034a2400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:26:43.776Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
2025-07-28T10:26:48.748Z DEBUG slog: got a response for stream_id=624, headers=[":status: 200", "cf-team: 28f22b312b0000d66503565400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
2025-07-28T10:26:51.460Z DEBUG actor_connectivity::connectivity: Routes changed:
NewNeighbour; Destination: **********; 
NewNeighbour; Destination: **********; 
2025-07-28T10:27:03.748Z DEBUG slog: got a response for stream_id=628, headers=[":status: 200", "cf-team: 28f22b6bc30000d665037ab400000001"] slog.target="httx::http3::client" slog.module_path="httx::http3::client" slog.file="/home/<USER>/.cargo/registry/src/freighter.rust.cfdata.org-59708f22138b42f9/httx-0.31.0/src/http3/client/mod.rs" slog.line=253 slog.column=17
