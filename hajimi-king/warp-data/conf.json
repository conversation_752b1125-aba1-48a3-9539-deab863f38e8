{"own_public_key": "MFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEETkk70g3Mom34m/5WFe6i025i1I7JxQe2EODS9fhB4XyLkMa9IdCmjcHRZc9/xLRumyZ0a2Nznmp+qXwUKUhZQ==", "registration_id": "2d6c00fe-5a6c-46fb-a62a-b9aa5ed62ace", "time_created": {"secs_since_epoch": 1753692587, "nanos_since_epoch": 25457594}, "interface": {"v4": "172.16.0.2", "v6": "2606:4700:110:8bb0:2b1:9371:a882:ec04"}, "endpoints": [{"v4": "162.159.198.2:443", "v6": "[2606:4700:103::2]:443"}, {"v4": "162.159.198.2:500", "v6": "[2606:4700:103::2]:500"}, {"v4": "162.159.198.2:1701", "v6": "[2606:4700:103::2]:1701"}, {"v4": "162.159.198.2:4500", "v6": "[2606:4700:103::2]:4500"}, {"v4": "162.159.198.2:4443", "v6": "[2606:4700:103::2]:4443"}, {"v4": "162.159.198.2:8443", "v6": "[2606:4700:103::2]:8443"}, {"v4": "162.159.198.2:8095", "v6": "[2606:4700:103::2]:8095"}], "public_key": "-----B<PERSON>IN PUBLIC KEY-----\nMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEIaU7MToJm9NKp8YfGxR6r+/h4mcG\n7SxI8tsW8OR1A5tv/zCzVbCRRh2t87/kxnP6lAy0lkr7qYwu+ox+k3dr6w==\n-----END PUBLIC KEY-----\n", "account": {"account_type": "free", "id": "38d2eb15-31ea-470f-92a9-3c26b5351c42", "license": "94e5DJS8-Wqh617X9-78cjuC59"}, "policy": {"onboarding": null, "operation_mode": null, "disable_auto_fallback": null, "fallback_domains": null, "proxy_port": null, "exclude": null, "gateway_id": null, "support_url": null, "allow_mode_switch": null, "switch_locked": null, "auto_connect": null, "captive_portal": null, "organization": null, "allow_updates": null, "allowed_to_leave": null, "profile_id": null, "lan_allow_minutes": null, "lan_allow_subnet_size": 24, "tunnel_protocol": "masque", "register_interface_ip_with_dns": null, "always_exclude": [{"ip": "*************"}, {"ip": "2606:4700:102::3"}], "always_include": [{"ip": "*************"}, {"ip": "2606:4700:102::4"}], "sccm_vpn_boundary_support": null, "enable_post_quantum": null}, "valid_until": "2025-07-29T08:49:47.025458997Z", "alternate_networks": null, "dex_tests": null, "install_root_ca": false, "subnet_cidrs": null, "tunnel_key_data": {"key_type": "secp256r1", "tunnel_type": "masque"}, "device_identifiers": "system_user"}