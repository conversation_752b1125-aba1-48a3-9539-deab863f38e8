services:
  moviepilot:
    stdin_open: true
    tty: true
    container_name: moviepilot-v2
    hostname: moviepilot-v2

    network_mode: bridge

    ports:
      - target: 3000
        published: 23000  # 加前缀“2”
        protocol: tcp
      - target: 3001
        published: 3001 
        protocol: tcp

    volumes:
      - '/opt/1panel/apps/qbittorrent/qbittorrent/data:/media'
      - '/opt/1panel/docker/compose/moviepilot/config:/config'
      - '/opt/1panel/docker/compose/moviepilot/core:/moviepilot/.cache/ms-playwright'
      - '/var/run/docker.sock:/var/run/docker.sock:ro'

    environment:
      - 'NGINX_PORT=3000'
      - 'PORT=3001'
      - 'PUID=0'
      - 'PGID=0'
      - 'UMASK=000'
      - 'TZ=Asia/Shanghai'
      - 'SUPERUSER=admin'
    restart: always
    image: jxxghp/moviepilot-v2:latest
