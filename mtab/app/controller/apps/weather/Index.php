<?php

namespace app\controller\apps\weather;

use app\model\CardModel;
use app\PluginsBase;
use IP2Location\Database;


require_once root_path('extend') . 'IP2Location/IP2LocationLoad.php';
class Index extends PluginsBase
{
    public $gateway = '';

    function _initialize()
    {
        parent::_initialize();
        $this->gateway = CardModel::config("weather", "gateway", "https://devapi.qweather.com");
        //如果不是https://开头就重新修改为https开头
        if (!preg_match('/^https?:\/\//', $this->gateway)) {
            $this->gateway = 'https://' . $this->gateway;
        }
    }

    function ip(): \think\response\Json
    {
        $file = root_path("extend") . '/IP2Location/IP2LOCATION-LITE-DB5.BIN';
        if (file_exists($file)) {
            $ip = getRealIp();
            $db = new Database($file, Database::FILE_IO);
            try {
                $records = $db->lookup($ip, Database::ALL);
                $ipInfo = [
                    'ipAddress' => $records['ipAddress'],
                    'latitude' => $records['latitude'],
                    'longitude' => $records['longitude'],
                    'cityName' => $records['cityName'],
                    'regionName' => $records['regionName'],
                    'countryName' => $records['countryName']
                ];
                if ($ipInfo['latitude'] == 0 && $ipInfo['longitude'] == 0) {
                    $ipInfo['latitude'] = 39.91;
                    $ipInfo['longitude'] = 116.41;
                }
                return json(['code' => 1, 'msg' => 'success', 'data' => $ipInfo]);
            } catch (\Exception $e) {
                return json(['code' => 0, 'msg' => '不支持ipv6,请手动填写城是查询']);
            }
        }
        return json(['code' => 0, 'msg' => '地理位置数据包不存在']);
    }

    function setting()
    {
        $this->getAdmin();
        if ($this->request->isPost()) {
            $form = $this->request->post();
            CardModel::saveConfigs("weather", $form);
            return $this->success("保存成功");
        }
        if ($this->request->isPut()) {
            $form = CardModel::configs('weather');
            return $this->success('ok', $form);
        }

        return $this->fetch("setting.html");
    }

    function everyDay(): \think\response\Json
    {

        $apiKey = CardModel::config('weather', 'key');
        $location = $this->request->get("location", "101010100");
        try {
            $result = \Axios::http()->get($this->gateway . '/v7/weather/7d', [
                'query' => [
                    'location' => $location,
                ],
                "headers"=>[
                    "X-QW-Api-Key"=>$apiKey
                ]
            ]);
            if ($result->getStatusCode() === 200) {
                $json = \Axios::toJson($result->getBody()->getContents());
                if ($json && $json['code'] == "200") {
                    return $this->success($json['daily']);
                }
            }
        } catch (\Exception $e) {
        }
        return $this->error("数据获取错误");
    }

    function now(): \think\response\Json
    {

        $apiKey = CardModel::config('weather', 'key');
        $location = $this->request->get('location', '101010100');
        try {
            $result = \Axios::http()->get($this->gateway . '/v7/weather/now', [
                'query' => [
                    'location' => $location,
                ],
                "headers"=>[
                    "X-QW-Api-Key"=>$apiKey
                ]
            ]);
            if ($result->getStatusCode() === 200) {
                $json = \Axios::toJson($result->getBody()->getContents());
                if ($json && $json['code'] == '200') {
                    return $this->success($json['now']);
                }
            }
        } catch (\Exception $e) {
        
        }
        return $this->error('数据获取错误');
    }

    function locationToCity(): \think\response\Json
    {

        $location = $this->request->all('location', '101010100');
        $apiKey = CardModel::config('weather', 'key');
        try {
            $result = \Axios::http()->get("{$this->gateway}/geo/v2/city/lookup", [
                'query' => [
                    'location' => $location,
                ],
                "headers"=>[
                    "X-QW-Api-Key"=>$apiKey
                ]
            ]);
            if ($result->getStatusCode() === 200) {
                $json = \Axios::toJson($result->getBody()->getContents());
                if ($json && $json['code'] == '200') {
                    if (count($json['location']) > 0) {
                        return $this->success($json['location'][0]);
                    }
                }
            }
            if($result->getStatusCode()===401||$result->getStatusCode()===403){
                return $this->error("获取失败，请检查API或者API KEY是否正确");
            }
        } catch (\Exception $e) {
        }
        return $this->error('数据获取错误');
    }

    function citySearch(): \think\response\Json
    {
        $city = $this->request->post("city", "");
        $apiKey = CardModel::config('weather', 'key');
        if (trim($city)) {
            try {
                $result = \Axios::http()->get("{$this->gateway}/geo/v2/city/lookup", [
                    'query' => [
                        'location' => $city,
                        'key' =>$apiKey ,
                    ],
                    "headers"=>[
                        "X-QW-Api-Key"=>$apiKey
                    ]
                ]);
                if ($result->getStatusCode() === 200) {
                    $json = \Axios::toJson($result->getBody()->getContents());
                    if ($json && $json['code'] == '200') {
                        if (count($json['location']) > 0) {
                            return $this->success($json['location']);
                        }
                    }
                }
                if($result->getStatusCode()===401||$result->getStatusCode()===403){
                    return $this->error("获取失败，请检查API或者API KEY是否正确");
                }
            } catch (\Exception $e) {
            }
        }
        return $this->error('数据获取错误');
    }
}