<?php

namespace app\model;

use think\Model;

/**
 *
 */
class AiModel extends Model
{
    protected $name = "ai";
    protected $pk = "id";

    /**
     * @param $user_id
     * @param $role
     * @param $message
     * @param $dialogue_id
     * @return AiModel|Model
     */
    static function addMessage($user_id, $role, $message, $dialogue_id)
    {
        if (!$role) {
            $role = 'user';
        }
        return self::create(['user_id' => $user_id, 'message' => $message, 'role' => $role, 'dialogue_id' => $dialogue_id]);
    }
    static function history($user_id, $dialogue_id): array
    {
        return self::where('user_id', $user_id)->where('dialogue_id', $dialogue_id)->field("role,message as content")->select()->toArray();
    }
}