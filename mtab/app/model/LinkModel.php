<?php
/*
 * @description:
 * @Date: 2022-09-26 20:27:01
 * @LastEditTime: 2022-09-26 20:27:53
 */

namespace app\model;

use think\Model;

class LinkModel extends Model
{
    protected $name = "link";
    protected $pk = "user_id";
    protected $autoWriteTimestamp = "datetime";
    protected $updateTime = "update_time";
    protected $jsonAssoc = true;
    protected $json = ['link'];
    protected $WebApp = [];
    protected $card = [];

    public function __construct(array $data = [])
    {
        parent::__construct($data);
        $list = LinkStoreModel::where("app", 1)->select()->toArray();
        foreach ($list as $k => $v) {
            $this->WebApp[$v['id']] = $v;
        }
    }

    function getLinkAttr($value): array
    {
        foreach ($value as $k => &$v) {
            if (isset($v['app']) && $v['app'] == 1) {
                //如果存在app，并且id>0,且type为icon，则从app中获取数据
                if (isset($v['origin_id']) && $v['origin_id'] > 0 && $v['type'] === 'icon') {
                    $origin_id = (int)$v['origin_id'];
                    if (isset($this->WebApp[$origin_id])) {
                        $webApp = $this->WebApp[$origin_id];
                        //替换掉app数据
                        $v['custom'] = $webApp['custom'];
                        $v['url'] = $webApp['url'];
                        $v['src'] = $webApp['src'];
                        $v['name'] = $webApp['name'];
                        $v['bgColor'] = $webApp['bgColor'];
                    }
                }
            }
        }
        return (array)$value;
    }
}
