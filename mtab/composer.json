{"name": "topthink/think", "description": "the new thinkphp framework", "type": "project", "keywords": ["framework", "thinkphp", "ORM"], "homepage": "https://www.thinkphp.cn/", "license": "Apache-2.0", "authors": [{"name": "liu21st", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": ">=7.4", "topthink/framework": "^6.1.0", "topthink/think-orm": "^2.0", "topthink/think-view": "^1.0", "ext-json": "*", "guzzlehttp/guzzle": "^7.7", "ext-openssl": "*", "ext-fileinfo": "*", "topthink/think-filesystem": "^2.0", "ext-mysqli": "*", "ext-redis": "*", "ext-pcntl": "*", "ext-zip": "*", "ext-posix": "*", "ext-dom": "*", "ext-mbstring": "*", "ext-bcmath": "*", "ext-gd": "*", "ext-libxml": "*", "nette/mail": "^3.1", "ext-curl": "*"}, "require-dev": {"symfony/var-dumper": "^4.2", "topthink/think-trace": "^1.0"}, "autoload": {"psr-4": {"app\\": "app", "plugins\\": "plugins"}, "psr-0": {"": "extend/"}}, "config": {"preferred-install": "dist", "platform-check": false}, "scripts": {"post-autoload-dump": ["@php think service:discover", "@php think vendor:publish"]}}