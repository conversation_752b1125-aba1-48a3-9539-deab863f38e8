
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>500-服务内部故障！</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background-color: #0078d7;
            color: white;
            font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 100vh;
            text-align: center;
        }
        .error-code {
            font-size: 1.5em;
            margin-top: 20px;
        }
        .description {
            font-size: 1.2em;
            margin-top: 10px;
        }
        .input-container {
            margin-top: 20px;
        }
        .input-container input {
            font-size: 1em;
            padding: 10px;
            width: 300px;
            border: none;
            border-radius: 5px;
        }
        a {
            text-decoration: none;
            color: #fff;
        }
    </style>
</head>
<body>
<div>
    <div class="emoji" style="font-size: 5em;">:(</div>
    <div class="main-message" style="font-size: 2em; margin-top: 20px;">An error occurred and your request could not be completed.</div>
    <div class="error-code">发生错误，您的请求无法完成，您可以截图联系开发者提交该截图来获取对应的解决方案</div>
    <div class="description"><div>ERROR_MSG：<?php echo $_ENV["error_msg"]; ?></div> </div>
</div>
<script>
    const errorCodeInput = document.getElementById('error-code-input');
    const errorCodeDisplay = document.getElementById('error-code-display');

    errorCodeInput.addEventListener('input', () => {
        errorCodeDisplay.textContent = errorCodeInput.value;
    });
</script>
</body>
</html>
