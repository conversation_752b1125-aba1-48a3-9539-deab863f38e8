<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:w="urn:schemas-microsoft-com:office:word" xmlns:dt="uuid:C2F41010-65B3-11d1-A29F-00AA00C14882" xmlns="http://www.w3.org/TR/REC-html40"><head><meta http-equiv=Content-Type  content="text/html; charset=utf-8" ><meta name=ProgId  content=Word.Document ><meta name=Generator  content="Microsoft Word 14" ><meta name=Originator  content="Microsoft Word 14" ><title></title><!--[if gte mso 9]><xml><o:DocumentProperties><o:Author>成成</o:Author><o:LastAuthor>成成</o:LastAuthor><o:Revision>1</o:Revision><o:Pages>2</o:Pages></o:DocumentProperties><o:CustomDocumentProperties><o:KSOProductBuildVer dt:dt="string" >2052-12.1.0.17827</o:KSOProductBuildVer><o:ICV dt:dt="string" >1DFF78AF52D74E47BF2000CD66A33187_13</o:ICV></o:CustomDocumentProperties></xml><![endif]--><!--[if gte mso 9]><xml><o:OfficeDocumentSettings></o:OfficeDocumentSettings></xml><![endif]--><!--[if gte mso 9]><xml><w:WordDocument><w:BrowserLevel>MicrosoftInternetExplorer4</w:BrowserLevel><w:DisplayHorizontalDrawingGridEvery>0</w:DisplayHorizontalDrawingGridEvery><w:DisplayVerticalDrawingGridEvery>2</w:DisplayVerticalDrawingGridEvery><w:DocumentKind>DocumentNotSpecified</w:DocumentKind><w:DrawingGridVerticalSpacing>7.8 磅</w:DrawingGridVerticalSpacing><w:View>Web</w:View><w:Compatibility><w:AdjustLineHeightInTable/><w:DontGrowAutofit/><w:BalanceSingleByteDoubleByteWidth/><w:DoNotExpandShiftReturn/><w:UseFELayout/></w:Compatibility><w:Zoom>0</w:Zoom></w:WordDocument></xml><![endif]--><!--[if gte mso 9]><xml><w:LatentStyles DefLockedState="false"  DefUnhideWhenUsed="true"  DefSemiHidden="true"  DefQFormat="false"  DefPriority="99"  LatentStyleCount="260" >
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  QFormat="true"  Name="Normal" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  QFormat="true"  Name="heading 1" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  QFormat="true"  Name="heading 2" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  QFormat="true"  Name="heading 3" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  QFormat="true"  Name="heading 4" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  QFormat="true"  Name="heading 5" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  QFormat="true"  Name="heading 6" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  QFormat="true"  Name="heading 7" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  QFormat="true"  Name="heading 8" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  QFormat="true"  Name="heading 9" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="index 1" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="index 2" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="index 3" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="index 4" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="index 5" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="index 6" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="index 7" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="index 8" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="index 9" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="toc 1" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="toc 2" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="toc 3" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="toc 4" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="toc 5" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="toc 6" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="toc 7" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="toc 8" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="toc 9" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Normal Indent" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="footnote text" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="annotation text" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="header" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="footer" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="index heading" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  QFormat="true"  Name="caption" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="table of figures" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="envelope address" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="envelope return" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="footnote reference" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="annotation reference" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="line number" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="page number" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="endnote reference" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="endnote text" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="table of authorities" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="macro" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="toa heading" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List Bullet" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List Number" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List 2" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List 3" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List 4" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List 5" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List Bullet 2" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List Bullet 3" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List Bullet 4" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List Bullet 5" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List Number 2" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List Number 3" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List Number 4" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List Number 5" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  QFormat="true"  Name="Title" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Closing" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Signature" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  UnhideWhenUsed="false"  QFormat="true"  Name="Default Paragraph Font" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Body Text" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Body Text Indent" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List Continue" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List Continue 2" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List Continue 3" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List Continue 4" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="List Continue 5" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Message Header" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  QFormat="true"  Name="Subtitle" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Salutation" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Date" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Body Text First Indent" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Body Text First Indent 2" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Note Heading" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Body Text 2" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Body Text 3" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Body Text Indent 2" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Body Text Indent 3" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Block Text" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Hyperlink" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="FollowedHyperlink" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  QFormat="true"  Name="Strong" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  QFormat="true"  Name="Emphasis" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Document Map" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Plain Text" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="E-mail Signature" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Normal (Web)" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="HTML Acronym" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="HTML Address" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="HTML Cite" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="HTML Code" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="HTML Definition" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="HTML Keyboard" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="HTML Preformatted" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="HTML Sample" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="HTML Typewriter" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="HTML Variable" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  UnhideWhenUsed="false"  QFormat="true"  Name="Normal Table" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="annotation subject" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="No List" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="1 / a / i" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="1 / 1.1 / 1.1.1" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Article / Section" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Simple 1" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Simple 2" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Simple 3" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Classic 1" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Classic 2" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Classic 3" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Classic 4" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Colorful 1" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Colorful 2" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Colorful 3" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Columns 1" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Columns 2" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Columns 3" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Columns 4" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Columns 5" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Grid 1" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Grid 2" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Grid 3" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Grid 4" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Grid 5" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Grid 6" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Grid 7" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Grid 8" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table List 1" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table List 2" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table List 3" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table List 4" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table List 5" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table List 6" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table List 7" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table List 8" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table 3D effects 1" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table 3D effects 2" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table 3D effects 3" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Contemporary" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Elegant" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Professional" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Subtle 1" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Subtle 2" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Web 1" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Web 2" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Web 3" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Balloon Text" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Grid" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="0"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Table Theme" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Placeholder Text" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="No Spacing" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="60"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light Shading" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="61"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light List" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="62"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light Grid" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="63"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Shading 1" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="64"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Shading 2" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="65"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium List 1" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="66"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium List 2" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="67"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 1" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="68"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 2" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="69"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 3" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="70"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Dark List" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="71"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful Shading" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="72"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful List" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="73"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful Grid" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="60"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light Shading Accent 1" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="61"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light List Accent 1" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="62"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light Grid Accent 1" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="63"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Shading 1 Accent 1" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="64"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Shading 2 Accent 1" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="65"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium List 1 Accent 1" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="List Paragraph" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Quote" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="99"  SemiHidden="false"  Name="Intense Quote" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="66"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium List 2 Accent 1" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="67"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 1 Accent 1" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="68"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 2 Accent 1" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="69"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 3 Accent 1" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="70"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Dark List Accent 1" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="71"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful Shading Accent 1" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="72"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful List Accent 1" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="73"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful Grid Accent 1" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="60"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light Shading Accent 2" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="61"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light List Accent 2" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="62"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light Grid Accent 2" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="63"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Shading 1 Accent 2" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="64"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Shading 2 Accent 2" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="65"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium List 1 Accent 2" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="66"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium List 2 Accent 2" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="67"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 1 Accent 2" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="68"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 2 Accent 2" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="69"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 3 Accent 2" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="70"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Dark List Accent 2" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="71"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful Shading Accent 2" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="72"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful List Accent 2" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="73"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful Grid Accent 2" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="60"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light Shading Accent 3" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="61"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light List Accent 3" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="62"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light Grid Accent 3" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="63"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Shading 1 Accent 3" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="64"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Shading 2 Accent 3" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="65"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium List 1 Accent 3" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="66"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium List 2 Accent 3" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="67"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 1 Accent 3" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="68"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 2 Accent 3" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="69"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 3 Accent 3" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="70"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Dark List Accent 3" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="71"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful Shading Accent 3" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="72"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful List Accent 3" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="73"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful Grid Accent 3" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="60"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light Shading Accent 4" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="61"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light List Accent 4" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="62"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light Grid Accent 4" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="63"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Shading 1 Accent 4" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="64"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Shading 2 Accent 4" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="65"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium List 1 Accent 4" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="66"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium List 2 Accent 4" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="67"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 1 Accent 4" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="68"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 2 Accent 4" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="69"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 3 Accent 4" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="70"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Dark List Accent 4" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="71"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful Shading Accent 4" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="72"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful List Accent 4" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="73"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful Grid Accent 4" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="60"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light Shading Accent 5" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="61"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light List Accent 5" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="62"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light Grid Accent 5" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="63"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Shading 1 Accent 5" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="64"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Shading 2 Accent 5" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="65"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium List 1 Accent 5" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="66"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium List 2 Accent 5" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="67"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 1 Accent 5" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="68"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 2 Accent 5" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="69"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 3 Accent 5" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="70"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Dark List Accent 5" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="71"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful Shading Accent 5" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="72"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful List Accent 5" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="73"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful Grid Accent 5" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="60"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light Shading Accent 6" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="61"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light List Accent 6" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="62"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Light Grid Accent 6" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="63"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Shading 1 Accent 6" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="64"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Shading 2 Accent 6" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="65"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium List 1 Accent 6" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="66"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium List 2 Accent 6" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="67"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 1 Accent 6" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="68"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 2 Accent 6" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="69"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Medium Grid 3 Accent 6" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="70"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Dark List Accent 6" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="71"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful Shading Accent 6" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="72"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful List Accent 6" ></w:LsdException>
    <w:LsdException Locked="false"  Priority="73"  SemiHidden="false"  UnhideWhenUsed="false"  Name="Colorful Grid Accent 6" ></w:LsdException>
</w:LatentStyles></xml><![endif]--><style>
    @font-face{
        font-family:"Times New Roman";
    }

    @font-face{
        font-family:"宋体";
    }

    @font-face{
        font-family:"Wingdings";
    }

    @font-face{
        font-family:"仿宋";
    }

    @font-face{
        font-family:"Calibri";
    }

    @font-face{
        font-family:"黑体";
    }

    @font-face{
        font-family:"楷体";
    }

    @font-face{
        font-family:"Arial";
    }

    p.MsoNormal{
        mso-style-name:正文;
        mso-style-parent:"";
        margin:0pt;
        margin-bottom:.0001pt;
        mso-layout-grid-align:none;
        punctuation-wrap:simple;
        punctuation-trim:leading;
        mso-pagination:none;
        text-align:justify;
        text-justify:inter-ideograph;
        font-family:'Times New Roman';
        mso-fareast-font-family:仿宋;
        mso-hansi-font-family:Calibri;
        letter-spacing:-0.3000pt;
        font-size:16.0000pt;
        mso-font-kerning:1.0000pt;
    }

    h1{
        mso-style-name:"标题 1";
        mso-para-margin-top:0.0000gd;
        mso-para-margin-bottom:0.0000gd;
        punctuation-wrap:simple;
        punctuation-trim:leading;
        mso-pagination:none;
        text-align:justify;
        text-justify:inter-ideograph;
        mso-outline-level:1;
        font-family:'Times New Roman';
        mso-fareast-font-family:黑体;
        mso-ansi-font-weight:normal;
        font-size:16.0000pt;
        mso-font-kerning:1.0000pt;
    }

    h2{
        mso-style-name:"标题 2";
        mso-style-noshow:yes;
        mso-para-margin-top:0.0000gd;
        mso-para-margin-bottom:0.0000gd;
        punctuation-wrap:simple;
        punctuation-trim:leading;
        mso-pagination:none;
        text-align:justify;
        text-justify:inter-ideograph;
        mso-outline-level:2;
        font-family:'Times New Roman';
        mso-fareast-font-family:楷体;
        mso-hansi-font-family:Arial;
        mso-ansi-font-weight:normal;
        font-size:16.0000pt;
        mso-font-kerning:1.0000pt;
    }

    h3{
        mso-style-name:"标题 3";
        mso-style-noshow:yes;
        mso-para-margin-top:0.0000gd;
        mso-para-margin-bottom:0.0000gd;
        punctuation-wrap:simple;
        punctuation-trim:leading;
        mso-pagination:none;
        text-align:justify;
        text-justify:inter-ideograph;
        mso-outline-level:3;
        font-family:'Times New Roman';
        mso-fareast-font-family:仿宋;
        mso-ansi-font-weight:normal;
        font-size:16.0000pt;
        mso-font-kerning:1.0000pt;
    }

    h4{
        mso-style-name:"标题 4";
        mso-style-noshow:yes;
        mso-para-margin-top:0.0000gd;
        mso-para-margin-bottom:0.0000gd;
        punctuation-wrap:simple;
        punctuation-trim:leading;
        mso-pagination:none;
        text-align:justify;
        text-justify:inter-ideograph;
        mso-outline-level:4;
        font-family:'Times New Roman';
        mso-fareast-font-family:仿宋;
        mso-hansi-font-family:Arial;
        mso-ansi-font-weight:normal;
        font-size:16.0000pt;
        mso-font-kerning:1.0000pt;
    }

    span.10{
        font-family:'Times New Roman';
    }

    span.15{
        font-family:'Times New Roman';
        color:rgb(0,0,255);
        text-decoration:underline;
        text-underline:single;
    }

    p.MsoFooter{
        mso-style-name:页脚;
        margin:0pt;
        margin-bottom:.0001pt;
        mso-layout-grid-align:none;
        layout-grid-mode:char;
        punctuation-wrap:simple;
        punctuation-trim:leading;
        mso-pagination:none;
        text-align:left;
        font-family:'Times New Roman';
        mso-fareast-font-family:仿宋;
        mso-hansi-font-family:Calibri;
        letter-spacing:-0.3000pt;
        font-size:9.0000pt;
        mso-font-kerning:1.0000pt;
    }

    p.MsoHeader{
        mso-style-name:页眉;
        margin:0pt;
        margin-bottom:.0001pt;
        border-top:none;
        mso-border-top-alt:none;
        border-right:none;
        mso-border-right-alt:none;
        border-bottom:none;
        mso-border-bottom-alt:none;
        border-left:none;
        mso-border-left-alt:none;
        padding:1pt 4pt 1pt 4pt ;
        mso-layout-grid-align:none;
        layout-grid-mode:char;
        punctuation-wrap:simple;
        punctuation-trim:leading;
        mso-pagination:none;
        text-align:justify;
        text-justify:inter-ideograph;
        font-family:'Times New Roman';
        mso-fareast-font-family:仿宋;
        mso-hansi-font-family:Calibri;
        letter-spacing:-0.3000pt;
        font-size:9.0000pt;
        mso-font-kerning:1.0000pt;
    }

    p.pre{
        mso-style-name:"HTML 预设格式";
        margin:0pt;
        margin-bottom:.0001pt;
        mso-layout-grid-align:none;
        punctuation-wrap:simple;
        punctuation-trim:leading;
        mso-pagination:none;
        text-align:left;
        font-family:宋体;
        letter-spacing:-0.3000pt;
        font-size:12.0000pt;
    }

    span.msoIns{
        mso-style-type:export-only;
        mso-style-name:"";
        text-decoration:underline;
        text-underline:single;
        color:blue;
    }

    span.msoDel{
        mso-style-type:export-only;
        mso-style-name:"";
        text-decoration:line-through;
        color:red;
    }

    table.MsoNormalTable{
        mso-style-name:普通表格;
        mso-style-parent:"";
        mso-style-noshow:yes;
        mso-tstyle-rowband-size:0;
        mso-tstyle-colband-size:0;
        mso-padding-alt:0.0000pt 5.4000pt 0.0000pt 5.4000pt;
        mso-para-margin:0pt;
        mso-para-margin-bottom:.0001pt;
        mso-pagination:widow-orphan;
        font-family:'Times New Roman';
        font-size:10.0000pt;
        mso-ansi-language:#0400;
        mso-fareast-language:#0400;
        mso-bidi-language:#0400;
    }
    @page{mso-page-border-surround-header:no;
        mso-page-border-surround-footer:no;}@page Section0{
        margin-top:98.1000pt;
        margin-bottom:92.4000pt;
        margin-left:79.3500pt;
        margin-right:73.7000pt;
        size:595.3000pt 841.9000pt;
        layout-grid:29.6000pt;
        mso-header-margin:42.5500pt;
        mso-footer-margin:52.4500pt;
        mso-header:url("~tmp%7b34fb1633-582b-4f6f-92b0-3e3662eb9596%7d3803144.files/header.html") h0;
        mso-footer:url("~tmp%7b34fb1633-582b-4f6f-92b0-3e3662eb9596%7d3803144.files/header.html") f0;
    }
    div.Section0{page:Section0;}</style></head><body style="tab-interval:21pt;" ><!--StartFragment--><div class="Section0"  style="layout-grid:29.6000pt;" ><p class=MsoNormal  align=center  style="punctuation-wrap:simple;punctuation-trim:leading;text-align:center;" ><b><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-weight:bold;font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><font face="Times New Roman" >mTab</font><font face="仿宋" >新标签页内容管理系统用户服务协议</font></span></b><b><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-weight:bold;font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></b></p><p class=MsoNormal  align=center  style="punctuation-wrap:simple;punctuation-trim:leading;text-align:center;" ><b><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-weight:bold;font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><o:p>&nbsp;</o:p></span></b></p><p class=MsoNormal  style="punctuation-wrap:simple;punctuation-trim:leading;" ><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><font face="仿宋" >为了明确用户</font></span><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><font face="仿宋" >与</font></span><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><font face="Times New Roman" >mTa</font></span><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><font face="Times New Roman" >b</font></span><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><font face="仿宋" >新标签页内容管理系统（以下简称</font><font face="仿宋" >“</font></span><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><font face="Times New Roman" >mTa</font></span><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><font face="Times New Roman" >b</font><font face="仿宋" >”）之间的权利义务关系，保障双方的合法权益，现依据相关法律法规，制定</font></span><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><font face="仿宋" >本</font></span><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><font face="仿宋" >协议。</font></span><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal  style="punctuation-wrap:simple;punctuation-trim:leading;" ><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><o:p>&nbsp;</o:p></span></p><p class=MsoNormal  style="punctuation-wrap:simple;punctuation-trim:leading;" ><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><font face="仿宋" >一、许可与使用</font></span><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal  style="margin-left:21.0000pt;mso-para-margin-left:0.0000gd;text-indent:21.0000pt;
 mso-char-indent-count:0.0000;punctuation-wrap:simple;punctuation-trim:leading;" ><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><font face="Times New Roman" >1. </font><font face="仿宋" >用户在使用</font><font face="Times New Roman" >mTab</font><font face="仿宋" >服务时，应遵守所有适用的法律法规及本协议的约定。</font></span><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal  style="margin-left:21.0000pt;mso-para-margin-left:0.0000gd;text-indent:21.0000pt;
 mso-char-indent-count:0.0000;punctuation-wrap:simple;punctuation-trim:leading;" ><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><font face="Times New Roman" >2. </font><font face="仿宋" >用户仅获授权在本协议允许的范围内使用</font><font face="Times New Roman" >mTab</font><font face="仿宋" >服务，未经授权不得修改、复制、传播、发行、再许可或以其他方式利用</font><font face="Times New Roman" >mTab</font><font face="仿宋" >的源代码、设计或其他相关内容。</font></span><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal  style="margin-left:21.0000pt;mso-para-margin-left:0.0000gd;text-indent:21.0000pt;
 mso-char-indent-count:0.0000;punctuation-wrap:simple;punctuation-trim:leading;" ><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><font face="Times New Roman" >3. </font><font face="仿宋" >用户不得基于本程序框架使用“</font><font face="Times New Roman" >mTab </font><font face="仿宋" >新标签页”、“</font><font face="Times New Roman" >mTab </font><font face="仿宋" >书签”或任何类似字样来标识或运营任何服务或产品。</font></span><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal  style="punctuation-wrap:simple;punctuation-trim:leading;" ><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><o:p>&nbsp;</o:p></span></p><p class=MsoNormal  style="punctuation-wrap:simple;punctuation-trim:leading;" ><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><font face="仿宋" >二、基于该程序的域名使用规定</font></span><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal  style="margin-left:21.0000pt;mso-para-margin-left:0.0000gd;text-indent:21.0000pt;
 mso-char-indent-count:0.0000;punctuation-wrap:simple;punctuation-trim:leading;" ><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><font face="Times New Roman" >1. </font><font face="仿宋" >用户不得注册、使用或推广任何以“</font><font face="Times New Roman" >mTab</font><font face="仿宋" >”相关结尾的一级域名。</font></span><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal  style="margin-left:21.0000pt;mso-para-margin-left:0.0000gd;text-indent:21.0000pt;
 mso-char-indent-count:0.0000;punctuation-wrap:simple;punctuation-trim:leading;" ><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><font face="Times New Roman" >2. </font><font face="仿宋" >任何因使用“</font><font face="Times New Roman" >mTab</font><font face="仿宋" >”相关域名所引发的问题和后果由用户自行承担。</font></span><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal  style="margin-left:21.0000pt;mso-para-margin-left:0.0000gd;text-indent:21.0000pt;
 mso-char-indent-count:0.0000;punctuation-wrap:simple;punctuation-trim:leading;" ><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><font face="Times New Roman" >3. </font><font face="仿宋" >“</font><font face="Times New Roman" >mTab</font><font face="仿宋" >”相关结尾的一级域名包括但不限于以下示例：</font><font face="Times New Roman" >*.mtab.cc</font></span><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><font face="仿宋" >，</font></span><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><font face="仿宋" >任何与</font><font face="Times New Roman" >mTab</font><font face="仿宋" >相关的变体、缩写或拼写相似的域名。</font></span><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal  style="punctuation-wrap:simple;punctuation-trim:leading;" ><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><o:p>&nbsp;</o:p></span></p><p class=MsoNormal  style="punctuation-wrap:simple;punctuation-trim:leading;" ><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><font face="仿宋" >三、知识产权</font></span><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal  style="margin-left:21.0000pt;mso-para-margin-left:0.0000gd;text-indent:21.0000pt;
 mso-char-indent-count:0.0000;punctuation-wrap:simple;punctuation-trim:leading;" ><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><font face="Times New Roman" >1. mTab</font><font face="仿宋" >新标签页内容管理系统的源代码及相关知识产权归</font><font face="Times New Roman" >mTab</font><font face="仿宋" >所有，用户在使用过程中应遵守相关版权协议。</font></span><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal  style="margin-left:21.0000pt;mso-para-margin-left:0.0000gd;text-indent:21.0000pt;
 mso-char-indent-count:0.0000;punctuation-wrap:simple;punctuation-trim:leading;" ><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><font face="Times New Roman" >2. </font><font face="仿宋" >用户不得以任何方式侵犯或试图侵犯</font><font face="Times New Roman" >mTab</font><font face="仿宋" >的知识产权，包括但不限于复制、修改</font></span><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><font face="仿宋" >后的二次销售行为，但是对于修改后的自主运营程序提供的使用服务，则在允许的范围内。</font></span><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:仿宋;
 mso-hansi-font-family:Calibri;letter-spacing:-0.3000pt;font-size:16.0000pt;
 mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal  style="punctuation-wrap:simple;punctuation-trim:leading;" ><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><o:p>&nbsp;</o:p></span></p><p class=MsoNormal  style="punctuation-wrap:simple;punctuation-trim:leading;" ><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><font face="仿宋" >四、责任限制</font></span><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal  style="margin-left:21.0000pt;mso-para-margin-left:0.0000gd;text-indent:21.0000pt;
 mso-char-indent-count:0.0000;punctuation-wrap:simple;punctuation-trim:leading;" ><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><font face="Times New Roman" >1. mTab</font><font face="仿宋" >不对因使用或无法使用本服务而产生的任何直接、间接、附带、特殊或惩罚性损害承担责任。</font></span><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal  style="margin-left:21.0000pt;mso-para-margin-left:0.0000gd;text-indent:21.0000pt;
 mso-char-indent-count:0.0000;punctuation-wrap:simple;punctuation-trim:leading;" ><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><font face="Times New Roman" >2. </font><font face="仿宋" >用户应为因使用本服务所导致的所有行为后果负责，</font><font face="Times New Roman" >mTab</font><font face="仿宋" >不承担由此引发的任何责任。</font></span><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal  style="punctuation-wrap:simple;punctuation-trim:leading;" ><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><o:p>&nbsp;</o:p></span></p><p class=MsoNormal  style="punctuation-wrap:simple;punctuation-trim:leading;" ><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><font face="仿宋" >五、服务的终止</font></span><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal  style="margin-left:21.0000pt;mso-para-margin-left:0.0000gd;text-indent:21.0000pt;
 mso-char-indent-count:0.0000;punctuation-wrap:simple;punctuation-trim:leading;" ><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><font face="Times New Roman" >1. mTab</font><font face="仿宋" >保留在任何时候、无需事先通知的情况下，基于任何原因终止用户访问和使用本服务的权利。</font></span><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal  style="margin-left:21.0000pt;mso-para-margin-left:0.0000gd;text-indent:21.0000pt;
 mso-char-indent-count:0.0000;punctuation-wrap:simple;punctuation-trim:leading;" ><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><font face="Times New Roman" >2. </font><font face="仿宋" >在服务终止后，</font><font face="Times New Roman" >mTab</font><font face="仿宋" >有权根据实际情况决定是否保留或删除用户的数据。</font></span><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal  style="margin-left:21.0000pt;mso-para-margin-left:0.0000gd;text-indent:21.0000pt;
 mso-char-indent-count:0.0000;punctuation-wrap:simple;punctuation-trim:leading;" ><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><font face="Times New Roman" >3. </font><font face="仿宋" >用户可在合理范围内申请恢复访问，但</font><font face="Times New Roman" >mTab</font><font face="仿宋" >不保证该申请必定得到批准。</font></span><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal  style="punctuation-wrap:simple;punctuation-trim:leading;" ><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><o:p>&nbsp;</o:p></span></p><p class=MsoNormal  style="punctuation-wrap:simple;punctuation-trim:leading;" ><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><font face="仿宋" >六、协议的变更</font></span><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal  style="margin-left:21.0000pt;mso-para-margin-left:0.0000gd;text-indent:21.0000pt;
 mso-char-indent-count:0.0000;punctuation-wrap:simple;punctuation-trim:leading;" ><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><font face="Times New Roman" >1. mTab</font><font face="仿宋" >有权根据实际需要随时修改本协议的条款，修改后的协议在发布之时生效。</font></span><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal  style="margin-left:21.0000pt;mso-para-margin-left:0.0000gd;text-indent:21.0000pt;
 mso-char-indent-count:0.0000;punctuation-wrap:simple;punctuation-trim:leading;" ><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><font face="Times New Roman" >2. </font><font face="仿宋" >用户在协议变更后继续使用本服务，即视为接受修改后的</font></span><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><font face="仿宋" >条款。</font></span><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal  style="margin-left:21.0000pt;mso-para-margin-left:0.0000gd;text-indent:21.0000pt;
 mso-char-indent-count:0.0000;punctuation-wrap:simple;punctuation-trim:leading;" ><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><font face="Times New Roman" >3. </font><font face="仿宋" >若用户不同意协议的修改内容，有权在变更生效前停止使用本服务。</font></span><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal  style="punctuation-wrap:simple;punctuation-trim:leading;" ><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><o:p>&nbsp;</o:p></span></p><p class=MsoNormal  style="punctuation-wrap:simple;punctuation-trim:leading;" ><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><font face="仿宋" >七、用户数据隐私</font></span><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal  style="margin-left:21.0000pt;mso-para-margin-left:0.0000gd;text-indent:21.0000pt;
 mso-char-indent-count:0.0000;punctuation-wrap:simple;punctuation-trim:leading;" ><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><font face="Times New Roman" >1. mTab</font></span><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><font face="仿宋" >不会收集运营者的部署的程序的用户信息内容信息，但不包括收集必要的服务器</font><font face="Times New Roman" >ip</font><font face="仿宋" >、域名、版本号等信息用于程序的更新和授权用途</font></span><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><font face="仿宋" >。</font></span><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal  style="margin-left:21.0000pt;mso-para-margin-left:0.0000gd;text-indent:21.0000pt;
 mso-char-indent-count:0.0000;punctuation-wrap:simple;punctuation-trim:leading;" ><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><font face="Times New Roman" >2. </font><font face="仿宋" >用户在使用</font><font face="Times New Roman" >mTab</font><font face="仿宋" >服务时，同意</font><font face="Times New Roman" >mTab</font><font face="仿宋" >按照相关法律规定及隐私政策处理其个人数据。</font></span><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal  style="margin-left:21.0000pt;mso-para-margin-left:0.0000gd;text-indent:21.0000pt;
 mso-char-indent-count:0.0000;punctuation-wrap:simple;punctuation-trim:leading;" ><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><o:p>&nbsp;</o:p></span></p><p class=MsoNormal  style="punctuation-wrap:simple;punctuation-trim:leading;" ><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><font face="仿宋" >八、适用法律与争议解决</font></span><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal  style="margin-left:21.0000pt;mso-para-margin-left:0.0000gd;text-indent:21.0000pt;
 mso-char-indent-count:0.0000;punctuation-wrap:simple;punctuation-trim:leading;" ><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><font face="Times New Roman" >1. </font><font face="仿宋" >本协议的订立、执行和解释及争议的解决均应适用中华人民共和国法律。</font></span><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal  style="margin-left:21.0000pt;mso-para-margin-left:0.0000gd;text-indent:21.0000pt;
 mso-char-indent-count:0.0000;punctuation-wrap:simple;punctuation-trim:leading;" ><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><font face="Times New Roman" >2. </font><font face="仿宋" >若因本协议产生任何争议，双方应本着友好协商的原则解决；协商不成的，任何一方均可向</font><font face="Times New Roman" >mTab</font><font face="仿宋" >所在地有管辖权的人民法院提起诉讼。</font></span><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal  style="margin-left:21.0000pt;mso-para-margin-left:0.0000gd;text-indent:21.0000pt;
 mso-char-indent-count:0.0000;punctuation-wrap:simple;punctuation-trim:leading;" ><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><o:p>&nbsp;</o:p></span></p><p class=MsoNormal  style="punctuation-wrap:simple;punctuation-trim:leading;" ><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><font face="仿宋" >九、联系方式</font></span><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal  style="margin-left:21.0000pt;mso-para-margin-left:0.0000gd;text-indent:21.0000pt;
 mso-char-indent-count:0.0000;punctuation-wrap:simple;punctuation-trim:leading;" ><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><font face="仿宋" >若用户对本协议有任何疑问，可通过以下方式与我们联系：</font></span><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal  style="margin-left:21.0000pt;mso-para-margin-left:0.0000gd;text-indent:21.0000pt;
 mso-char-indent-count:0.0000;punctuation-wrap:simple;punctuation-trim:leading;" ><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><font face="仿宋" >电子邮件：</font></span><span><a href="mailto:<EMAIL>" ><u><span class="15"  style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';color:rgb(0,0,255);
 text-decoration:underline;text-underline:single;" ><font face="Times New Roman" ><EMAIL></font></span></u></a></span><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal  style="punctuation-wrap:simple;punctuation-trim:leading;" ><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><o:p>&nbsp;</o:p></span></p><p class=MsoNormal  style="punctuation-wrap:simple;punctuation-trim:leading;" ><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><font face="仿宋" >十、附则</font></span><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal  style="margin-left:21.0000pt;mso-para-margin-left:0.0000gd;text-indent:21.0000pt;
 mso-char-indent-count:0.0000;punctuation-wrap:simple;punctuation-trim:leading;" ><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><font face="Times New Roman" >1. </font><font face="仿宋" >本协议自发布之日起生效，解释权归</font><font face="Times New Roman" >mTab</font><font face="仿宋" >所有。</font></span><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal  style="margin-left:21.0000pt;mso-para-margin-left:0.0000gd;text-indent:21.0000pt;
 mso-char-indent-count:0.0000;punctuation-wrap:simple;punctuation-trim:leading;" ><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><font face="Times New Roman" >2. </font><font face="仿宋" >本协议的部分条款因任何原因被裁定为无效或不可执行，不影响其他条款的效力。</font></span><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal  style="margin-left:21.0000pt;mso-para-margin-left:0.0000gd;text-indent:21.0000pt;
 mso-char-indent-count:0.0000;punctuation-wrap:simple;punctuation-trim:leading;" ><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><font face="Times New Roman" >3. </font><font face="仿宋" >用户通过暴力、技术手段或其他方式绕过本协议的签署流程以继续使用本服务的，</font><font face="Times New Roman" >mTab</font><font face="仿宋" >有权立即终止其服务并追究相关法律责任。</font></span><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p><p class=MsoNormal  style="punctuation-wrap:simple;punctuation-trim:leading;" ><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><o:p>&nbsp;</o:p></span></p><p class=MsoNormal  style="punctuation-wrap:simple;punctuation-trim:leading;" ><span style="mso-spacerun:'yes';font-family:仿宋;mso-ascii-font-family:'Times New Roman';
 mso-hansi-font-family:Calibri;mso-bidi-font-family:'Times New Roman';letter-spacing:-0.3000pt;
 font-size:16.0000pt;mso-font-kerning:1.0000pt;" ><font face="仿宋" >感谢您使用</font><font face="Times New Roman" >mTab</font><font face="仿宋" >新标签页内容管理系统！我们期待为您提供优质的服务。</font></span><span style="mso-spacerun:'yes';font-family:'Times New Roman';mso-fareast-font-family:仿宋;
 mso-hansi-font-family:Calibri;letter-spacing:-0.3000pt;font-size:16.0000pt;
 mso-font-kerning:1.0000pt;" ><o:p></o:p></span></p></div><!--EndFragment--></body></html>
