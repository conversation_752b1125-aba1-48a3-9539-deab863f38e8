INSERT INTO linkstore (name, src, url, type, size, create_time, hot, tips, domain, app, install_num) VALUES ('Bilibili', '/static/bilibili.png', 'https://bilibili.com', 'icon', '1x1', '2022-11-07 21:51:42', 0, 'Bilibili弹幕视频网站Acg网站', 'bilibili.com,www.bilibili.com', 0, 0);
INSERT INTO linkstore (name, src, url, type, size, create_time, hot, tips, domain, app, install_num) VALUES ('蓝易云', '/static/tsy.png', 'https://www.tsyvps.com/aff/IRYIGFMX', 'icon', '1x1', '2022-11-07 22:02:41', 0, '蓝易云-持证高性价比服务器', 'www.tsyvps.com,tsyvps.com', 0, 0);
INSERT INTO linkstore (name, src, url, type, size, create_time, hot, tips, domain, app, install_num) VALUES ('ImgUrl', '/static/imgurl.png', 'https://imgurl.ink', 'icon', '1x1', '2022-11-07 22:05:46', 0, 'ImgUrl图床，图片外链', 'imgurl.ink,www.imgurl.ink', 0, 0);
INSERT INTO linkstore (name, src, url, type, size, create_time, hot, tips, domain, app, install_num) VALUES ('微博', '/static/weibo.png', 'http://weibo.com/', 'icon', '1x1', '2022-11-07 23:37:22', 1, '微博-随时随地发现新鲜事', 'weibo.com,www.weibo.com', 0, 0);
INSERT INTO linkstore (name, src, url, type, size, create_time, hot, tips, domain, app, install_num) VALUES ('火山翻译', '/static/huoshanfanyi.png', 'https://translate.volcengine.com/translate', 'icon', '1x1', '2022-11-07 23:42:49', 1, '火山翻译-字节跳动旗下机器翻译品牌', 'translate.volcengine.com', 1, 1);
INSERT INTO linkstore (name, src, url, type, size, create_time, hot, tips, domain, app, install_num) VALUES ('腾讯云', '/static/tencentcloud.png', 'https://cloud.tencent.com/', 'icon', '1x1', '2022-11-10 16:25:51', 1, '腾讯云', 'cloud.tencent.com', 0, 0);
INSERT INTO linkstore (name, src, url, type, size, create_time, hot, tips, domain, app, install_num) VALUES ('阿里云', '/static/aliyun.svg', 'https://www.aliyun.com/', 'icon', '1x1', '2022-11-10 17:30:17', 1, '阿里云', 'www.aliyun.com,aliyun.com', 0, 0);
INSERT INTO linkstore (name, src, url, type, size, create_time, hot, tips, domain, app, install_num) VALUES ('腾讯视频', '/static/txsp.png', 'https://v.qq.com/channel/choice?channel_2022=1', 'icon', '1x1', '2022-12-19 19:34:45', 0, '腾讯视频', 'v.qq.com', 0, 0);
INSERT INTO linkstore (name, src, url, type, size, create_time, hot, tips, domain, app, install_num) VALUES ('记事本', '/static/note.png', '/noteApp', 'icon', '1x1', '2023-06-14 21:13:15', 1,'记事本App', '/noteApp', 1, 3);
INSERT INTO linkstore (name, src, url, type, size, create_time, hot, tips, domain, app, install_num) VALUES ('WebTerm', '/static/webTerm.svg', 'https://ssh.mtab.cc', 'icon', '1x1', '2023-06-14 21:13:15', 1,'在线SSH终端', 'ssh.mtab.cc', 1, 3);


INSERT INTO search_engine (id, name, icon, url, sort, create_time, status, tips) VALUES (1, '百度', '/static/searchEngine/baidu.svg', 'https://www.baidu.com/s?wd={1}', 0, '2024-01-14 22:12:18', 1, '中国领先的搜索引擎和互联网公司，提供全球最大的中文搜索引擎服务，同时涵盖在线地图、贴吧、知道等多个互');
INSERT INTO search_engine (id, name, icon, url, sort, create_time, status, tips) VALUES (3, '必应', '/static/searchEngine/bing.svg', 'https://www.bing.com/search?q={1}', 99, '2024-01-14 23:20:03', 1, '微软推出的搜索引擎，以直观的界面和优质搜索结果而闻名，提供全球范围内的多语言搜索服务');
INSERT INTO search_engine (id, name, icon, url, sort, create_time, status, tips) VALUES (4, 'Google', '/static/searchEngine/google.svg', 'https://www.google.com/search?q={1}', 98, '2024-01-14 23:20:21', 1, 'Google：全球最大的搜索引擎，以卓越的搜索算法、广告服务和多样化的产品而著称，成为互联网信息检索');
INSERT INTO search_engine (id, name, icon, url, sort, create_time, status, tips) VALUES (5, '搜狗', '/static/searchEngine/sougou.svg', 'https://www.sogou.com/web?query={1}', 0, '2024-01-14 23:20:46', 1, '中国领先的搜索引擎，致力于提供智能搜索和语音输入技术，以及多元化的互联网服务，深受用户喜爱');
INSERT INTO search_engine (id, name, icon, url, sort, create_time, status, tips) VALUES (6, '360', '/static/searchEngine/360.svg', 'https://www.so.com/s?q={1}', 0, '2024-01-14 23:21:07', 1, '中国知名搜索引擎，注重用户隐私安全，提供全面的搜索服务，涵盖网页、图片、新闻等多个领域，致力于用户友');
INSERT INTO search_engine (id, name, icon, url, sort, create_time, status, tips) VALUES (7, '开发者搜索', '/static/searchEngine/baidudev.png', 'https://kaifa.baidu.com/searchPage?module=SEARCH&wd={1}', 0, '2024-01-14 23:21:45', 1, '专注于技术文档、API 和开发者资源的搜索引擎，为开发者提供快速准确的技术信息检索服务，支持多种编程');
INSERT INTO search_engine (id, name, icon, url, sort, create_time, status, tips) VALUES (8, 'B站', '/static/searchEngine/bilibiliico.png', 'https://search.bilibili.com/all?vt=21160573&keyword={1}', 0, '2024-01-14 23:21:57', 1, '中国弹幕视频平台，以二次元文化为特色，提供丰富的动画、游戏、音乐等内容，用户可通过弹幕互动分享观感。');
INSERT INTO search_engine (id, name, icon, url, sort, create_time, status, tips) VALUES (9, '微博', '/static/searchEngine/weiboico.png', 'https://s.weibo.com/weibo?q={1}', 0, '2024-01-14 23:22:12', 1, '中国社交媒体平台，用户可以发布短文、图片和视频，关注他人并互动评论，是实时新闻、话题讨论和社交分享的');

INSERT INTO wallpaper (id, type, folder, mime, url, cover, create_time, name, sort) VALUES (1, 1, null, 0, null, null, '2024-02-22 12:29:21', '默认壁纸', 999);
INSERT INTO wallpaper (id, type, folder, mime, url, cover, create_time, name, sort) VALUES (2, 0, 1, 0, '/static/wallpaper/wallpaper-1.jpeg', '/static/wallpaper/m_wallpaper-1.jpeg', '2024-02-22 12:35:59', null, 999);
INSERT INTO wallpaper (id, type, folder, mime, url, cover, create_time, name, sort) VALUES (3, 0, 1, 0, '/static/wallpaper/wallpaper-2.jpeg', '/static/wallpaper/m_wallpaper-2.jpeg', '2024-02-22 12:36:27', null, 999);
INSERT INTO wallpaper (id, type, folder, mime, url, cover, create_time, name, sort) VALUES (4, 0, 1, 0, '/static/wallpaper/wallpaper-3.jpeg', '/static/wallpaper/m_wallpaper-3.jpeg', '2024-02-22 12:36:43', null, 999);
INSERT INTO wallpaper (id, type, folder, mime, url, cover, create_time, name, sort) VALUES (5, 0, 1, 0, '/static/wallpaper/wallpaper-4.jpeg', '/static/wallpaper/m_wallpaper-4.jpeg', '2024-02-22 12:36:52', null, 999);
INSERT INTO wallpaper (id, type, folder, mime, url, cover, create_time, name, sort) VALUES (6, 0, 1, 0, '/static/wallpaper/wallpaper-5.jpeg', '/static/wallpaper/m_wallpaper-5.jpeg', '2024-02-22 12:37:03', null, 999);
